/**
 * 验证码按钮组件
 * 
 * 功能特性：
 * - 获取验证码功能
 * - 倒计时功能
 * - 加载状态显示
 * - 完全的样式定制
 * - 响应式设计
 * - TypeScript 支持
 */

import VerificationCodeButton from './VerificationCodeButton.vue'

// 类型定义
export interface VerificationCodeButtonProps {
  // 基础配置
  disabled?: boolean
  loading?: boolean
  
  // 倒计时配置
  countdown?: number
  autoStart?: boolean
  
  // 文本配置
  text?: string
  loadingText?: string
  countdownFormat?: string
  
  // 样式配置
  width?: number | string
  height?: number | string
  backgroundColor?: string
  disabledBackgroundColor?: string
  textColor?: string
  disabledTextColor?: string
  fontSize?: number
  borderRadius?: number
  borderWidth?: number
  borderColor?: string
  borderStyle?: string
  
  // 其他配置
  customClass?: string
  customStyle?: Record<string, any>
}

export interface VerificationCodeButtonEvents {
  click: []
  countdownStart: [countdown: number]
  countdownEnd: []
  countdownTick: [remaining: number]
}

export interface VerificationCodeButtonState {
  isCounting: boolean
  isLoading: boolean
  remainingTime: number
  isDisabled: boolean
}

export interface VerificationCodeButtonMethods {
  startCountdown: () => void
  stopCountdown: () => void
  setLoading: (loading: boolean) => void
  getState: () => VerificationCodeButtonState
}

// 预设配置
export const verificationCodeButtonPresets = {
  // 默认配置
  defaults: {
    countdown: 60,
    text: '获取验证码',
    loadingText: '发送中...',
    countdownFormat: '{time}s后重新获取',
    width: 'auto',
    height: 36,
    backgroundColor: '#007aff',
    textColor: '#ffffff',
    fontSize: 14,
    borderRadius: 4
  },

  // 样式主题
  themes: {
    // 默认主题（蓝色）
    default: {
      backgroundColor: '#007aff',
      disabledBackgroundColor: '#c8c9cc',
      textColor: '#ffffff',
      disabledTextColor: '#969799'
    },
    
    // 成功主题（绿色）
    success: {
      backgroundColor: '#07c160',
      disabledBackgroundColor: '#c8c9cc',
      textColor: '#ffffff',
      disabledTextColor: '#969799'
    },
    
    // 警告主题（橙色）
    warning: {
      backgroundColor: '#ff976a',
      disabledBackgroundColor: '#c8c9cc',
      textColor: '#ffffff',
      disabledTextColor: '#969799'
    },
    
    // 危险主题（红色）
    danger: {
      backgroundColor: '#ee0a24',
      disabledBackgroundColor: '#c8c9cc',
      textColor: '#ffffff',
      disabledTextColor: '#969799'
    },
    
    // 朴素主题（白色边框）
    plain: {
      backgroundColor: 'transparent',
      disabledBackgroundColor: 'transparent',
      textColor: '#007aff',
      disabledTextColor: '#c8c9cc',
      borderWidth: 1,
      borderColor: '#007aff'
    },
    
    // 暗色主题
    dark: {
      backgroundColor: '#1f2937',
      disabledBackgroundColor: '#4b5563',
      textColor: '#ffffff',
      disabledTextColor: '#9ca3af'
    }
  },

  // 尺寸预设
  sizes: {
    // 小尺寸
    small: {
      height: 28,
      fontSize: 12,
      borderRadius: 3
    },
    
    // 默认尺寸
    default: {
      height: 36,
      fontSize: 14,
      borderRadius: 4
    },
    
    // 大尺寸
    large: {
      height: 44,
      fontSize: 16,
      borderRadius: 6
    },
    
    // 超大尺寸
    xlarge: {
      height: 52,
      fontSize: 18,
      borderRadius: 8
    }
  },

  // 使用场景配置
  scenarios: {
    // 登录场景
    login: {
      text: '获取验证码',
      countdown: 60,
      countdownFormat: '{time}s后重新获取'
    },
    
    // 注册场景
    register: {
      text: '发送验证码',
      countdown: 60,
      countdownFormat: '重新发送({time}s)'
    },
    
    // 找回密码场景
    resetPassword: {
      text: '获取验证码',
      countdown: 120,
      countdownFormat: '{time}s后重新获取'
    },
    
    // 绑定手机场景
    bindPhone: {
      text: '发送验证码',
      countdown: 60,
      countdownFormat: '{time}s后重新发送'
    },
    
    // 修改密码场景
    changePassword: {
      text: '获取验证码',
      countdown: 60,
      countdownFormat: '重新获取({time}s)'
    }
  }
}

// 工具函数
export const verificationCodeButtonUtils = {
  /**
   * 创建预设配置
   */
  createPreset(
    theme: keyof typeof verificationCodeButtonPresets.themes = 'default',
    size: keyof typeof verificationCodeButtonPresets.sizes = 'default',
    scenario: keyof typeof verificationCodeButtonPresets.scenarios = 'login'
  ): VerificationCodeButtonProps {
    return {
      ...verificationCodeButtonPresets.defaults,
      ...verificationCodeButtonPresets.themes[theme],
      ...verificationCodeButtonPresets.sizes[size],
      ...verificationCodeButtonPresets.scenarios[scenario]
    }
  },

  /**
   * 验证手机号格式
   */
  validatePhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  /**
   * 验证邮箱格式
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 格式化倒计时文本
   */
  formatCountdown(time: number, format: string = '{time}s后重新获取'): string {
    return format.replace('{time}', time.toString())
  },

  /**
   * 创建异步验证码获取函数
   */
  createAsyncHandler(
    apiCall: () => Promise<any>,
    options: {
      onStart?: () => void
      onSuccess?: (result: any) => void
      onError?: (error: any) => void
      onFinally?: () => void
    } = {}
  ) {
    return async (buttonRef: any) => {
      try {
        options.onStart?.()
        buttonRef?.setLoading(true)
        
        const result = await apiCall()
        
        options.onSuccess?.(result)
        buttonRef?.startCountdown()
      } catch (error) {
        options.onError?.(error)
      } finally {
        buttonRef?.setLoading(false)
        options.onFinally?.()
      }
    }
  }
}

// 常量定义
export const VERIFICATION_CODE_BUTTON_CONSTANTS = {
  // 默认倒计时时间
  DEFAULT_COUNTDOWN: 60,
  
  // 最小倒计时时间
  MIN_COUNTDOWN: 10,
  
  // 最大倒计时时间
  MAX_COUNTDOWN: 300,
  
  // 默认文本
  DEFAULT_TEXTS: {
    NORMAL: '获取验证码',
    LOADING: '发送中...',
    COUNTDOWN: '{time}s后重新获取'
  },
  
  // 事件名称
  EVENTS: {
    CLICK: 'click',
    COUNTDOWN_START: 'countdownStart',
    COUNTDOWN_END: 'countdownEnd',
    COUNTDOWN_TICK: 'countdownTick'
  } as const,
  
  // 状态
  STATES: {
    NORMAL: 'normal',
    LOADING: 'loading',
    COUNTING: 'counting',
    DISABLED: 'disabled'
  } as const
}

// 组件导出
export { VerificationCodeButton }
export default VerificationCodeButton
