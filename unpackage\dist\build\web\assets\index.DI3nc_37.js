import{y as t,z as e,A as a,X as o,D as l,Y as r,o as s,c as n,w as c,a as i,j as u,E as f,b as d,t as g,x as h,Z as p,m as y,$ as v,i as x,e as b,a0 as k,a1 as C}from"./index-ChUEiI3E.js";import{_ as B}from"./_plugin-vue_export-helper.BCo6x5W8.js";const m=B(t({__name:"CustomNavBar",props:{title:{default:""},showBack:{type:Boolean,default:!0},backText:{default:""},rightText:{default:""},rightIcon:{default:""},backgroundColor:{default:"#ffffff"},textColor:{default:"#333333"},iconColor:{default:"#333333"},titleSize:{default:18},titleWeight:{default:"bold"},showBorder:{type:Boolean,default:!0},borderColor:{default:"#e5e5e5"},fixed:{type:Boolean,default:!0},zIndex:{default:999},transparent:{type:Boolean,default:!1},safeAreaInsetTop:{type:Boolean,default:!0}},emits:["leftClick","rightClick","back"],setup(t,{expose:k,emit:C}){const B=t,m=C,_=e(0),I=e(44),T=e(null);a((()=>{z()}));const z=()=>{try{const t=o();T.value=t,_.value=t.statusBarHeight||0,"ios"===t.platform?I.value=44:"android"===t.platform?I.value=48:I.value=44,_.value=0,I.value=44}catch(t){console.error("获取系统信息失败:",t),_.value=20,I.value=44}},H=l((()=>{const t={position:B.fixed?"fixed":"relative",top:0,left:0,right:0,zIndex:B.zIndex};return B.transparent||(t.backgroundColor=B.backgroundColor),B.safeAreaInsetTop&&(t.paddingTop="env(safe-area-inset-top)"),t})),w=l((()=>({color:B.textColor,fontSize:B.titleSize+"px",fontWeight:B.titleWeight}))),S=l((()=>({backgroundColor:B.borderColor}))),A=l((()=>_.value+I.value)),N=()=>{if(m("leftClick"),B.showBack){m("back");p().length>1?y():v({url:"/pages/index/index"})}},$=()=>{m("rightClick")};return k({statusBarHeight:_,navBarHeight:I,totalHeight:A,systemInfo:T}),(t,e)=>{const a=x,o=r("uni-icons"),l=b;return s(),n(a,{class:"custom-navbar",style:u(H.value)},{default:c((()=>[i(a,{class:"status-bar",style:u({height:_.value+"px"})},null,8,["style"]),i(a,{class:"navbar-content",style:u({height:I.value+"px"})},{default:c((()=>[i(a,{class:"navbar-left",onClick:N},{default:c((()=>[f(t.$slots,"left",{},(()=>[t.showBack?(s(),n(a,{key:0,class:"back-button"},{default:c((()=>[i(o,{type:"left",size:"20",color:t.iconColor},null,8,["color"]),t.backText?(s(),n(l,{key:0,class:"back-text",style:u({color:t.textColor})},{default:c((()=>[d(g(t.backText),1)])),_:1},8,["style"])):h("",!0)])),_:1})):h("",!0)]),!0)])),_:3}),i(a,{class:"navbar-center"},{default:c((()=>[f(t.$slots,"center",{},(()=>[i(l,{class:"navbar-title",style:u(w.value)},{default:c((()=>[d(g(t.title),1)])),_:1},8,["style"])]),!0)])),_:3}),i(a,{class:"navbar-right",onClick:$},{default:c((()=>[f(t.$slots,"right",{},(()=>[t.rightText?(s(),n(a,{key:0,class:"right-text",style:u({color:t.textColor})},{default:c((()=>[d(g(t.rightText),1)])),_:1},8,["style"])):h("",!0),t.rightIcon?(s(),n(a,{key:1,class:"right-icon"},{default:c((()=>[i(o,{type:t.rightIcon,size:"20",color:t.iconColor},null,8,["type","color"])])),_:1})):h("",!0)]),!0)])),_:3})])),_:3},8,["style"]),t.showBorder?(s(),n(a,{key:0,class:"navbar-border",style:u(S.value)},null,8,["style"])):h("",!0)])),_:3},8,["style"])}}}),[["__scopeId","data-v-772e10fc"]]),_={getTotalHeight(){try{const t=o();t.statusBarHeight;let e=44;return"android"===t.platform&&(e=48),e}catch(t){return console.error("获取导航栏高度失败:",t),64}},getStatusBarHeight(){try{o();return 0}catch(t){return console.error("获取状态栏高度失败:",t),20}},getNavBarHeight(){try{const t=o();let e=44;return"android"===t.platform&&(e=48),e}catch(t){return console.error("获取导航栏高度失败:",t),44}},needSafeArea(){var t;try{return((null==(t=o().safeAreaInsets)?void 0:t.top)??0)>0}catch(e){return!1}},getMenuButtonInfo(){try{return null}catch(t){return console.error("获取胶囊按钮信息失败:",t),null}},setNavigationBarStyle(t){try{k({frontColor:t.frontColor||"#000000",backgroundColor:t.backgroundColor||"#ffffff",animation:!1===t.animation?void 0:{duration:300},success:()=>{console.log("设置导航栏样式成功")},fail:t=>{console.error("设置导航栏样式失败:",t)}}),t.title&&C({title:t.title})}catch(e){console.error("设置导航栏样式失败:",e)}},hideNavigationBar(){}};export{m as C,_ as n};
