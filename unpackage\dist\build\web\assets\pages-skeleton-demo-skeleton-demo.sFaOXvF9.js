import{y as a,z as e,A as l,o as t,c as s,w as d,a as i,B as c,b as o,k as u,r as n,F as m,x as _,t as r,j as f,m as p,s as v,e as g,C as y,i as b,f as k,d as x,S as j,l as h}from"./index-ChUEiI3E.js";import{_ as w,S as C,a as F}from"./index.B_fb1lzg.js";import{n as I,C as T}from"./index.DI3nc_37.js";/* empty css                                                                 */import"./index.BAv8G1Ca.js";import"./index.BbQFgwO5.js";import"./index.4kChqqAX.js";import"./index.Dvz5gvUX.js";import"./index._qKjp6eZ.js";/* empty css                                                                         *//* empty css                                                                      *//* empty css                                                                               */import{_ as A}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=A(a({__name:"skeleton-demo",setup(a){const A=e(0),B=e(!0),P=e(!0),S=e("article"),U=[{key:"article",name:"文章"},{key:"card",name:"卡片"},{key:"list",name:"列表"},{key:"profile",name:"用户"},{key:"table",name:"表格"}],V=e([{id:1,name:"张三",description:"前端开发工程师",avatar:"/static/avatar1.png"},{id:2,name:"李四",description:"后端开发工程师",avatar:"/static/avatar2.png"},{id:3,name:"王五",description:"UI/UX 设计师",avatar:"/static/avatar3.png"},{id:4,name:"赵六",description:"产品经理",avatar:"/static/avatar4.png"}]),z=e([{id:1,name:"张三",age:28,job:"前端工程师",city:"北京"},{id:2,name:"李四",age:32,job:"后端工程师",city:"上海"},{id:3,name:"王五",age:26,job:"UI设计师",city:"深圳"},{id:4,name:"赵六",age:30,job:"产品经理",city:"杭州"},{id:5,name:"钱七",age:29,job:"测试工程师",city:"广州"}]);l((()=>{A.value=I.getTotalHeight(),setTimeout((()=>{B.value=!1}),3e3)}));const q=()=>{p()},H=a=>{B.value=a.detail.value},X=a=>{P.value=a.detail.value},D=()=>{B.value=!B.value,B.value&&setTimeout((()=>{B.value=!1}),2e3)},E=()=>{v({title:"主题切换功能待实现",icon:"none"})};return(a,e)=>{const l=g,p=y,v=b,I=k,G=x,J=j;return t(),s(v,{class:"page"},{default:d((()=>[i(c(T),{title:"骨架屏演示","show-back":!0,onBack:q}),i(J,{"scroll-y":"",class:"content",style:f({paddingTop:A.value+"px"})},{default:d((()=>[i(v,{class:"control-panel"},{default:d((()=>[i(l,{class:"panel-title"},{default:d((()=>[o("🎛️ 控制面板")])),_:1}),i(v,{class:"control-group"},{default:d((()=>[i(l,{class:"control-label"},{default:d((()=>[o("显示骨架屏:")])),_:1}),i(p,{checked:B.value,onChange:H,color:"#007aff"},null,8,["checked"])])),_:1}),i(v,{class:"control-group"},{default:d((()=>[i(l,{class:"control-label"},{default:d((()=>[o("启用动画:")])),_:1}),i(p,{checked:P.value,onChange:X,color:"#007aff"},null,8,["checked"])])),_:1}),i(v,{class:"template-buttons"},{default:d((()=>[(t(),u(m,null,n(U,(a=>i(I,{key:a.key,onClick:e=>(a=>{S.value=a,B.value=!0,setTimeout((()=>{B.value=!1}),2e3)})(a.key),class:h(["template-btn",{active:S.value===a.key}])},{default:d((()=>[o(r(a.name),1)])),_:2},1032,["onClick","class"]))),64))])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:d((()=>[i(l,{class:"section-title"},{default:d((()=>[o("📱 预设模板演示")])),_:1}),"article"===S.value?(t(),s(v,{key:0,class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("文章模板")])),_:1}),i(c(C),{loading:B.value,template:"article",animated:P.value},{default:d((()=>[i(v,{class:"article-content"},{default:d((()=>[i(l,{class:"article-title"},{default:d((()=>[o("Vue 3 Composition API 深度解析")])),_:1}),i(v,{class:"article-meta"},{default:d((()=>[i(G,{class:"author-avatar",src:w,mode:"aspectFill"}),i(l,{class:"author-name"},{default:d((()=>[o("张三")])),_:1}),i(l,{class:"publish-time"},{default:d((()=>[o("2024-01-15")])),_:1})])),_:1}),i(l,{class:"article-text"},{default:d((()=>[o(" Vue 3 的 Composition API 为我们提供了更灵活的组件逻辑组织方式， 通过 setup 函数，我们可以更好地复用逻辑，提高代码的可维护性... ")])),_:1})])),_:1})])),_:1},8,["loading","animated"])])),_:1})):_("",!0),"card"===S.value?(t(),s(v,{key:1,class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("卡片模板")])),_:1}),i(c(C),{loading:B.value,template:"card",animated:P.value},{default:d((()=>[i(v,{class:"card-content"},{default:d((()=>[i(G,{class:"card-image",src:"/static/demo-image.jpg",mode:"aspectFill"}),i(l,{class:"card-title"},{default:d((()=>[o("美丽的风景")])),_:1}),i(l,{class:"card-description"},{default:d((()=>[o(" 这是一张美丽的风景图片，展示了大自然的壮丽景色。 ")])),_:1})])),_:1})])),_:1},8,["loading","animated"])])),_:1})):_("",!0),"list"===S.value?(t(),s(v,{key:2,class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("列表模板")])),_:1}),i(c(C),{loading:B.value,template:"list","list-rows":4,animated:P.value},{default:d((()=>[i(v,{class:"list-content"},{default:d((()=>[(t(!0),u(m,null,n(V.value,(a=>(t(),s(v,{key:a.id,class:"list-item"},{default:d((()=>[i(G,{class:"item-avatar",src:a.avatar,mode:"aspectFill"},null,8,["src"]),i(v,{class:"item-info"},{default:d((()=>[i(l,{class:"item-name"},{default:d((()=>[o(r(a.name),1)])),_:2},1024),i(l,{class:"item-desc"},{default:d((()=>[o(r(a.description),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1},8,["loading","animated"])])),_:1})):_("",!0),"profile"===S.value?(t(),s(v,{key:3,class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("用户信息模板")])),_:1}),i(c(C),{loading:B.value,template:"profile",animated:P.value},{default:d((()=>[i(v,{class:"profile-content"},{default:d((()=>[i(G,{class:"profile-avatar",src:"/static/user-avatar.png",mode:"aspectFill"}),i(v,{class:"profile-info"},{default:d((()=>[i(l,{class:"profile-name"},{default:d((()=>[o("李四")])),_:1}),i(l,{class:"profile-email"},{default:d((()=>[o("<EMAIL>")])),_:1}),i(l,{class:"profile-bio"},{default:d((()=>[o("前端开发工程师，热爱技术分享")])),_:1})])),_:1})])),_:1})])),_:1},8,["loading","animated"])])),_:1})):_("",!0),"table"===S.value?(t(),s(v,{key:4,class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("表格模板")])),_:1}),i(c(C),{loading:B.value,template:"table","table-rows":5,animated:P.value},{default:d((()=>[i(v,{class:"table-content"},{default:d((()=>[i(v,{class:"table-header"},{default:d((()=>[i(l,{class:"table-cell"},{default:d((()=>[o("姓名")])),_:1}),i(l,{class:"table-cell"},{default:d((()=>[o("年龄")])),_:1}),i(l,{class:"table-cell"},{default:d((()=>[o("职业")])),_:1}),i(l,{class:"table-cell"},{default:d((()=>[o("城市")])),_:1})])),_:1}),(t(!0),u(m,null,n(z.value,(a=>(t(),s(v,{key:a.id,class:"table-row"},{default:d((()=>[i(l,{class:"table-cell"},{default:d((()=>[o(r(a.name),1)])),_:2},1024),i(l,{class:"table-cell"},{default:d((()=>[o(r(a.age),1)])),_:2},1024),i(l,{class:"table-cell"},{default:d((()=>[o(r(a.job),1)])),_:2},1024),i(l,{class:"table-cell"},{default:d((()=>[o(r(a.city),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1},8,["loading","animated"])])),_:1})):_("",!0)])),_:1}),i(v,{class:"demo-section"},{default:d((()=>[i(l,{class:"section-title"},{default:d((()=>[o("🔧 自定义骨架屏")])),_:1}),i(v,{class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("基础组合")])),_:1}),i(c(C),{loading:B.value,avatar:!0,title:!0,paragraph:!0,"paragraph-rows":2,animated:P.value},{default:d((()=>[i(v,{class:"custom-content"},{default:d((()=>[i(G,{class:"custom-avatar",src:w,mode:"aspectFill"}),i(v,{class:"custom-info"},{default:d((()=>[i(l,{class:"custom-title"},{default:d((()=>[o("自定义内容标题")])),_:1}),i(l,{class:"custom-text"},{default:d((()=>[o("这是自定义的内容文本，展示了骨架屏的基础组合效果。")])),_:1})])),_:1})])),_:1})])),_:1},8,["loading","animated"])])),_:1}),i(v,{class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("单独元素")])),_:1}),i(v,{class:"skeleton-items"},{default:d((()=>[i(v,{class:"skeleton-row"},{default:d((()=>[i(l,{class:"item-label"},{default:d((()=>[o("头像:")])),_:1}),i(c(F),{loading:B.value,type:"avatar",animated:P.value},{default:d((()=>[i(G,{class:"demo-avatar",src:w,mode:"aspectFill"})])),_:1},8,["loading","animated"])])),_:1}),i(v,{class:"skeleton-row"},{default:d((()=>[i(l,{class:"item-label"},{default:d((()=>[o("文本:")])),_:1}),i(c(F),{loading:B.value,type:"text",width:"200px",animated:P.value},{default:d((()=>[i(l,{class:"demo-text"},{default:d((()=>[o("这是一段文本内容")])),_:1})])),_:1},8,["loading","animated"])])),_:1}),i(v,{class:"skeleton-row"},{default:d((()=>[i(l,{class:"item-label"},{default:d((()=>[o("按钮:")])),_:1}),i(c(F),{loading:B.value,type:"button",width:"100px",animated:P.value},{default:d((()=>[i(I,{class:"demo-button"},{default:d((()=>[o("点击按钮")])),_:1})])),_:1},8,["loading","animated"])])),_:1}),i(v,{class:"skeleton-row"},{default:d((()=>[i(l,{class:"item-label"},{default:d((()=>[o("图片:")])),_:1}),i(c(F),{loading:B.value,type:"image",width:"150px",height:"100px",animated:P.value},{default:d((()=>[i(G,{class:"demo-image",src:"/static/demo-image.jpg",mode:"aspectFill"})])),_:1},8,["loading","animated"])])),_:1})])),_:1})])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:d((()=>[i(l,{class:"section-title"},{default:d((()=>[o("✨ 动画效果")])),_:1}),i(v,{class:"demo-card"},{default:d((()=>[i(l,{class:"demo-title"},{default:d((()=>[o("错开动画")])),_:1}),i(v,{class:"staggered-animation"},{default:d((()=>[(t(),u(m,null,n(5,((a,e)=>i(c(F),{key:e,loading:B.value,type:"text","animation-delay":.1*e,animated:P.value},{default:d((()=>[i(l,{class:"staggered-text"},{default:d((()=>[o("动画文本 "+r(e+1),1)])),_:2},1024)])),_:2},1032,["loading","animation-delay","animated"]))),64))])),_:1})])),_:1})])),_:1}),i(v,{class:"action-buttons"},{default:d((()=>[i(I,{onClick:D,class:"action-btn primary"},{default:d((()=>[o(r(B.value?"加载完成":"重新加载"),1)])),_:1}),i(I,{onClick:E,class:"action-btn secondary"},{default:d((()=>[o(" 切换主题 ")])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}}}),[["__scopeId","data-v-afdb545b"]]);export{B as default};
