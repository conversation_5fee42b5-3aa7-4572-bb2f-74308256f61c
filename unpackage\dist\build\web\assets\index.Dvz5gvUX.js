import"./index-ChUEiI3E.js";const e={createConfig:(e={})=>({time:0,endTime:void 0,startTime:void 0,format:"HH:mm:ss",showDays:!0,showHours:!0,showMinutes:!0,showSeconds:!0,showMilliseconds:!1,showLabels:!1,showSeparator:!0,dayLabel:"天",hourLabel:"时",minuteLabel:"分",secondLabel:"秒",millisecondLabel:"毫秒",separator:":",size:32,color:"#333333",backgroundColor:"transparent",borderColor:"transparent",borderRadius:4,valueColor:"#333333",valueSize:32,valueBold:!0,labelColor:"#666666",labelSize:24,separatorColor:"#666666",separatorSize:28,autoStart:!0,precision:1e3,customClass:"",customStyle:{},...e}),createSeckillConfig(e){return this.createConfig({endTime:e,showDays:!1,showHours:!0,showMinutes:!0,showSeconds:!0,showMilliseconds:!1,backgroundColor:"#ff3b30",valueColor:"#ffffff",borderRadius:8,valueBold:!0})},createActivityConfig(e){return this.createConfig({endTime:e,showDays:!0,showHours:!0,showMinutes:!0,showSeconds:!0,showLabels:!0,backgroundColor:"#007aff",valueColor:"#ffffff",labelColor:"#ffffff",borderRadius:12})},createExamConfig(e){return this.createConfig({time:e,showDays:!1,showHours:!0,showMinutes:!0,showSeconds:!0,showMilliseconds:!1,backgroundColor:"#ff9500",valueColor:"#ffffff",borderRadius:6,valueSize:36,valueBold:!0})},createVerifyCodeConfig(e=6e4){return this.createConfig({time:e,showDays:!1,showHours:!1,showMinutes:!1,showSeconds:!0,showMilliseconds:!1,showSeparator:!1,backgroundColor:"transparent",valueColor:"#007aff",valueSize:28,valueBold:!1})},createPreciseConfig(e){return this.createConfig({time:e,showDays:!1,showHours:!1,showMinutes:!0,showSeconds:!0,showMilliseconds:!0,precision:100,backgroundColor:"#4cd964",valueColor:"#ffffff",borderRadius:4})},parseTime(e){if("number"==typeof e)return e;if("string"==typeof e){const o=Date.parse(e);if(!isNaN(o))return o;const r=Number(e);if(!isNaN(r))return r}return e instanceof Date?e.getTime():0},formatTime(e,o="HH:mm:ss"){const r=Math.floor(e/864e5),a=Math.floor(e%864e5/36e5),t=Math.floor(e%36e5/6e4),s=Math.floor(e%6e4/1e3),i=e%1e3;return o.replace(/DD/g,r.toString().padStart(2,"0")).replace(/D/g,r.toString()).replace(/HH/g,a.toString().padStart(2,"0")).replace(/H/g,a.toString()).replace(/mm/g,t.toString().padStart(2,"0")).replace(/m/g,t.toString()).replace(/ss/g,s.toString().padStart(2,"0")).replace(/s/g,s.toString()).replace(/SSS/g,i.toString().padStart(3,"0")).replace(/SS/g,Math.floor(i/10).toString().padStart(2,"0")).replace(/S/g,Math.floor(i/100).toString())},calculateTimeDiff(e,o){const r=this.parseTime(e),a=o?this.parseTime(o):Date.now();return Math.max(0,r-a)},validateTime(e){if("number"==typeof e)return!isNaN(e)&&e>=0;if("string"==typeof e){const o=Date.parse(e);return!isNaN(o)}return e instanceof Date&&!isNaN(e.getTime())},createTimeData:e=>({days:Math.floor(e/864e5),hours:Math.floor(e%864e5/36e5),minutes:Math.floor(e%36e5/6e4),seconds:Math.floor(e%6e4/1e3),milliseconds:e%1e3,total:e}),getThemeColors(){const e=this.isDarkTheme();return{backgroundColor:e?"#2a2a2a":"#ffffff",borderColor:e?"#444444":"#e5e5e5",valueColor:e?"#ffffff":"#333333",labelColor:e?"#cccccc":"#666666",separatorColor:e?"#cccccc":"#666666"}},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches};e.createConfig(),e.createSeckillConfig(Date.now()+36e5),e.createActivityConfig(Date.now()+864e5),e.createExamConfig(72e5),e.createVerifyCodeConfig(6e4),e.createPreciseConfig(3e5);
