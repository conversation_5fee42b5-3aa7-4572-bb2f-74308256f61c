import{y as e,z as l,D as a,H as o,A as t,o as s,c as u,w as c,j as n,a as r,B as i,x as d,b as v,k as h,F as f,r as p,t as g,_ as m,M as y,s as _,I as b,i as C,e as w,S as x,f as k}from"./index-ChUEiI3E.js";import{n as A,C as S}from"./index.DI3nc_37.js";import"./index._qKjp6eZ.js";import{C as H}from"./CascadeSelection.D80cIVcX.js";import{I as T}from"./Icon.DLnR9vuk.js";/* empty css                                                                 *//* empty css                                                                      */import{_ as U}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{B as j}from"./BottomPopup.gFUAg0m5.js";/* empty css                                                                         */function E(e,l){if(!l||0===l.length)return null;let a,o,t;if(l[0]&&(a=e.find((e=>e.value===l[0]))),l[1]&&(null==a?void 0:a.children)&&(o=a.children.find((e=>e.value===l[1]))),l[2]&&(null==o?void 0:o.children)&&(t=o.children.find((e=>e.value===l[2]))),!a)return null;const s={province:a.label||"",provinceCode:a.value,city:(null==o?void 0:o.label)||"",cityCode:(null==o?void 0:o.value)||0,district:(null==t?void 0:t.label)||"",districtCode:(null==t?void 0:t.value)||0,fullAddress:""},u=[s.province];return s.city&&s.city!==s.province&&u.push(s.city),s.district&&u.push(s.district),s.fullAddress=u.join(""),s}function P(e,l){if(!l.trim())return[];const a=[],o=l.toLowerCase();return e.forEach((e=>{var l,t;(null==(l=e.label)?void 0:l.toLowerCase().includes(o))&&a.push({...e,searchType:"province"}),null==(t=e.children)||t.forEach((l=>{var t,s;(null==(t=l.label)?void 0:t.toLowerCase().includes(o))&&a.push({...l,provinceName:e.label,searchType:"city"}),null==(s=l.children)||s.forEach((t=>{var s;(null==(s=t.label)?void 0:s.toLowerCase().includes(o))&&a.push({...t,provinceName:e.label,cityName:l.label,searchType:"district"})}))}))})),a.slice(0,50)}const B=U(e({__name:"PickerAddress",props:{value:{default:()=>[]},defaultValue:{default:()=>[]},title:{default:"选择地区"},placeholder:{default:"请选择"},emptyText:{default:"暂无数据"},searchPlaceholder:{default:"搜索城市"},showSearch:{type:Boolean,default:!0},showHotCities:{type:Boolean,default:!0},showClose:{type:Boolean,default:!1},showFooter:{type:Boolean,default:!0},showActiveIndicator:{type:Boolean,default:!0},height:{default:"60vh"},cascadeHeight:{default:"auto"},itemHeight:{default:44},backgroundColor:{default:"#ffffff"},activeColor:{default:"#007aff"},textColor:{default:"#333333"},borderColor:{default:"#e5e5e5"},confirmText:{default:"确定"},cancelText:{default:"取消"},customClass:{default:""},customStyle:{default:()=>({})}},emits:["update:value","select","change","confirm","cancel","close","search"],setup(e,{expose:k,emit:A}){const S=e,U=A,j=l([]),B=l([]),I=l(""),N=l([]),V=l([]),D=l(!1),$=l(!1),L=a((()=>({height:"number"==typeof S.height?`${S.height}px`:S.height,backgroundColor:S.backgroundColor,...S.customStyle}))),R=a((()=>({borderBottomColor:S.borderColor}))),F=a((()=>{const e=S.showSearch?50:0;return{height:("number"==typeof S.height?S.height:400)-e+"px"}})),M=a((()=>({borderBottomColor:S.borderColor}))),z=a((()=>({borderBottomColor:S.borderColor}))),O=a((()=>({borderColor:S.borderColor,color:S.textColor}))),q=a((()=>{const e=S.showSearch?50:0,l=S.showHotCities&&V.value.length>0?120:0;let a=400;if("number"==typeof S.height)a=S.height;else if("string"==typeof S.height)if(S.height.includes("vh")){const e=parseFloat(S.height.replace("vh",""));a=window.innerHeight*e/100}else a=S.height.includes("px")?parseFloat(S.height.replace("px","")):parseFloat(S.height)||400;const o=Math.max(200,a-e-l);return console.log("级联容器样式计算:",{totalHeight:a,searchBarHeight:e,hotCitiesHeight:l,cascadeHeight:o,cascadeHeightProp:S.cascadeHeight}),{height:"auto"===S.cascadeHeight?`${o}px`:S.cascadeHeight,minHeight:"200px",overflow:"hidden"}})),J=a((()=>E(j.value,B.value))),K=async()=>{try{console.log("开始加载城市数据...");let l=null;try{const e=await m((()=>import("./components-PickerAddress-city.DJ-qJrMv.js")),[]);l=e.default||e,console.log("动态导入成功")}catch(e){console.warn("动态导入失败，尝试备用方法:",e),l=await(async()=>{try{console.log("使用备用方法加载城市数据...");const e=await fetch("/components/PickerAddress/city.json");if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const l=await e.json();return console.log("备用方法加载的数据:",l),l}catch(e){return console.error("备用数据加载方法也失败:",e),console.log("使用最小测试数据"),[{value:11e4,text:"北京市",level:1,pid:0,children:[{value:110100,text:"北京市市辖区",level:2,pid:11e4,children:[{value:110101,text:"东城区",level:3,pid:110100,children:[]},{value:110102,text:"西城区",level:3,pid:110100,children:[]},{value:110105,text:"朝阳区",level:3,pid:110100,children:[]},{value:110106,text:"丰台区",level:3,pid:110100,children:[]}]}]},{value:12e4,text:"天津市",level:1,pid:0,children:[{value:120100,text:"天津市市辖区",level:2,pid:12e4,children:[{value:120101,text:"和平区",level:3,pid:120100,children:[]},{value:120102,text:"河东区",level:3,pid:120100,children:[]},{value:120103,text:"河西区",level:3,pid:120100,children:[]},{value:120104,text:"南开区",level:3,pid:120100,children:[]}]}]},{value:31e4,text:"上海市",level:1,pid:0,children:[{value:310100,text:"上海市市辖区",level:2,pid:31e4,children:[{value:310101,text:"黄浦区",level:3,pid:310100,children:[]},{value:310104,text:"徐汇区",level:3,pid:310100,children:[]},{value:310105,text:"长宁区",level:3,pid:310100,children:[]},{value:310106,text:"静安区",level:3,pid:310100,children:[]}]}]}]}})()}if(console.log("原始城市数据类型:",typeof l),console.log("是否为数组:",Array.isArray(l)),!l)throw new Error("城市数据为空");let a=l;if(!Array.isArray(l))throw new Error("城市数据格式不正确，应该是数组格式");console.log("城市数据数组长度:",a.length),a.length>0&&console.log("第一个省份数据:",a[0]),function(e){try{if(!Array.isArray(e)||0===e.length)return console.warn("数据不是数组或为空"),!1;const l=Math.min(3,e.length);for(let a=0;a<l;a++){const l=e[a];if(!l||"object"!=typeof l)return console.warn(`省份数据 ${a} 不是对象:`,l),!1;if(!l.text||!l.value)return console.warn(`省份数据 ${a} 缺少必要字段:`,l),!1;if(l.children&&Array.isArray(l.children)&&l.children.length>0){const e=l.children[0];if(!e.text||!e.value)return console.warn("城市数据格式错误:",e),!1;if(e.children&&Array.isArray(e.children)&&e.children.length>0){const l=e.children[0];if(!l.text||!l.value)return console.warn("区县数据格式错误:",l),!1}}}return console.log("数据验证通过"),!0}catch(l){return console.error("数据验证出错:",l),!1}}(a)||console.warn("城市数据验证失败，但继续处理"),console.log("开始转换数据格式...");const o=a.map((e=>{var l;return{label:e.text,value:e.value,level:e.level,pid:e.pid,children:(null==(l=e.children)?void 0:l.map((e=>{var l;return{label:e.text,value:e.value,level:e.level,pid:e.pid,children:(null==(l=e.children)?void 0:l.map((e=>({label:e.text,value:e.value,level:e.level,pid:e.pid,children:[]}))))||[]}})))||[]}}));if(console.log("转换后数据长度:",o.length),0===o.length)throw new Error("转换后的数据为空");j.value=o,S.showHotCities&&(console.log("获取热门城市..."),V.value=function(e){const l=["北京市","上海市","广州市","深圳市","杭州市","南京市","武汉市","成都市","西安市","重庆市","天津市","苏州市","长沙市","郑州市","青岛市","大连市","宁波市","厦门市"],a=[];return e.forEach((e=>{var o;null==(o=e.children)||o.forEach((o=>{l.includes(o.label||"")&&a.push({...o,provinceName:e.label})}))})),a}(j.value),console.log("热门城市数量:",V.value.length)),S.defaultValue&&S.defaultValue.length>0?(console.log("设置默认值:",S.defaultValue),B.value=[...S.defaultValue]):S.value&&S.value.length>0&&(console.log("设置当前值:",S.value),B.value=[...S.value]),console.log("城市数据加载完成，共",j.value.length,"个省份"),y((()=>{console.log("数据加载完成，触发重新渲染"),console.log("showSearchResults.value:",D.value),console.log("级联选择器应该显示:",!D.value),D.value&&!I.value&&(D.value=!1,console.log("重置搜索结果状态"))}))}catch(l){console.error("加载城市数据失败:",l),console.error("错误详情:",null==l?void 0:l.message),console.error("错误堆栈:",null==l?void 0:l.stack),"undefined"!=typeof uni&&_?_({title:"城市数据加载失败",icon:"error"}):console.error("无法显示错误提示，uni对象不可用")}},G=e=>{var l;const a=e.detail.value||(null==(l=e.target)?void 0:l.value)||"";I.value=a,a.trim()?(N.value=P(j.value,a),D.value=!0,U("search",a,N.value)):(N.value=[],D.value=!1)},Q=()=>{$.value=!0,I.value.trim()&&(D.value=!0)},W=()=>{$.value=!1,setTimeout((()=>{$.value||(D.value=!1)}),200)},X=()=>{I.value="",N.value=[],D.value=!1},Y=e=>{const l=[];return e.provinceName&&l.push(e.provinceName),e.cityName&&l.push(e.cityName),l.join(" > ")},Z=e=>{switch(e.searchType){case"province":return"省";case"city":return"市";case"district":return"区";default:return""}},ee=(e,l,a)=>{const o=J.value;o&&U("select",o,l)},le=(e,l)=>{B.value=e;const a=E(j.value,e);a&&U("change",a)},ae=(e,l)=>{const a=E(j.value,e);a&&U("confirm",a)},oe=()=>{U("cancel")},te=()=>{U("close")};o((()=>S.value),(e=>{e&&e.length>0&&(B.value=[...e])}),{deep:!0}),o(B,(e=>{U("update:value",e)}),{deep:!0}),o(j,(e=>{console.log("cityData 发生变化:",e.length,"个省份"),e.length>0&&(console.log("数据已加载，检查渲染状态..."),y((()=>{console.log("DOM 更新完成，级联选择器应该可见"),se()})))}),{deep:!0}),o(D,(e=>{console.log("showSearchResults 变化:",e),console.log("级联选择器显示状态:",!e)}));const se=()=>{console.log("=== PickerAddress 组件状态调试 ==="),console.log("cityData.value:",j.value),console.log("cityData 长度:",j.value.length),console.log("selectedValues.value:",B.value),console.log("hotCities.value:",V.value),console.log("showSearchResults.value:",D.value),console.log("searchResults.value:",N.value),console.log("currentAddressInfo.value:",J.value),console.log("cascadeContainerStyle.value:",q.value),console.log("props.height:",S.height),console.log("props.cascadeHeight:",S.cascadeHeight),console.log("props.showSearch:",S.showSearch),console.log("props.showHotCities:",S.showHotCities),console.log("props:",S),j.value.length>0&&(console.log("第一个省份数据:",j.value[0]),j.value[0].children&&j.value[0].children.length>0&&console.log("第一个城市数据:",j.value[0].children[0])),console.log("=== 调试结束 ===")};t((async()=>{console.log("PickerAddress 组件已挂载"),await K(),setTimeout((()=>{se()}),1e3)}));return k({getAddressInfo:()=>J.value,setAddress:(e,l,a)=>{const o=function(e,l,a,o){var t,s;const u=[],c=e.find((e=>{var a;return e.label===l||(null==(a=e.label)?void 0:a.includes(l||""))}));if(!c)return u;if(u.push(c.value),!a)return u;const n=null==(t=c.children)?void 0:t.find((e=>{var l;return e.label===a||(null==(l=e.label)?void 0:l.includes(a))}));if(!n)return u;if(u.push(n.value),!o)return u;const r=null==(s=n.children)?void 0:s.find((e=>{var l;return e.label===o||(null==(l=e.label)?void 0:l.includes(o))}));return r?(u.push(r.value),u):u}(j.value,e,l,a);B.value=o},clearSelection:()=>{B.value=[]},searchCity:e=>{I.value=e,e.trim()&&(N.value=P(j.value,e),D.value=!0)},debugState:se,forceShowCascade:()=>{console.log("强制显示级联选择器"),D.value=!1,I.value="",N.value=[],y((()=>{console.log("强制显示后的状态:",{showSearchResults:D.value,cityDataLength:j.value.length,cascadeContainerStyle:q.value}),se()}))},getComponentState:()=>({cityDataLength:j.value.length,showSearchResults:D.value,selectedValues:B.value,cascadeContainerStyle:q.value})}),(e,l)=>{const a=b,o=C,t=w,m=x;return s(),u(o,{class:"picker-address",style:n(L.value)},{default:c((()=>[e.showSearch?(s(),u(o,{key:0,class:"search-bar",style:n(R.value)},{default:c((()=>[r(o,{class:"search-input-wrapper"},{default:c((()=>[r(i(T),{name:"search",size:16,color:"#999"}),r(a,{modelValue:I.value,"onUpdate:modelValue":l[0]||(l[0]=e=>I.value=e),class:"search-input",placeholder:e.searchPlaceholder,onInput:G,onFocus:Q,onBlur:W},null,8,["modelValue","placeholder"]),I.value?(s(),u(o,{key:0,class:"clear-btn",onClick:X},{default:c((()=>[r(i(T),{name:"close",size:14,color:"#999"})])),_:1})):d("",!0)])),_:1})])),_:1},8,["style"])):d("",!0),D.value?(s(),u(o,{key:1,class:"search-results",style:n(F.value)},{default:c((()=>[r(m,{"scroll-y":"",class:"search-scroll"},{default:c((()=>[0===N.value.length?(s(),u(o,{key:0,class:"search-empty"},{default:c((()=>[r(t,{class:"empty-text"},{default:c((()=>[v("未找到相关城市")])),_:1})])),_:1})):(s(),u(o,{key:1,class:"search-list"},{default:c((()=>[(s(!0),h(f,null,p(N.value,((e,l)=>(s(),u(o,{key:l,class:"search-item",style:n(M.value),onClick:l=>(e=>{const l=[];if("province"===e.searchType)l.push(e.value);else if("city"===e.searchType){const a=j.value.find((l=>{var a;return null==(a=l.children)?void 0:a.some((l=>l.value===e.value))}));a&&l.push(a.value,e.value)}else if("district"===e.searchType){let a,o;j.value.forEach((l=>{var t;null==(t=l.children)||t.forEach((t=>{var s;(null==(s=t.children)?void 0:s.some((l=>l.value===e.value)))&&(a=l.value,o=t.value)}))})),a&&o&&l.push(a,o,e.value)}B.value=l,X();const a=E(j.value,l);a&&U("change",a)})(e)},{default:c((()=>[r(o,{class:"search-item-content"},{default:c((()=>[r(t,{class:"search-item-name"},{default:c((()=>[v(g(e.label),1)])),_:2},1024),e.provinceName||e.cityName?(s(),u(t,{key:0,class:"search-item-path"},{default:c((()=>[v(g(Y(e)),1)])),_:2},1024)):d("",!0)])),_:2},1024),r(t,{class:"search-item-type"},{default:c((()=>[v(g(Z(e)),1)])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1}))])),_:1})])),_:1},8,["style"])):d("",!0),e.showHotCities&&!D.value?(s(),u(o,{key:2,class:"hot-cities",style:n(z.value)},{default:c((()=>[r(t,{class:"hot-cities-title"},{default:c((()=>[v("热门城市")])),_:1}),r(o,{class:"hot-cities-grid"},{default:c((()=>[(s(!0),h(f,null,p(V.value,((e,l)=>(s(),u(o,{key:l,class:"hot-city-item",style:n(O.value),onClick:l=>(e=>{const l=j.value.find((l=>{var a;return null==(a=l.children)?void 0:a.some((l=>l.value===e.value))}));if(l){const a=[l.value,e.value];B.value=a;const o=E(j.value,a);o&&U("change",o)}})(e)},{default:c((()=>[r(t,{class:"hot-city-name"},{default:c((()=>[v(g(e.label),1)])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1},8,["style"])):d("",!0),D.value?d("",!0):(s(),u(o,{key:3,class:"cascade-container",style:n(q.value)},{default:c((()=>[r(i(H),{data:j.value,value:B.value,"onUpdate:value":l[1]||(l[1]=e=>B.value=e),title:e.title,placeholder:e.placeholder,"empty-text":e.emptyText,"show-close":!1,"show-footer":e.showFooter,"show-active-indicator":e.showActiveIndicator,height:"100%","item-height":e.itemHeight,"active-color":e.activeColor,"background-color":e.backgroundColor,"text-color":e.textColor,"border-color":e.borderColor,"confirm-text":e.confirmText,"cancel-text":e.cancelText,"label-field":"label","value-field":"value","children-field":"children",onSelect:ee,onChange:le,onConfirm:ae,onCancel:oe,onClose:te},null,8,["data","value","title","placeholder","empty-text","show-footer","show-active-indicator","item-height","active-color","background-color","text-color","border-color","confirm-text","cancel-text"])])),_:1},8,["style"]))])),_:1},8,["style"])}}}),[["__scopeId","data-v-2c4e83d5"]]),I=U(e({__name:"picker-address-demo",setup(e){const a=l(0),o=l(!1),m=l([]),y=l(""),_=l(!1),b=l([]),H=l(""),T=l(!1),U=l([]),E=l(""),P=l(!1),I=l([]),N=l(""),V=l(!1),D=l([]),$=l(""),L=l(!1),R=l([]),F=l(""),M=l(!1),z=l([]),O=l(""),q=l(),J=l(!1),K=l([]),G=l([]);t((()=>{a.value=A.getTotalHeight()}));const Q=()=>{o.value=!0},W=()=>{_.value=!0},X=()=>{T.value=!0},Y=e=>{y.value=e.fullAddress,o.value=!1},Z=e=>{H.value=e.fullAddress,_.value=!1},ee=e=>{E.value=e.fullAddress,T.value=!1},le=()=>{P.value=!0},ae=()=>{V.value=!0},oe=e=>{N.value=e.fullAddress,P.value=!1},te=e=>{$.value=e.fullAddress,V.value=!1},se=()=>{L.value=!0},ue=e=>{F.value=e.fullAddress,L.value=!1},ce=()=>{q.value&&(q.value.setAddress("北京市","北京市市辖区","东城区"),O.value="北京市东城区"),M.value=!0},ne=()=>{q.value&&(q.value.setAddress("上海市","上海市市辖区","黄浦区"),O.value="上海市黄浦区"),M.value=!0},re=()=>{q.value&&(q.value.clearSelection(),O.value=""),M.value=!0},ie=e=>{O.value=e.fullAddress,M.value=!1},de=()=>{G.value=[],J.value=!0},ve=(e,l)=>{const a=(new Date).toLocaleTimeString();G.value.push({time:a,event:e,data:"object"==typeof l?JSON.stringify(l):String(l)})},he=(e,l)=>{ve("select",`Level ${l}: ${e.fullAddress}`)},fe=e=>{ve("change",e.fullAddress)},pe=e=>{ve("confirm",e.fullAddress),J.value=!1},ge=()=>{ve("cancel","User cancelled"),J.value=!1},me=()=>{ve("close","Picker closed"),J.value=!1},ye=(e,l)=>{ve("search",`Keyword: ${e}, Results: ${l.length}`)};return(e,l)=>{const t=w,A=k,ve=C,_e=x;return s(),u(ve,{class:"page",style:n({paddingTop:a.value+"px"})},{default:c((()=>[r(i(S),{title:"城市选择器演示","show-back":!0,"background-color":"#007aff",color:"#ffffff"}),r(_e,{"scroll-y":"",class:"content",style:n({height:`calc(100vh - ${a.value}px)`})},{default:c((()=>[r(ve,{class:"demo-section"},{default:c((()=>[r(t,{class:"section-title"},{default:c((()=>[v("🏙️ 基础用法")])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("完整模式:")])),_:1}),r(A,{onClick:Q,class:"demo-btn"},{default:c((()=>[v("选择地址")])),_:1}),y.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 已选择: "+g(y.value),1)])),_:1})):d("",!0)])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("简单模式:")])),_:1}),r(A,{onClick:W,class:"demo-btn"},{default:c((()=>[v("选择地址")])),_:1}),H.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 已选择: "+g(H.value),1)])),_:1})):d("",!0)])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("搜索模式:")])),_:1}),r(A,{onClick:X,class:"demo-btn"},{default:c((()=>[v("选择地址")])),_:1}),E.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 已选择: "+g(E.value),1)])),_:1})):d("",!0)])),_:1})])),_:1}),r(ve,{class:"demo-section"},{default:c((()=>[r(t,{class:"section-title"},{default:c((()=>[v("🎨 样式定制")])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("绿色主题:")])),_:1}),r(A,{onClick:le,class:"demo-btn green"},{default:c((()=>[v("选择地址")])),_:1}),N.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 已选择: "+g(N.value),1)])),_:1})):d("",!0)])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("橙色主题:")])),_:1}),r(A,{onClick:ae,class:"demo-btn orange"},{default:c((()=>[v("选择地址")])),_:1}),$.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 已选择: "+g($.value),1)])),_:1})):d("",!0)])),_:1})])),_:1}),r(ve,{class:"demo-section"},{default:c((()=>[r(t,{class:"section-title"},{default:c((()=>[v("⚡ 高级功能")])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("默认值设置:")])),_:1}),r(A,{onClick:se,class:"demo-btn"},{default:c((()=>[v("选择地址")])),_:1}),F.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 已选择: "+g(F.value),1)])),_:1})):d("",!0)])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("程序化控制:")])),_:1}),r(ve,{class:"control-buttons"},{default:c((()=>[r(A,{onClick:ce,class:"control-btn"},{default:c((()=>[v("设置北京")])),_:1}),r(A,{onClick:ne,class:"control-btn"},{default:c((()=>[v("设置上海")])),_:1}),r(A,{onClick:re,class:"control-btn"},{default:c((()=>[v("清空")])),_:1})])),_:1}),O.value?(s(),u(t,{key:0,class:"demo-result"},{default:c((()=>[v(" 当前地址: "+g(O.value),1)])),_:1})):d("",!0)])),_:1})])),_:1}),r(ve,{class:"demo-section"},{default:c((()=>[r(t,{class:"section-title"},{default:c((()=>[v("📡 事件监听")])),_:1}),r(ve,{class:"demo-card"},{default:c((()=>[r(t,{class:"demo-label"},{default:c((()=>[v("事件日志:")])),_:1}),r(A,{onClick:de,class:"demo-btn"},{default:c((()=>[v("选择地址")])),_:1}),G.value.length>0?(s(),u(_e,{key:0,"scroll-y":"",class:"event-logs"},{default:c((()=>[(s(!0),h(f,null,p(G.value,((e,l)=>(s(),u(ve,{key:l,class:"event-log"},{default:c((()=>[r(t,{class:"event-time"},{default:c((()=>[v(g(e.time),1)])),_:2},1024),r(t,{class:"event-name"},{default:c((()=>[v(g(e.event),1)])),_:2},1024),r(t,{class:"event-data"},{default:c((()=>[v(g(e.data),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):d("",!0)])),_:1})])),_:1})])),_:1},8,["style"]),r(i(j),{visible:o.value,"onUpdate:visible":l[3]||(l[3]=e=>o.value=e),height:"70vh"},{default:c((()=>[r(i(B),{value:m.value,"onUpdate:value":l[0]||(l[0]=e=>m.value=e),title:"选择地址","show-close":!0,"show-footer":!0,onConfirm:Y,onClose:l[1]||(l[1]=e=>o.value=!1),onCancel:l[2]||(l[2]=e=>o.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:_.value,"onUpdate:visible":l[7]||(l[7]=e=>_.value=e),height:"400px"},{default:c((()=>[r(i(B),{value:b.value,"onUpdate:value":l[4]||(l[4]=e=>b.value=e),title:"选择地址","show-search":!1,"show-hot-cities":!1,"show-close":!0,"show-footer":!0,onConfirm:Z,onClose:l[5]||(l[5]=e=>_.value=!1),onCancel:l[6]||(l[6]=e=>_.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:T.value,"onUpdate:visible":l[11]||(l[11]=e=>T.value=e),height:"60vh"},{default:c((()=>[r(i(B),{value:U.value,"onUpdate:value":l[8]||(l[8]=e=>U.value=e),title:"搜索城市","show-search":!0,"show-hot-cities":!1,"show-close":!0,"show-footer":!0,onConfirm:ee,onClose:l[9]||(l[9]=e=>T.value=!1),onCancel:l[10]||(l[10]=e=>T.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:P.value,"onUpdate:visible":l[15]||(l[15]=e=>P.value=e),height:"70vh"},{default:c((()=>[r(i(B),{value:I.value,"onUpdate:value":l[12]||(l[12]=e=>I.value=e),title:"选择地址","active-color":"#52c41a","border-color":"#d9f7be","show-close":!0,"show-footer":!0,onConfirm:oe,onClose:l[13]||(l[13]=e=>P.value=!1),onCancel:l[14]||(l[14]=e=>P.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:V.value,"onUpdate:visible":l[19]||(l[19]=e=>V.value=e),height:"70vh"},{default:c((()=>[r(i(B),{value:D.value,"onUpdate:value":l[16]||(l[16]=e=>D.value=e),title:"选择地址","active-color":"#fa8c16","border-color":"#ffd591","show-close":!0,"show-footer":!0,onConfirm:te,onClose:l[17]||(l[17]=e=>V.value=!1),onCancel:l[18]||(l[18]=e=>V.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:L.value,"onUpdate:visible":l[23]||(l[23]=e=>L.value=e),height:"70vh"},{default:c((()=>[r(i(B),{value:R.value,"onUpdate:value":l[20]||(l[20]=e=>R.value=e),"default-value":[11e4,110100,110101],title:"选择地址","show-close":!0,"show-footer":!0,onConfirm:ue,onClose:l[21]||(l[21]=e=>L.value=!1),onCancel:l[22]||(l[22]=e=>L.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:M.value,"onUpdate:visible":l[27]||(l[27]=e=>M.value=e),height:"70vh"},{default:c((()=>[r(i(B),{ref_key:"programPickerRef",ref:q,value:z.value,"onUpdate:value":l[24]||(l[24]=e=>z.value=e),title:"程序化控制","show-close":!0,"show-footer":!0,onConfirm:ie,onClose:l[25]||(l[25]=e=>M.value=!1),onCancel:l[26]||(l[26]=e=>M.value=!1)},null,8,["value"])])),_:1},8,["visible"]),r(i(j),{visible:J.value,"onUpdate:visible":l[29]||(l[29]=e=>J.value=e),height:"70vh"},{default:c((()=>[r(i(B),{value:K.value,"onUpdate:value":l[28]||(l[28]=e=>K.value=e),title:"事件监听","show-close":!0,"show-footer":!0,onSelect:he,onChange:fe,onConfirm:pe,onCancel:ge,onClose:me,onSearch:ye},null,8,["value"])])),_:1},8,["visible"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-babd6332"]]);export{I as default};
