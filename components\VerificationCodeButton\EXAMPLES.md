# VerificationCodeButton 使用示例

## 🚀 快速开始

### 1. 最简单的使用

```vue
<template>
  <VerificationCodeButton @click="handleGetCode" />
</template>

<script setup lang="ts">
const handleGetCode = () => {
  console.log('获取验证码')
}
</script>
```

### 2. 完整的登录表单

```vue
<template>
  <view class="login-form">
    <input 
      v-model="phone" 
      placeholder="请输入手机号" 
      type="number"
      maxlength="11"
    />
    <view class="code-row">
      <input 
        v-model="code" 
        placeholder="请输入验证码" 
        type="number"
        maxlength="6"
      />
      <VerificationCodeButton
        ref="codeButtonRef"
        @click="handleGetCode"
      />
    </view>
    <button @click="handleLogin">登录</button>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VerificationCodeButton, verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const phone = ref('')
const code = ref('')
const codeButtonRef = ref()

const handleGetCode = async () => {
  // 验证手机号
  if (!verificationCodeButtonUtils.validatePhone(phone.value)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return
  }
  
  try {
    // 设置加载状态
    codeButtonRef.value?.setLoading(true)
    
    // 调用API
    const response = await fetch('/api/send-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone: phone.value })
    })
    
    if (response.ok) {
      // 开始倒计时
      codeButtonRef.value?.startCountdown()
      uni.showToast({ title: '验证码已发送', icon: 'success' })
    } else {
      throw new Error('发送失败')
    }
  } catch (error) {
    uni.showToast({ title: '发送失败，请重试', icon: 'error' })
  } finally {
    codeButtonRef.value?.setLoading(false)
  }
}

const handleLogin = () => {
  if (!phone.value || !code.value) {
    uni.showToast({ title: '请填写完整信息', icon: 'none' })
    return
  }
  
  // 登录逻辑
  console.log('登录', { phone: phone.value, code: code.value })
}
</script>

<style scoped>
.login-form {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.code-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

input {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  height: 36px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
}
</style>
```

## 🎨 样式定制示例

### 1. 不同主题

```vue
<template>
  <view class="theme-demo">
    <!-- 默认主题 -->
    <VerificationCodeButton 
      text="默认主题"
      @click="handleClick"
    />
    
    <!-- 成功主题 -->
    <VerificationCodeButton 
      v-bind="successTheme"
      text="成功主题"
      @click="handleClick"
    />
    
    <!-- 警告主题 -->
    <VerificationCodeButton 
      v-bind="warningTheme"
      text="警告主题"
      @click="handleClick"
    />
    
    <!-- 朴素主题 -->
    <VerificationCodeButton 
      v-bind="plainTheme"
      text="朴素主题"
      @click="handleClick"
    />
  </view>
</template>

<script setup lang="ts">
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const successTheme = verificationCodeButtonUtils.createPreset('success')
const warningTheme = verificationCodeButtonUtils.createPreset('warning')
const plainTheme = verificationCodeButtonUtils.createPreset('plain')

const handleClick = () => {
  console.log('点击了按钮')
}
</script>
```

### 2. 自定义样式

```vue
<template>
  <!-- 圆形按钮 -->
  <VerificationCodeButton
    width="80px"
    height="80px"
    border-radius="40px"
    background-color="#ff6b6b"
    text="获取"
    font-size="12"
  />
  
  <!-- 长条按钮 -->
  <VerificationCodeButton
    width="200px"
    height="32px"
    border-radius="16px"
    background-color="#4ecdc4"
    text="发送验证码到手机"
    font-size="13"
  />
  
  <!-- 边框按钮 -->
  <VerificationCodeButton
    background-color="transparent"
    text-color="#007aff"
    border-width="1"
    border-color="#007aff"
    text="获取验证码"
  />
</template>
```

## ⚡ 高级功能示例

### 1. 自动倒计时

```vue
<template>
  <VerificationCodeButton
    :auto-start="true"
    text="自动倒计时"
    @click="handleAutoStart"
  />
</template>

<script setup lang="ts">
const handleAutoStart = () => {
  console.log('点击后自动开始倒计时')
  // 这里只需要处理发送验证码的逻辑
  // 倒计时会自动开始
}
</script>
```

### 2. 自定义倒计时

```vue
<template>
  <VerificationCodeButton
    :countdown="120"
    countdown-format="请等待 {time} 秒"
    @click="handleCustomCountdown"
  />
</template>

<script setup lang="ts">
const handleCustomCountdown = () => {
  console.log('自定义120秒倒计时')
}
</script>
```

### 3. 手动控制

```vue
<template>
  <view>
    <VerificationCodeButton
      ref="manualButtonRef"
      text="手动控制"
      @click="handleManualClick"
    />
    
    <view class="controls">
      <button @click="startCountdown">开始倒计时</button>
      <button @click="stopCountdown">停止倒计时</button>
      <button @click="setLoading">切换加载</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const manualButtonRef = ref()

const handleManualClick = () => {
  console.log('手动控制按钮被点击')
}

const startCountdown = () => {
  manualButtonRef.value?.startCountdown()
}

const stopCountdown = () => {
  manualButtonRef.value?.stopCountdown()
}

const setLoading = () => {
  const state = manualButtonRef.value?.getState()
  manualButtonRef.value?.setLoading(!state?.isLoading)
}
</script>
```

### 4. 事件监听

```vue
<template>
  <view>
    <VerificationCodeButton
      @click="handleClick"
      @countdown-start="handleCountdownStart"
      @countdown-tick="handleCountdownTick"
      @countdown-end="handleCountdownEnd"
    />
    
    <view class="event-log">
      <text v-for="log in eventLogs" :key="log.id">{{ log.message }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const eventLogs = ref([])
let logId = 0

const addLog = (message) => {
  eventLogs.value.push({
    id: logId++,
    message: `${new Date().toLocaleTimeString()}: ${message}`
  })
}

const handleClick = () => {
  addLog('按钮被点击')
}

const handleCountdownStart = (countdown) => {
  addLog(`倒计时开始: ${countdown}秒`)
}

const handleCountdownTick = (remaining) => {
  addLog(`剩余时间: ${remaining}秒`)
}

const handleCountdownEnd = () => {
  addLog('倒计时结束')
}
</script>
```

## 🔧 实际应用场景

### 1. 注册页面

```vue
<template>
  <form class="register-form">
    <input v-model="form.username" placeholder="用户名" />
    <input v-model="form.password" type="password" placeholder="密码" />
    
    <view class="phone-row">
      <input v-model="form.phone" placeholder="手机号" type="tel" />
      <VerificationCodeButton
        v-bind="registerPreset"
        @click="handleSendRegisterCode"
      />
    </view>
    
    <input v-model="form.code" placeholder="验证码" />
    <button @click="handleRegister">注册</button>
  </form>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const form = reactive({
  username: '',
  password: '',
  phone: '',
  code: ''
})

const registerPreset = verificationCodeButtonUtils.createPreset('success', 'default', 'register')

const handleSendRegisterCode = async () => {
  if (!verificationCodeButtonUtils.validatePhone(form.phone)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return
  }
  
  // 发送注册验证码逻辑
}

const handleRegister = () => {
  // 注册逻辑
}
</script>
```

### 2. 找回密码页面

```vue
<template>
  <form class="reset-form">
    <input v-model="phone" placeholder="请输入手机号" />
    
    <view class="code-row">
      <input v-model="code" placeholder="验证码" />
      <VerificationCodeButton
        v-bind="resetPreset"
        @click="handleSendResetCode"
      />
    </view>
    
    <input v-model="newPassword" type="password" placeholder="新密码" />
    <button @click="handleResetPassword">重置密码</button>
  </form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const phone = ref('')
const code = ref('')
const newPassword = ref('')

const resetPreset = verificationCodeButtonUtils.createPreset('warning', 'default', 'resetPassword')

const handleSendResetCode = async () => {
  // 发送重置密码验证码逻辑
}

const handleResetPassword = () => {
  // 重置密码逻辑
}
</script>
```

### 3. 绑定手机号页面

```vue
<template>
  <view class="bind-phone">
    <text class="tip">为了账户安全，请绑定手机号</text>
    
    <view class="input-group">
      <input v-model="phone" placeholder="请输入手机号" />
      <VerificationCodeButton
        v-bind="bindPreset"
        @click="handleSendBindCode"
      />
    </view>
    
    <input v-model="code" placeholder="请输入验证码" />
    <button @click="handleBindPhone">绑定手机号</button>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const phone = ref('')
const code = ref('')

const bindPreset = verificationCodeButtonUtils.createPreset('default', 'default', 'bindPhone')

const handleSendBindCode = async () => {
  // 发送绑定验证码逻辑
}

const handleBindPhone = () => {
  // 绑定手机号逻辑
}
</script>
```

## 🛠️ 工具函数使用

### 1. 创建异步处理器

```typescript
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

// 创建发送验证码的异步处理器
const sendCodeHandler = verificationCodeButtonUtils.createAsyncHandler(
  // API调用函数
  () => fetch('/api/send-code', {
    method: 'POST',
    body: JSON.stringify({ phone: phone.value })
  }),
  // 选项
  {
    onStart: () => {
      console.log('开始发送验证码')
    },
    onSuccess: (result) => {
      uni.showToast({ title: '验证码已发送', icon: 'success' })
    },
    onError: (error) => {
      uni.showToast({ title: '发送失败', icon: 'error' })
    },
    onFinally: () => {
      console.log('发送完成')
    }
  }
)

// 使用
const handleGetCode = () => {
  if (!verificationCodeButtonUtils.validatePhone(phone.value)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return
  }
  
  sendCodeHandler(codeButtonRef.value)
}
```

### 2. 表单验证

```typescript
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const validateForm = () => {
  // 验证手机号
  if (!verificationCodeButtonUtils.validatePhone(form.phone)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return false
  }
  
  // 验证邮箱（如果需要）
  if (form.email && !verificationCodeButtonUtils.validateEmail(form.email)) {
    uni.showToast({ title: '请输入正确的邮箱', icon: 'none' })
    return false
  }
  
  return true
}
```

这些示例展示了 VerificationCodeButton 组件在各种实际场景中的使用方法，可以根据具体需求进行调整和定制。
