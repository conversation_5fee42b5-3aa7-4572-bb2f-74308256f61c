import{p as a,q as t,s as e,u as s,v as l,c as o,w as i,i as n,o as c,a as u,b as d,t as r,x as f,k as m,r as p,F as _,e as h,f as g,I}from"./index-ChUEiI3E.js";import{_ as D}from"./_plugin-vue_export-helper.BCo6x5W8.js";const w=D({data:()=>({userInfo:{name:"张三",age:25,email:"<EMAIL>",isActive:!0},locationInfo:null,dataList:[],formData:{username:"",password:""}}),methods:{async getUserLocation(){try{a({title:"获取位置中..."});const s=await this.getLocation();this.locationInfo={longitude:s.longitude,latitude:s.latitude,address:`${s.longitude}, ${s.latitude}`},t(),e({title:"位置获取成功",icon:"success"})}catch(s){t(),e({title:"位置获取失败",icon:"error"}),console.error("获取位置失败:",s)}},getLocation:()=>new Promise(((a,t)=>{s({type:"wgs84",success:a,fail:t})})),loadData(){a({title:"加载中..."}),setTimeout((()=>{this.dataList=[{id:1,title:"任务一",status:"active"},{id:2,title:"任务二",status:"pending"},{id:3,title:"任务三",status:"inactive"}],t(),e({title:"数据加载完成",icon:"success"})}),1e3)},submitForm(){this.validateForm()&&l({title:"提交确认",content:`用户名: ${this.formData.username}`,success:a=>{a.confirm&&this.handleSubmit()}})},validateForm(){return this.formData.username.trim()?!!this.formData.password.trim()||(e({title:"请输入密码",icon:"error"}),!1):(e({title:"请输入用户名",icon:"error"}),!1)},handleSubmit(){console.log("提交数据:",this.formData),e({title:"提交成功",icon:"success"}),this.formData={username:"",password:""}}},onLoad(){console.log("TypeScript页面加载完成")},onShow(){console.log("TypeScript页面显示")}},[["render",function(a,t,e,s,l,D){const w=h,k=n,y=g,v=I;return c(),o(k,{class:"container"},{default:i((()=>[u(k,{class:"header"},{default:i((()=>[u(w,{class:"title"},{default:i((()=>[d("UniApp TypeScript 示例")])),_:1})])),_:1}),u(k,{class:"content"},{default:i((()=>[u(k,{class:"user-card"},{default:i((()=>[u(w,{class:"card-title"},{default:i((()=>[d("用户信息")])),_:1}),u(k,{class:"user-info"},{default:i((()=>[u(w,null,{default:i((()=>[d("姓名: "+r(l.userInfo.name),1)])),_:1}),u(w,null,{default:i((()=>[d("年龄: "+r(l.userInfo.age),1)])),_:1}),u(w,null,{default:i((()=>[d("邮箱: "+r(l.userInfo.email),1)])),_:1}),u(w,null,{default:i((()=>[d("状态: "+r(l.userInfo.isActive?"活跃":"非活跃"),1)])),_:1})])),_:1})])),_:1}),u(k,{class:"api-section"},{default:i((()=>[u(w,{class:"card-title"},{default:i((()=>[d("API 调用示例")])),_:1}),u(y,{onClick:D.getUserLocation,class:"btn"},{default:i((()=>[d("获取位置信息")])),_:1},8,["onClick"]),l.locationInfo?(c(),o(k,{key:0,class:"location-info"},{default:i((()=>[u(w,null,{default:i((()=>[d("经度: "+r(l.locationInfo.longitude),1)])),_:1}),u(w,null,{default:i((()=>[d("纬度: "+r(l.locationInfo.latitude),1)])),_:1}),u(w,null,{default:i((()=>[d("地址: "+r(l.locationInfo.address),1)])),_:1})])),_:1})):f("",!0)])),_:1}),u(k,{class:"list-section"},{default:i((()=>[u(w,{class:"card-title"},{default:i((()=>[d("数据列表")])),_:1}),u(y,{onClick:D.loadData,class:"btn"},{default:i((()=>[d("加载数据")])),_:1},8,["onClick"]),l.dataList.length>0?(c(),o(k,{key:0,class:"data-list"},{default:i((()=>[(c(!0),m(_,null,p(l.dataList,(a=>(c(),o(k,{key:a.id,class:"list-item"},{default:i((()=>[u(w,null,{default:i((()=>[d(r(a.title)+" - "+r(a.status),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):f("",!0)])),_:1}),u(k,{class:"form-section"},{default:i((()=>[u(w,{class:"card-title"},{default:i((()=>[d("表单示例")])),_:1}),u(v,{modelValue:l.formData.username,"onUpdate:modelValue":t[0]||(t[0]=a=>l.formData.username=a),placeholder:"请输入用户名",class:"input"},null,8,["modelValue"]),u(v,{modelValue:l.formData.password,"onUpdate:modelValue":t[1]||(t[1]=a=>l.formData.password=a),placeholder:"请输入密码",type:"password",class:"input"},null,8,["modelValue"]),u(y,{onClick:D.submitForm,class:"btn"},{default:i((()=>[d("提交")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-535dace3"]]);export{w as default};
