import{m as e,n as t,h as a,g as s,c as l,w as n,i as o,o as r,a as c,b as u,f as i,e as d,S as m}from"./index-ChUEiI3E.js";import{_ as f}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=f({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/type-aliases"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/generics"})},markAsCompleted(){try{let e=a("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("functions")||(e.completedItems.push("functions"),e.lastUpdate=Date.now(),s("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("函数类型课程加载完成")}},[["render",function(e,t,a,s,f,g){const p=i,b=d,_=o,x=m;return r(),l(_,{class:"container"},{default:n((()=>[c(_,{class:"header"},{default:n((()=>[c(p,{onClick:g.goBack,class:"back-btn"},{default:n((()=>[u("← 返回")])),_:1},8,["onClick"]),c(b,{class:"title"},{default:n((()=>[u("函数类型")])),_:1}),c(_,{class:"progress-indicator"},{default:n((()=>[u("4/9")])),_:1})])),_:1}),c(x,{"scroll-y":"",class:"content"},{default:n((()=>[c(_,{class:"lesson-intro"},{default:n((()=>[c(b,{class:"intro-title"},{default:n((()=>[u("学习目标")])),_:1}),c(b,{class:"intro-text"},{default:n((()=>[u("掌握 TypeScript 中函数的类型定义、参数类型、返回值类型、函数重载等概念")])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("🔧 基本函数类型")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u("// 函数声明 function add(a: number, b: number): number { return a + b } // 函数表达式 const multiply = function(a: number, b: number): number { return a * b } // 箭头函数 const subtract = (a: number, b: number): number => { return a - b } // 简化的箭头函数 const divide = (a: number, b: number): number => a / b // 使用函数 console.log(add(5, 3)) // 8 console.log(multiply(4, 2)) // 8 console.log(subtract(10, 3)) // 7 console.log(divide(8, 2)) // 4")])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 函数参数和返回值都需要指定类型")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 支持函数声明、函数表达式、箭头函数")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• TypeScript 可以推断简单的返回值类型")])),_:1})])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("❓ 可选参数和默认参数")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u('// 可选参数（使用 ? 标记） function greet(name: string, greeting?: string): string { if (greeting) { return `${greeting}, ${name}!` } return `Hello, ${name}!` } console.log(greet("张三")) // "Hello, 张三!" console.log(greet("李四", "你好")) // "你好, 李四!" // 默认参数 function createUser(name: string, age: number = 18, isActive: boolean = true): object { return { name, age, isActive } } console.log(createUser("王五")) // { name: "王五", age: 18, isActive: true } console.log(createUser("赵六", 25)) // { name: "赵六", age: 25, isActive: true } console.log(createUser("孙七", 30, false)) // { name: "孙七", age: 30, isActive: false } // 可选参数必须在必需参数之后 function buildName(firstName: string, lastName?: string): string { if (lastName) { return `${firstName} ${lastName}` } return firstName }')])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 可选参数使用 ? 标记，必须在必需参数之后")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 默认参数自动成为可选参数")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 默认参数可以在任意位置")])),_:1})])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("📦 剩余参数")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u('// 剩余参数 function sum(...numbers: number[]): number { return numbers.reduce((total, num) => total + num, 0) } console.log(sum(1, 2, 3)) // 6 console.log(sum(1, 2, 3, 4, 5)) // 15 // 混合参数 function introduce(name: string, age: number, ...hobbies: string[]): string { const hobbyText = hobbies.length > 0 ? `，爱好：${hobbies.join(\'、\')}` : \'\' return `我是${name}，今年${age}岁${hobbyText}` } console.log(introduce("张三", 25)) // "我是张三，今年25岁" console.log(introduce("李四", 30, "读书", "游泳", "编程")) // "我是李四，今年30岁，爱好：读书、游泳、编程" // 剩余参数的类型 function logMessages(prefix: string, ...messages: (string | number)[]): void { messages.forEach(msg => { console.log(`${prefix}: ${msg}`) }) } logMessages("INFO", "系统启动", 200, "连接成功")')])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 剩余参数使用 ...参数名 语法")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 剩余参数是一个数组类型")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 剩余参数必须是最后一个参数")])),_:1})])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("📝 函数类型表达式")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u("// 函数类型定义 type MathOperation = (a: number, b: number) => number // 使用函数类型 const add: MathOperation = (x, y) => x + y const multiply: MathOperation = (x, y) => x * y // 函数作为参数 function calculate(operation: MathOperation, a: number, b: number): number { return operation(a, b) } console.log(calculate(add, 5, 3)) // 8 console.log(calculate(multiply, 4, 2)) // 8 // 更复杂的函数类型 type EventHandler = (event: string, data?: any) => void type Validator = (value: string) => boolean type Transformer<T, U> = (input: T) => U // 使用示例 const handleClick: EventHandler = (event, data) => { console.log(`处理事件: ${event}`, data) } const isEmail: Validator = (email) => { return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email) } const toString: Transformer<number, string> = (num) => { return num.toString() }")])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 可以定义函数类型别名")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 函数类型可以作为参数类型")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 支持泛型函数类型")])),_:1})])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("🔄 函数重载")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u('// 函数重载声明 function format(value: string): string function format(value: number): string function format(value: boolean): string function format(value: Date): string // 函数实现 function format(value: string | number | boolean | Date): string { if (typeof value === \'string\') { return `"${value}"` } else if (typeof value === \'number\') { return value.toFixed(2) } else if (typeof value === \'boolean\') { return value ? \'Yes\' : \'No\' } else if (value instanceof Date) { return value.toLocaleDateString() } return String(value) } // 使用重载函数 console.log(format("Hello")) // "Hello" console.log(format(3.14159)) // "3.14" console.log(format(true)) // "Yes" console.log(format(new Date())) // "2024/1/1" // 更复杂的重载示例 function createElement(tag: "div"): HTMLDivElement function createElement(tag: "span"): HTMLSpanElement function createElement(tag: "button"): HTMLButtonElement function createElement(tag: string): HTMLElement function createElement(tag: string): HTMLElement { return document.createElement(tag) } // TypeScript 会根据参数推断返回类型 const div = createElement("div") // HTMLDivElement const span = createElement("span") // HTMLSpanElement const button = createElement("button") // HTMLButtonElement')])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 函数重载允许同一函数有多个类型签名")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 重载声明在前，实现在后")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• TypeScript 会根据参数选择合适的重载")])),_:1})])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("🎯 this 类型")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u("// 显式 this 类型 interface Calculator { value: number add(this: Calculator, num: number): Calculator multiply(this: Calculator, num: number): Calculator getValue(this: Calculator): number } const calculator: Calculator = { value: 0, add(this: Calculator, num: number): Calculator { this.value += num return this }, multiply(this: Calculator, num: number): Calculator { this.value *= num return this }, getValue(this: Calculator): number { return this.value } } // 链式调用 const result = calculator .add(5) .multiply(2) .add(3) .getValue() console.log(result) // 13 // 箭头函数中的 this class Counter { private count = 0 // 普通方法，this 指向实例 increment(): void { this.count++ } // 箭头函数，this 绑定到类实例 getIncrementer = (): (() => void) => { return () => { this.count++ } } getCount(): number { return this.count } }")])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 可以显式声明 this 的类型")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• this 参数必须是第一个参数")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 箭头函数会绑定外层的 this")])),_:1})])),_:1})])),_:1}),c(_,{class:"section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("⚡ 异步函数")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u("// 异步函数类型 async function fetchUser(id: number): Promise<User> { const response = await fetch(`/api/users/${id}`) const user: User = await response.json() return user } // 异步箭头函数 const fetchUserData = async (id: number): Promise<User> => { try { const user = await fetchUser(id) return user } catch (error) { throw new Error(`获取用户失败: ${error}`) } } // 异步函数类型定义 type AsyncOperation<T> = () => Promise<T> type AsyncTransformer<T, U> = (input: T) => Promise<U> // UniApp 异步函数示例 function getLocation(): Promise<UniApp.GetLocationSuccess> { return new Promise((resolve, reject) => { uni.getLocation({ type: 'wgs84', success: resolve, fail: reject }) }) } async function getCurrentPosition(): Promise<{latitude: number, longitude: number}> { try { const location = await getLocation() return { latitude: location.latitude, longitude: location.longitude } } catch (error) { throw new Error('获取位置失败') } }")])),_:1})])),_:1}),c(_,{class:"explanation"},{default:n((()=>[c(b,{class:"exp-text"},{default:n((()=>[u("• 异步函数返回 Promise 类型")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 可以使用 async/await 语法")])),_:1}),c(b,{class:"exp-text"},{default:n((()=>[u("• 需要正确处理错误和异常")])),_:1})])),_:1})])),_:1}),c(_,{class:"practice-section"},{default:n((()=>[c(b,{class:"section-title"},{default:n((()=>[u("🎯 实践练习")])),_:1}),c(_,{class:"practice-item"},{default:n((()=>[c(b,{class:"practice-title"},{default:n((()=>[u("练习 1：创建工具函数")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u("// 创建一个 debounce 函数 // 参数：函数、延迟时间 // 返回：防抖后的函数")])),_:1})])),_:1})])),_:1}),c(_,{class:"practice-item"},{default:n((()=>[c(b,{class:"practice-title"},{default:n((()=>[u("练习 2：函数重载")])),_:1}),c(_,{class:"code-block"},{default:n((()=>[c(b,{class:"code"},{default:n((()=>[u("// 创建一个 parseValue 函数，支持： // - parseValue(str: string): string // - parseValue(num: number): number // - parseValue(bool: boolean): boolean")])),_:1})])),_:1})])),_:1})])),_:1}),c(_,{class:"navigation"},{default:n((()=>[c(p,{onClick:g.previousLesson,class:"nav-btn secondary"},{default:n((()=>[u("上一课：类型别名")])),_:1},8,["onClick"]),c(p,{onClick:g.nextLesson,class:"nav-btn primary"},{default:n((()=>[u("下一课：泛型")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-1abe7794"]]);export{g as default};
