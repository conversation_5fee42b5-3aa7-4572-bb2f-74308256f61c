import"./index-ChUEiI3E.js";const e={createConfig:(e={})=>({value:0,modelValue:0,min:0,max:99999,step:1,precision:0,width:"auto",height:60,backgroundColor:"#ffffff",borderColor:"#e5e5e5",borderRadius:8,btnWidth:60,btnHeight:60,btnBackgroundColor:"#f8f9fa",btnBorderColor:"#e5e5e5",color:"#333333",inputBackgroundColor:"#ffffff",inputBorderColor:"#e5e5e5",inputColor:"#333333",minusText:"-",plusText:"+",disabled:!1,readonly:!1,disabledColor:"#cccccc",placeholder:"",longpress:!0,longpressTime:600,customClass:"",customStyle:{},...e}),createCartConfig(){return this.createConfig({width:200,height:50,btnWidth:50,btnHeight:50,borderRadius:25,backgroundColor:"#f8f9fa",btnBackgroundColor:"#007aff",color:"#ffffff",step:1,min:0,max:999})},createCounterConfig(){return this.createConfig({width:150,height:40,btnWidth:40,btnHeight:40,borderRadius:4,step:1,min:1,max:99})},createPriceConfig(){return this.createConfig({width:250,height:60,step:.01,precision:2,min:0,max:999999.99,formatter:e=>`¥${e.toFixed(2)}`,parser:e=>parseFloat(e.replace(/¥/g,""))||0})},createQuantityConfig(){return this.createConfig({width:180,height:50,step:1,min:1,max:999,longpress:!0,longpressTime:500})},createRatingConfig(){return this.createConfig({width:200,height:50,step:.5,precision:1,min:0,max:5,formatter:e=>`${e.toFixed(1)}分`})},validateValue:(e,r,t)=>e>=r&&e<=t,formatValue:(e,r=0)=>r>0?Number(e.toFixed(r)):Math.round(e),calculateStepValue:(e,r,t)=>"plus"===t?e+r:e-r,checkBoundary:(e,r,t)=>({isMin:e<=r,isMax:e>=t,isValid:e>=r&&e<=t}),generateRandomValue(e,r,t=0){const o=Math.random()*(r-e)+e;return this.formatValue(o,t)},createCurrencyFormatter:(e="¥",r=2)=>t=>`${e}${t.toFixed(r)}`,createCurrencyParser:(e="¥")=>r=>{const t=new RegExp(`\\${e}`,"g");return parseFloat(r.replace(t,""))||0},createPercentageFormatter:(e=1)=>r=>`${r.toFixed(e)}%`,createPercentageParser:()=>e=>parseFloat(e.replace(/%/g,""))||0,getThemeColors(){const e=this.isDarkTheme();return{backgroundColor:e?"#2a2a2a":"#ffffff",borderColor:e?"#444444":"#e5e5e5",btnBackgroundColor:e?"#3a3a3a":"#f8f9fa",color:e?"#ffffff":"#333333",disabledColor:e?"#666666":"#cccccc"}},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches};e.createConfig(),e.createCartConfig(),e.createCounterConfig(),e.createPriceConfig(),e.createQuantityConfig(),e.createRatingConfig();
