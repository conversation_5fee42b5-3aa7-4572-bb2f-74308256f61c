import"./index-ChUEiI3E.js";const e={createConfig:(e={})=>({visible:!1,title:"",content:"",height:"auto",maxHeight:"80%",minHeight:"200px",borderRadius:"20px 20px 0 0",backgroundColor:"#ffffff",maskClosable:!0,maskColor:"#000000",maskOpacity:.5,titleColor:"#333333",titleSize:18,titleAlign:"center",contentColor:"#666666",contentSize:16,contentPadding:"20px",showClose:!1,closeText:"✕",closeColor:"#999999",closeSize:16,showFooter:!1,showCancel:!0,showConfirm:!0,cancelText:"取消",confirmText:"确定",cancelColor:"#666666",confirmColor:"#ffffff",cancelBgColor:"#f8f9fa",confirmBgColor:"#007aff",showHandle:!0,handleColor:"#e5e5e5",animationDuration:300,safeAreaInsetBottom:!0,zIndex:9999,customClass:"",customStyle:{},...e}),createPickerConfig(){return this.createConfig({height:"50%",title:"请选择",showClose:!0,showFooter:!0,showHandle:!0})},createMenuConfig(){return this.createConfig({height:"auto",maxHeight:"60%",showHandle:!0,showFooter:!1,contentPadding:"0"})},createFormConfig(){return this.createConfig({height:"auto",maxHeight:"80%",title:"表单",showClose:!0,showFooter:!0,showHandle:!1,contentPadding:"20px"})},createConfirmConfig(e,t){return this.createConfig({title:e,content:t,height:"auto",showClose:!1,showFooter:!0,showHandle:!1,maskClosable:!1})},createShareConfig(){return this.createConfig({title:"分享到",height:"auto",maxHeight:"50%",showClose:!0,showFooter:!1,showHandle:!0,contentPadding:"20px 20px 40px"})},createListConfig(){return this.createConfig({height:"60%",showHandle:!0,showFooter:!1,contentPadding:"0"})},createDetailConfig(){return this.createConfig({height:"80%",title:"详情",showClose:!0,showFooter:!1,showHandle:!0,contentPadding:"20px"})},createInputConfig(e){return this.createConfig({title:e,height:"auto",showClose:!0,showFooter:!0,showHandle:!1,contentPadding:"20px"})},getThemeColors(){const e=this.isDarkTheme();return{backgroundColor:e?"#2a2a2a":"#ffffff",titleColor:e?"#ffffff":"#333333",contentColor:e?"#cccccc":"#666666",handleColor:e?"#555555":"#e5e5e5",maskColor:"#000000",cancelBgColor:e?"#3a3a3a":"#f8f9fa",cancelColor:e?"#cccccc":"#666666"}},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches,getSafeAreaHeight(){if("undefined"!=typeof window&&CSS.supports("height","env(safe-area-inset-bottom)")){const e=document.createElement("div");e.style.height="env(safe-area-inset-bottom)",e.style.position="fixed",e.style.bottom="0",e.style.visibility="hidden",document.body.appendChild(e);const t=e.offsetHeight;return document.body.removeChild(e),t}return 0},debounce(e,t){let o=null;return(...n)=>{o&&clearTimeout(o),o=setTimeout((()=>e(...n)),t)}},throttle(e,t){let o=!1;return(...n)=>{o||(e(...n),o=!0,setTimeout((()=>o=!1),t))}}};e.createConfig(),e.createPickerConfig(),e.createMenuConfig(),e.createFormConfig(),e.createShareConfig(),e.createListConfig(),e.createDetailConfig();
