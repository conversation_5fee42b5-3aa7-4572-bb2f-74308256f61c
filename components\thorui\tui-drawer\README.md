# TUI-Drawer 抽屉组件

## 概述

TUI-Drawer 是一个功能强大的抽屉组件，支持从四个方向（上、下、左、右）弹出，具有完整的 TypeScript 支持和丰富的配置选项。

## 特性

- ✅ **四方向支持**: 支持从左、右、上、下四个方向弹出
- ✅ **TypeScript**: 完整的 TypeScript 类型支持
- ✅ **自定义样式**: 可自定义宽度、高度、背景色、圆角等
- ✅ **遮罩控制**: 支持遮罩层显示/隐藏和点击关闭
- ✅ **关闭按钮**: 可选的关闭按钮，支持多种位置
- ✅ **丰富事件**: 提供 open、opened、close、closed 事件
- ✅ **平滑动画**: 可自定义动画时长，平滑的过渡效果
- ✅ **跨平台**: 兼容 H5、小程序、APP 等多个平台

## 安装使用

### 引入方式

#### 方式一：手动引入

```javascript
import tuiDrawer from "@/components/thorui/tui-drawer/tui-drawer.vue"

export default {
  components: {
    tuiDrawer
  }
}
```

#### 方式二：easycom 自动引入

在 `pages.json` 中配置：

```json
{
  "easycom": {
    "^tui-(.*)": "@/components/thorui/tui-$1/tui-$1.vue"
  }
}
```

## 基础用法

### 基本示例

```vue
<template>
  <view>
    <button @click="openDrawer">打开抽屉</button>
    
    <tui-drawer 
      :visible="drawerVisible" 
      mode="right" 
      @close="closeDrawer"
    >
      <view class="drawer-content">
        <text>抽屉内容</text>
      </view>
    </tui-drawer>
  </view>
</template>

<script>
export default {
  data() {
    return {
      drawerVisible: false
    }
  },
  methods: {
    openDrawer() {
      this.drawerVisible = true
    },
    closeDrawer() {
      this.drawerVisible = false
    }
  }
}
</script>
```

### 不同方向

```vue
<template>
  <view>
    <!-- 左侧抽屉 -->
    <tui-drawer mode="left" :visible="leftVisible" @close="leftVisible = false">
      <view class="drawer-content">左侧内容</view>
    </tui-drawer>
    
    <!-- 右侧抽屉 -->
    <tui-drawer mode="right" :visible="rightVisible" @close="rightVisible = false">
      <view class="drawer-content">右侧内容</view>
    </tui-drawer>
    
    <!-- 顶部抽屉 -->
    <tui-drawer mode="top" :visible="topVisible" @close="topVisible = false">
      <view class="drawer-content">顶部内容</view>
    </tui-drawer>
    
    <!-- 底部抽屉 -->
    <tui-drawer mode="bottom" :visible="bottomVisible" @close="bottomVisible = false">
      <view class="drawer-content">底部内容</view>
    </tui-drawer>
  </view>
</template>
```

### 自定义样式

```vue
<template>
  <tui-drawer 
    :visible="visible"
    mode="right"
    width="80%"
    background-color="#f5f5f5"
    border-radius="20rpx"
    mask-color="rgba(0, 0, 0, 0.8)"
    :duration="500"
    @close="visible = false"
  >
    <view class="custom-content">
      自定义样式的抽屉
    </view>
  </tui-drawer>
</template>
```

### 带关闭按钮

```vue
<template>
  <tui-drawer 
    :visible="visible"
    mode="right"
    :show-close="true"
    close-position="top-right"
    @close="visible = false"
  >
    <view class="drawer-content">
      带关闭按钮的抽屉
    </view>
  </tui-drawer>
</template>
```

## API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 是否显示抽屉 |
| mask | Boolean | true | 是否显示遮罩层 |
| maskClosable | Boolean | true | 遮罩是否可点击关闭 |
| mode | String | 'right' | 抽屉弹出方向，可选值：'left', 'right', 'top', 'bottom' |
| width | String | '70%' | 抽屉宽度（left/right 模式时有效） |
| height | String | '50%' | 抽屉高度（top/bottom 模式时有效） |
| zIndex | Number/String | 990 | 抽屉 z-index 值 |
| maskZIndex | Number/String | 980 | 遮罩 z-index 值 |
| backgroundColor | String | '#fff' | 抽屉背景色 |
| maskColor | String | 'rgba(0, 0, 0, 0.6)' | 遮罩背景色 |
| borderRadius | String | '0' | 圆角大小 |
| duration | Number | 300 | 动画持续时间（毫秒） |
| showClose | Boolean | false | 是否显示关闭按钮 |
| closePosition | String | 'top-right' | 关闭按钮位置，可选值：'top-left', 'top-right', 'bottom-left', 'bottom-right' |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| open | 抽屉开始打开时触发 | - |
| opened | 抽屉打开完成时触发 | - |
| close | 抽屉开始关闭时触发 | - |
| closed | 抽屉关闭完成时触发 | - |

### Slots

| 插槽名 | 说明 |
|--------|------|
| default | 抽屉内容 |

## 注意事项

1. **内容滚动**: 当抽屉内容超过一屏时，建议在插槽内使用 `scroll-view` 组件
2. **事件处理**: `maskClosable` 为 `true` 时，需要绑定 `close` 事件并在事件中将 `visible` 设置为 `false`
3. **动画性能**: 在低端设备上建议适当减少 `duration` 值以提升性能
4. **层级管理**: 注意 `zIndex` 和 `maskZIndex` 的设置，避免与其他组件冲突

## 平台兼容性

| 平台 | 支持情况 |
|------|----------|
| H5 | ✅ |
| 微信小程序 | ✅ |
| 支付宝小程序 | ✅ |
| 百度小程序 | ✅ |
| 字节小程序 | ✅ |
| QQ小程序 | ✅ |
| APP-Vue | ✅ |
| APP-Nvue | 升级中 |

## 更新日志

### v2.0.0 (2024-07-29)
- 🎉 新增完整的 TypeScript 支持
- 🎉 新增 width/height 自定义属性
- 🎉 新增关闭按钮功能
- 🎉 新增更多事件回调（open, opened, closed）
- 🎉 优化动画效果和性能
- 🎉 增强跨平台兼容性
- 🎉 完善文档和类型定义

### v1.x.x
- 基础抽屉功能
- 四方向支持
- 遮罩控制
