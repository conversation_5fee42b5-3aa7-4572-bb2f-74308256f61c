var e=Object.defineProperty,t=(t,l,o)=>(((t,l,o)=>{l in t?e(t,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[l]=o})(t,"symbol"!=typeof l?l+"":l,o),o);import{y as l,H as o,D as a,o as n,c as s,w as c,a as i,j as d,l as r,J as f,b as u,t as m,x as C,E as b,i as _,e as p,f as h,z as y,K as g,L as k,A as v,B as x,s as w,p as z,q as B,S}from"./index-ChUEiI3E.js";import{n as T,C as I}from"./index.DI3nc_37.js";import"./index.BbQFgwO5.js";import{_ as $}from"./_plugin-vue_export-helper.BCo6x5W8.js";const A=$(l({__name:"Modal",props:{visible:{type:Boolean,default:!1},title:{default:""},content:{default:""},type:{default:"default"},width:{default:"auto"},height:{default:"auto"},maxWidth:{default:"80%"},maxHeight:{default:"80%"},top:{default:"auto"},backgroundColor:{default:"#ffffff"},borderRadius:{default:12},maskClosable:{type:Boolean,default:!0},maskColor:{default:"#000000"},maskOpacity:{default:.5},titleColor:{default:"#333333"},titleSize:{default:18},titleAlign:{default:"center"},contentColor:{default:"#666666"},contentSize:{default:16},contentAlign:{default:"center"},showClose:{type:Boolean,default:!1},closeText:{default:"✕"},closeColor:{default:"#999999"},closeSize:{default:16},showFooter:{type:Boolean,default:!0},showCancel:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!0},cancelText:{default:"取消"},confirmText:{default:"确定"},cancelColor:{default:"#666666"},confirmColor:{default:"#ffffff"},cancelBgColor:{default:"#f8f9fa"},confirmBgColor:{default:"#007aff"},animation:{default:"scale"},animationDuration:{default:300},zIndex:{default:9999},safeAreaInsetBottom:{type:Boolean,default:!1},customClass:{default:""},customStyle:{default:()=>({})}},emits:["update:visible","open","close","cancel","confirm","maskClick"],setup(e,{emit:t}){const l=e,y=t;o((()=>l.visible),(e=>{y(e?"open":"close")}));const g=a((()=>["modal-overlay-base",`modal-animation-${l.animation}`,{"modal-visible":l.visible,"modal-safe-area":l.safeAreaInsetBottom},l.customClass])),k=a((()=>({zIndex:l.zIndex,animationDuration:`${l.animationDuration}ms`,...l.customStyle}))),v=a((()=>({backgroundColor:l.maskColor,opacity:l.maskOpacity}))),x=a((()=>["modal-container-base",`modal-type-${l.type}`])),w=a((()=>{const e={};return"auto"!==l.width&&(e.width="number"==typeof l.width?`${l.width}px`:l.width),"auto"!==l.height&&(e.height="number"==typeof l.height?`${l.height}px`:l.height),l.maxWidth&&(e.maxWidth="number"==typeof l.maxWidth?`${l.maxWidth}px`:l.maxWidth),l.maxHeight&&(e.maxHeight="number"==typeof l.maxHeight?`${l.maxHeight}px`:l.maxHeight),"auto"!==l.top&&(e.top="number"==typeof l.top?`${l.top}px`:l.top,e.transform="translateX(-50%)"),e})),z=a((()=>({backgroundColor:l.backgroundColor,borderRadius:"number"==typeof l.borderRadius?`${l.borderRadius}px`:l.borderRadius}))),B=a((()=>({}))),S=a((()=>({color:l.titleColor,fontSize:"number"==typeof l.titleSize?`${l.titleSize}px`:l.titleSize,textAlign:l.titleAlign}))),T=a((()=>({}))),I=a((()=>({}))),$=a((()=>({color:l.contentColor,fontSize:"number"==typeof l.contentSize?`${l.contentSize}px`:l.contentSize,textAlign:l.contentAlign}))),A=a((()=>({}))),H=a((()=>["btn-base","btn-cancel"])),W=a((()=>["btn-base","btn-confirm"])),M=a((()=>({color:l.cancelColor,backgroundColor:l.cancelBgColor,fontSize:"number"==typeof l.contentSize?`${l.contentSize}px`:l.contentSize}))),R=a((()=>({color:l.confirmColor,backgroundColor:l.confirmBgColor,fontSize:"number"==typeof l.contentSize?`${l.contentSize}px`:l.contentSize}))),j=()=>{y("maskClick"),l.maskClosable&&D()},D=()=>{y("update:visible",!1),y("close")},U=()=>{y("cancel"),D()},O=()=>{y("confirm"),D()};return(e,t)=>{const l=_,o=p,a=h;return e.visible?(n(),s(l,{key:0,class:r(["modal-overlay",g.value]),style:d(k.value),onClick:j},{default:c((()=>[i(l,{class:"modal-mask",style:d(v.value)},null,8,["style"]),i(l,{class:r(["modal-container",x.value]),style:d(w.value),onClick:t[0]||(t[0]=f((()=>{}),["stop"]))},{default:c((()=>[i(l,{class:"modal-content",style:d(z.value)},{default:c((()=>[e.title||e.showClose?(n(),s(l,{key:0,class:"modal-header",style:d(B.value)},{default:c((()=>[e.title?(n(),s(o,{key:0,class:"modal-title",style:d(S.value)},{default:c((()=>[u(m(e.title),1)])),_:1},8,["style"])):C("",!0),e.showClose?(n(),s(l,{key:1,class:"modal-close",style:d(T.value),onClick:D},{default:c((()=>[i(o,{class:"close-icon",style:d({color:e.closeColor})},{default:c((()=>[u(m(e.closeText),1)])),_:1},8,["style"])])),_:1},8,["style"])):C("",!0)])),_:1},8,["style"])):C("",!0),i(l,{class:"modal-body",style:d(I.value)},{default:c((()=>[b(e.$slots,"default",{},(()=>[e.content?(n(),s(o,{key:0,class:"modal-text",style:d($.value)},{default:c((()=>[u(m(e.content),1)])),_:1},8,["style"])):C("",!0)]),!0)])),_:3},8,["style"]),e.showFooter?(n(),s(l,{key:1,class:"modal-footer",style:d(A.value)},{default:c((()=>[b(e.$slots,"footer",{},(()=>[i(l,{class:"modal-buttons"},{default:c((()=>[e.showCancel?(n(),s(a,{key:0,class:r(["modal-btn cancel-btn",H.value]),style:d(M.value),onClick:U},{default:c((()=>[u(m(e.cancelText),1)])),_:1},8,["class","style"])):C("",!0),e.showConfirm?(n(),s(a,{key:1,class:r(["modal-btn confirm-btn",W.value]),style:d(R.value),onClick:O},{default:c((()=>[u(m(e.confirmText),1)])),_:1},8,["class","style"])):C("",!0)])),_:1})]),!0)])),_:3},8,["style"])):C("",!0)])),_:3},8,["style"])])),_:3},8,["class","style"])])),_:3},8,["class","style"])):C("",!0)}}}),[["__scopeId","data-v-ac9c3f5e"]]);const H=new class{constructor(){t(this,"instances",new Map),t(this,"zIndexCounter",9999)}createInstance(e){const t=`modal_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,l=y(!0),o=document.createElement("div");o.id=t,document.body.appendChild(o);const a=g({setup(){const o=async()=>{try{e.onConfirm&&await e.onConfirm()}catch(t){console.error("Modal confirm error:",t)}finally{n.close()}},a=async()=>{try{e.onCancel&&await e.onCancel()}catch(t){console.error("Modal cancel error:",t)}finally{n.close()}},s=async()=>{try{e.onClose&&await e.onClose()}catch(o){console.error("Modal close error:",o)}finally{l.value=!1,setTimeout((()=>{this.destroyInstance(t)}),300)}};return()=>k(A,{visible:l.value,title:e.title,content:e.content,type:e.type||"default",width:e.width,height:e.height,maskClosable:!1!==e.maskClosable,showClose:e.showClose||!1,showCancel:!1!==e.showCancel,showConfirm:!1!==e.showConfirm,cancelText:e.cancelText||"取消",confirmText:e.confirmText||"确定",cancelColor:e.cancelColor,confirmColor:e.confirmColor,cancelBgColor:e.cancelBgColor,confirmBgColor:e.confirmBgColor,animation:e.animation||"scale",zIndex:e.zIndex||this.zIndexCounter++,"onUpdate:visible":e=>{l.value=e,e||s()},onConfirm:o,onCancel:a,onClose:s})}});a.mount(o);const n={id:t,close:()=>{l.value=!1,setTimeout((()=>{this.destroyInstance(t)}),300)},update:t=>{Object.assign(e,t)}};return this.instances.set(t,{app:a,container:o,instance:n,config:e}),n}destroyInstance(e){const t=this.instances.get(e);if(t){const{app:l,container:o}=t;l.unmount(),o.parentNode&&o.parentNode.removeChild(o),this.instances.delete(e)}}show(e){return this.createInstance(e)}alert(e){const t="string"==typeof e?{content:e,type:"alert",showCancel:!1}:{...e,type:"alert",showCancel:!1};return this.createInstance(t)}confirm(e){const t="string"==typeof e?{content:e,type:"confirm"}:{...e,type:"confirm"};return this.createInstance(t)}prompt(e){return this.createInstance({...e,type:"prompt"})}closeAll(){this.instances.forEach((e=>{e.instance.close()}))}getCount(){return this.instances.size}},W=e=>H.show(e),M=e=>H.alert(e),R=e=>H.confirm(e),j=()=>H.closeAll(),D=()=>H.getCount(),U=$(l({__name:"modal-demo",setup(e){const t=y(0),l=y(0),o=y(!1),a=y(!1),r=y(!1),f=y(!1);v((()=>{t.value=T.getTotalHeight(),C()}));const C=()=>{l.value=D()},b=()=>{o.value=!0},g=()=>{a.value=!0},k=()=>{r.value=!0},$=()=>{f.value=!0},H=()=>{w({title:"点击了确定",icon:"none"})},U=()=>{w({title:"点击了取消",icon:"none"})},O=()=>{w({title:"知道了",icon:"none"})},E=()=>{w({title:"确认操作",icon:"success"})},F=()=>{w({title:"取消操作",icon:"none"})},q=()=>{M({title:"服务警告",content:"这是通过 ModalService 调用的警告弹窗",onConfirm:()=>{w({title:"服务警告确认",icon:"none"})}}),C()},N=()=>{R({title:"服务确认",content:"确定要通过服务调用执行此操作吗？",onConfirm:()=>{w({title:"服务确认",icon:"success"})},onCancel:()=>{w({title:"服务取消",icon:"none"})}}),C()},J=()=>{W({title:"操作成功",content:"🎉 恭喜！操作已成功完成。",type:"alert",showCancel:!1,confirmText:"太好了",confirmBgColor:"#4cd964",onConfirm:()=>{w({title:"成功确认",icon:"success"})}}),C()},K=()=>{W({title:"操作失败",content:"❌ 抱歉，操作执行失败，请重试。",type:"alert",showCancel:!1,confirmText:"重试",confirmBgColor:"#ff3b30",onConfirm:()=>{w({title:"准备重试",icon:"none"})}}),C()},L=()=>{W({title:"自定义样式",content:"这是一个自定义样式的弹窗，具有特殊的颜色和圆角。",backgroundColor:"#f0f8ff",titleColor:"#007aff",contentColor:"#333333",borderRadius:20,confirmBgColor:"#007aff",cancelBgColor:"#f8f9fa"}),C()},P=()=>{W({title:"圆角弹窗",content:"这是一个具有大圆角的弹窗。",borderRadius:30,animation:"scale"}),C()},X=()=>{W({title:"彩色主题",content:"这是一个彩色主题的弹窗。",backgroundColor:"#667eea",titleColor:"#ffffff",contentColor:"#ffffff",confirmBgColor:"#ffffff",confirmColor:"#667eea",cancelBgColor:"rgba(255, 255, 255, 0.2)",cancelColor:"#ffffff"}),C()},G=()=>{W({title:"大尺寸弹窗",content:"这是一个大尺寸的弹窗，可以容纳更多内容。您可以在这里放置更多的信息、表单或其他复杂的内容。",width:600,height:400,maxWidth:"95%"}),C()},Q=()=>{W({title:"淡入淡出",content:"这个弹窗使用淡入淡出动画。",animation:"fade"}),C()},V=()=>{W({title:"缩放动画",content:"这个弹窗使用缩放动画。",animation:"scale"}),C()},Y=()=>{W({title:"上滑动画",content:"这个弹窗使用上滑动画。",animation:"slide-up"}),C()},Z=()=>{W({title:"下滑动画",content:"这个弹窗使用下滑动画。",animation:"slide-down"}),C()},ee=()=>{W({title:"顶部弹窗",content:"这个弹窗显示在页面顶部。",top:100,animation:"slide-down"}),C()},te=()=>{W({title:"居中弹窗",content:"这个弹窗显示在页面中央。",animation:"scale"}),C()},le=()=>{W({title:"底部弹窗",content:"这个弹窗显示在页面底部。",top:"auto",animation:"slide-up"}),C()},oe=()=>{W({title:"全屏弹窗",content:"这是一个全屏显示的弹窗，占据整个屏幕空间。",width:"100%",height:"100%",maxWidth:"100%",maxHeight:"100%",borderRadius:0}),C()},ae=()=>{W({title:"点击遮罩关闭",content:"点击弹窗外的遮罩区域可以关闭弹窗。",maskClosable:!0}),C()},ne=()=>{W({title:"禁止遮罩关闭",content:"点击弹窗外的遮罩区域无法关闭弹窗，只能通过按钮关闭。",maskClosable:!1}),C()},se=()=>{W({title:"带关闭按钮",content:"这个弹窗在右上角显示关闭按钮。",showClose:!0}),C()},ce=()=>{W({title:"无底部按钮",content:"这个弹窗没有底部按钮，只能通过右上角关闭。",showFooter:!1,showClose:!0}),C()},ie=()=>{R({title:"⚠️ 删除确认",content:"确定要删除这个文件吗？删除后无法恢复。",confirmText:"删除",confirmBgColor:"#ff3b30",onConfirm:()=>{w({title:"文件已删除",icon:"success"})}}),C()},de=()=>{R({title:"📝 提交确认",content:"确定要提交表单吗？提交后将无法修改。",confirmText:"提交",confirmBgColor:"#007aff",onConfirm:()=>{z({title:"提交中..."}),setTimeout((()=>{B(),w({title:"提交成功",icon:"success"})}),2e3)}}),C()},re=()=>{const e=Math.random()>.5;M(e?{title:"✅ 操作成功",content:"恭喜！操作已成功完成，您可以继续下一步操作。",confirmBgColor:"#4cd964"}:{title:"❌ 操作失败",content:"抱歉，操作执行失败，请检查网络连接后重试。",confirmBgColor:"#ff3b30"}),C()},fe=()=>{W({title:"用户服务协议",content:"\n欢迎使用我们的服务！\n\n1. 服务条款\n本协议是您与我们之间关于使用服务的法律协议。\n\n2. 用户责任\n您需要对自己的账户和密码安全负责。\n\n3. 隐私保护\n我们承诺保护您的个人隐私信息。\n\n4. 服务变更\n我们保留随时修改或终止服务的权利。\n\n5. 法律适用\n本协议受相关法律法规约束。\n\n感谢您的理解与配合！\n    ",width:500,height:400,maxWidth:"95%",contentAlign:"left",confirmText:"同意",cancelText:"拒绝"}),C()},ue=()=>{setTimeout((()=>{M("第一个弹窗"),C()}),100),setTimeout((()=>{M("第二个弹窗"),C()}),200),setTimeout((()=>{M("第三个弹窗"),C()}),300)},me=()=>{j(),C(),w({title:"已关闭所有弹窗",icon:"none"})};return(e,C)=>{const y=p,v=h,w=_,z=S;return n(),s(w,{class:"page"},{default:c((()=>[i(x(I),{title:"弹窗组件演示","show-back":!0}),i(z,{"scroll-y":"",class:"content",style:d({paddingTop:t.value+"px"})},{default:c((()=>[i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("📝 基础用法")])),_:1}),i(w,{class:"button-grid"},{default:c((()=>[i(v,{onClick:b,class:"demo-btn"},{default:c((()=>[u("基础弹窗")])),_:1}),i(v,{onClick:g,class:"demo-btn"},{default:c((()=>[u("警告弹窗")])),_:1}),i(v,{onClick:k,class:"demo-btn"},{default:c((()=>[u("确认弹窗")])),_:1}),i(v,{onClick:$,class:"demo-btn"},{default:c((()=>[u("自定义内容")])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("🚀 程序化调用")])),_:1}),i(w,{class:"button-grid"},{default:c((()=>[i(v,{onClick:q,class:"demo-btn primary"},{default:c((()=>[u("服务警告")])),_:1}),i(v,{onClick:N,class:"demo-btn success"},{default:c((()=>[u("服务确认")])),_:1}),i(v,{onClick:J,class:"demo-btn success"},{default:c((()=>[u("成功提示")])),_:1}),i(v,{onClick:K,class:"demo-btn danger"},{default:c((()=>[u("错误提示")])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("🎨 样式定制")])),_:1}),i(w,{class:"button-grid"},{default:c((()=>[i(v,{onClick:L,class:"demo-btn"},{default:c((()=>[u("自定义样式")])),_:1}),i(v,{onClick:P,class:"demo-btn"},{default:c((()=>[u("圆角弹窗")])),_:1}),i(v,{onClick:X,class:"demo-btn"},{default:c((()=>[u("彩色主题")])),_:1}),i(v,{onClick:G,class:"demo-btn"},{default:c((()=>[u("大尺寸")])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("✨ 动画效果")])),_:1}),i(w,{class:"button-grid"},{default:c((()=>[i(v,{onClick:Q,class:"demo-btn"},{default:c((()=>[u("淡入淡出")])),_:1}),i(v,{onClick:V,class:"demo-btn"},{default:c((()=>[u("缩放动画")])),_:1}),i(v,{onClick:Y,class:"demo-btn"},{default:c((()=>[u("上滑动画")])),_:1}),i(v,{onClick:Z,class:"demo-btn"},{default:c((()=>[u("下滑动画")])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("📍 位置控制")])),_:1}),i(w,{class:"button-grid"},{default:c((()=>[i(v,{onClick:ee,class:"demo-btn"},{default:c((()=>[u("顶部弹窗")])),_:1}),i(v,{onClick:te,class:"demo-btn"},{default:c((()=>[u("居中弹窗")])),_:1}),i(v,{onClick:le,class:"demo-btn"},{default:c((()=>[u("底部弹窗")])),_:1}),i(v,{onClick:oe,class:"demo-btn"},{default:c((()=>[u("全屏弹窗")])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("🎛️ 交互控制")])),_:1}),i(w,{class:"button-grid"},{default:c((()=>[i(v,{onClick:ae,class:"demo-btn"},{default:c((()=>[u("点击遮罩关闭")])),_:1}),i(v,{onClick:ne,class:"demo-btn"},{default:c((()=>[u("禁止遮罩关闭")])),_:1}),i(v,{onClick:se,class:"demo-btn"},{default:c((()=>[u("显示关闭按钮")])),_:1}),i(v,{onClick:ce,class:"demo-btn"},{default:c((()=>[u("无底部按钮")])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("🛠️ 应用场景")])),_:1}),i(w,{class:"scenario-grid"},{default:c((()=>[i(w,{class:"scenario-card"},{default:c((()=>[i(y,{class:"card-title"},{default:c((()=>[u("删除确认")])),_:1}),i(y,{class:"card-desc"},{default:c((()=>[u("删除重要数据前的确认")])),_:1}),i(v,{onClick:ie,class:"scenario-btn danger"},{default:c((()=>[u("删除文件")])),_:1})])),_:1}),i(w,{class:"scenario-card"},{default:c((()=>[i(y,{class:"card-title"},{default:c((()=>[u("表单提交")])),_:1}),i(y,{class:"card-desc"},{default:c((()=>[u("提交表单前的确认")])),_:1}),i(v,{onClick:de,class:"scenario-btn primary"},{default:c((()=>[u("提交表单")])),_:1})])),_:1}),i(w,{class:"scenario-card"},{default:c((()=>[i(y,{class:"card-title"},{default:c((()=>[u("操作结果")])),_:1}),i(y,{class:"card-desc"},{default:c((()=>[u("显示操作成功或失败")])),_:1}),i(v,{onClick:re,class:"scenario-btn success"},{default:c((()=>[u("执行操作")])),_:1})])),_:1}),i(w,{class:"scenario-card"},{default:c((()=>[i(y,{class:"card-title"},{default:c((()=>[u("用户协议")])),_:1}),i(y,{class:"card-desc"},{default:c((()=>[u("显示长文本内容")])),_:1}),i(v,{onClick:fe,class:"scenario-btn"},{default:c((()=>[u("查看协议")])),_:1})])),_:1})])),_:1})])),_:1}),i(w,{class:"demo-section"},{default:c((()=>[i(y,{class:"section-title"},{default:c((()=>[u("🔧 批量操作")])),_:1}),i(w,{class:"batch-controls"},{default:c((()=>[i(v,{onClick:ue,class:"demo-btn"},{default:c((()=>[u("显示多个弹窗")])),_:1}),i(v,{onClick:me,class:"demo-btn secondary"},{default:c((()=>[u("关闭所有弹窗")])),_:1}),i(y,{class:"modal-count"},{default:c((()=>[u("当前弹窗数量: "+m(l.value),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]),i(x(A),{visible:o.value,"onUpdate:visible":C[0]||(C[0]=e=>o.value=e),title:"基础弹窗",content:"这是一个基础的弹窗示例，包含标题、内容和底部按钮。",onConfirm:H,onCancel:U},null,8,["visible"]),i(x(A),{visible:a.value,"onUpdate:visible":C[1]||(C[1]=e=>a.value=e),title:"警告",content:"这是一个警告信息，请仔细阅读。",type:"alert","show-cancel":!1,"confirm-text":"知道了","confirm-bg-color":"#ff9500",onConfirm:O},null,8,["visible"]),i(x(A),{visible:r.value,"onUpdate:visible":C[2]||(C[2]=e=>r.value=e),title:"确认操作",content:"确定要执行此操作吗？此操作不可撤销。",type:"confirm",onConfirm:E,onCancel:F},null,8,["visible"]),i(x(A),{visible:f.value,"onUpdate:visible":C[4]||(C[4]=e=>f.value=e),title:"自定义内容","show-footer":!1,"show-close":!0},{default:c((()=>[i(w,{class:"custom-content"},{default:c((()=>[i(y,{class:"custom-title"},{default:c((()=>[u("🎉 恭喜您！")])),_:1}),i(y,{class:"custom-text"},{default:c((()=>[u("您已成功完成所有任务，获得了以下奖励：")])),_:1}),i(w,{class:"reward-list"},{default:c((()=>[i(w,{class:"reward-item"},{default:c((()=>[i(y,{class:"reward-icon"},{default:c((()=>[u("🏆")])),_:1}),i(y,{class:"reward-text"},{default:c((()=>[u("成就徽章 x1")])),_:1})])),_:1}),i(w,{class:"reward-item"},{default:c((()=>[i(y,{class:"reward-icon"},{default:c((()=>[u("💰")])),_:1}),i(y,{class:"reward-text"},{default:c((()=>[u("金币 x100")])),_:1})])),_:1}),i(w,{class:"reward-item"},{default:c((()=>[i(y,{class:"reward-icon"},{default:c((()=>[u("⭐")])),_:1}),i(y,{class:"reward-text"},{default:c((()=>[u("经验值 x50")])),_:1})])),_:1})])),_:1}),i(v,{onClick:C[3]||(C[3]=e=>f.value=!1),class:"custom-btn"},{default:c((()=>[u("领取奖励")])),_:1})])),_:1})])),_:1},8,["visible"])])),_:1})}}}),[["__scopeId","data-v-8084a3a1"]]);export{U as default};
