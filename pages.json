{"pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "uni-app"}}, {"path": "pages/ts-learn/index", "style": {"navigationBarTitleText": "TypeScript 学习"}}, {"path": "pages/ts-learn/basic-types", "style": {"navigationBarTitleText": "TypeScript 基础类型"}}, {"path": "pages/ts-learn/interfaces", "style": {"navigationBarTitleText": "TypeScript 接口 (Interface)"}}, {"path": "pages/ts-learn/type-aliases", "style": {"navigationBarTitleText": "TypeScript 类型别名 (Type)"}}, {"path": "pages/ts-learn/functions", "style": {"navigationBarTitleText": "TypeScript 函数类型"}}, {"path": "pages/ts-learn/classes", "style": {"navigationBarTitleText": "TypeScript 类和继承"}}, {"path": "pages/ts-learn/modules", "style": {"navigationBarTitleText": "TypeScript 模块和命名空间"}}, {"path": "pages/ts-learn/generics", "style": {"navigationBarTitleText": "TypeScript 泛型 (Generics)"}}, {"path": "pages/ts-learn/advanced-types", "style": {"navigationBarTitleText": "TypeScript 高级类型"}}, {"path": "pages/typescript-demo/typescript-demo", "style": {"navigationBarTitleText": "TypeScript 示例"}}, {"path": "pages/skeleton-demo/skeleton-demo", "style": {"navigationBarTitleText": "骨架屏演示", "navigationStyle": "custom"}}, {"path": "pages/skeleton-simple/skeleton-simple", "style": {"navigationBarTitleText": "骨架屏简单演示", "navigationStyle": "custom"}}, {"path": "pages/icon-demo/icon-demo", "style": {"navigationBarTitleText": "图标组件演示", "navigationStyle": "custom"}}, {"path": "pages/numberbox-demo/numberbox-demo", "style": {"navigationBarTitleText": "数字框组件演示", "navigationStyle": "custom"}}, {"path": "pages/modal-demo/modal-demo", "style": {"navigationBarTitleText": "弹窗组件演示", "navigationStyle": "custom"}}, {"path": "pages/tabs-demo/tabs-demo", "style": {"navigationBarTitleText": "标签页组件演示", "navigationStyle": "custom"}}, {"path": "pages/countdown-demo/countdown-demo", "style": {"navigationBarTitleText": "倒计时组件演示", "navigationStyle": "custom"}}, {"path": "pages/bottom-popup-demo/bottom-popup-demo", "style": {"navigationBarTitleText": "底部弹窗组件演示", "navigationStyle": "custom"}}, {"path": "pages/cascade-selection-demo/cascade-selection-demo", "style": {"navigationBarTitleText": "级联选择器组件演示", "navigationStyle": "custom"}}, {"path": "pages/picker-address-demo/picker-address-demo", "style": {"navigationBarTitleText": "城市选择器组件演示", "navigationStyle": "custom"}}, {"path": "pages/verification-code-demo/verification-code-demo", "style": {"navigationBarTitleText": "验证码按钮组件演示", "navigationStyle": "custom"}}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "uniIdRouter": {}}