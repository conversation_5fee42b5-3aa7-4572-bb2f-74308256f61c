import{m as e,n as t,h as s,g as a,c as l,w as n,i as r,o as c,a as o,b as i,f as d,e as u,S as p}from"./index-ChUEiI3E.js";import{_ as f}from"./_plugin-vue_export-helper.BCo6x5W8.js";const _=f({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/interfaces"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/functions"})},markAsCompleted(){try{let e=s("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("type-aliases")||(e.completedItems.push("type-aliases"),e.lastUpdate=Date.now(),a("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("类型别名课程加载完成")}},[["render",function(e,t,s,a,f,_){const m=d,y=u,g=r,x=p;return c(),l(g,{class:"container"},{default:n((()=>[o(g,{class:"header"},{default:n((()=>[o(m,{onClick:_.goBack,class:"back-btn"},{default:n((()=>[i("← 返回")])),_:1},8,["onClick"]),o(y,{class:"title"},{default:n((()=>[i("类型别名 (Type)")])),_:1}),o(g,{class:"progress-indicator"},{default:n((()=>[i("3/9")])),_:1})])),_:1}),o(x,{"scroll-y":"",class:"content"},{default:n((()=>[o(g,{class:"lesson-intro"},{default:n((()=>[o(y,{class:"intro-title"},{default:n((()=>[i("学习目标")])),_:1}),o(y,{class:"intro-text"},{default:n((()=>[i("掌握 type 关键字的使用，理解联合类型、交叉类型等高级类型概念")])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("🏷️ 基本类型别名")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i('// 基本类型别名 type UserID = number type UserName = string type IsActive = boolean // 使用类型别名 let userId: UserID = 12345 let userName: UserName = "张三" let isActive: IsActive = true // 复杂类型别名 type Point = { x: number y: number } type User = { id: UserID name: UserName position: Point }')])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• type 关键字用于创建类型别名")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 可以为任何类型创建别名")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 提高代码的可读性和维护性")])),_:1})])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("🔗 联合类型 (Union Types)")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i('// 联合类型：可以是多种类型中的一种 type Status = "pending" | "success" | "error" type ID = string | number // 使用联合类型 let currentStatus: Status = "pending" let userId: ID = 123 let userCode: ID = "USER_001" // 函数参数使用联合类型 function formatId(id: ID): string { if (typeof id === "string") { return id.toUpperCase() } else { return `ID_${id}` } } // 更复杂的联合类型 type Theme = "light" | "dark" | "auto" type Size = "small" | "medium" | "large" type ButtonVariant = "primary" | "secondary" | "danger"')])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• 使用 | 符号创建联合类型")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 值可以是联合类型中的任意一种")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 常用于限制值的范围")])),_:1})])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("✖️ 交叉类型 (Intersection Types)")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i('// 交叉类型：同时具有多种类型的特性 type Person = { name: string age: number } type Employee = { employeeId: string department: string } // 交叉类型 type Staff = Person & Employee // 使用交叉类型 const staff: Staff = { name: "张三", age: 30, employeeId: "EMP001", department: "技术部" } // 函数类型的交叉 type Logger = { log: (message: string) => void } type Timer = { start: () => void stop: () => void } type LoggerTimer = Logger & Timer')])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• 使用 & 符号创建交叉类型")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 必须同时满足所有类型的要求")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 常用于组合多个类型")])),_:1})])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("📝 字面量类型")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i('// 字符串字面量类型 type Direction = "up" | "down" | "left" | "right" type HttpMethod = "GET" | "POST" | "PUT" | "DELETE" // 数字字面量类型 type DiceRoll = 1 | 2 | 3 | 4 | 5 | 6 type Port = 80 | 443 | 3000 | 8080 // 布尔字面量类型 type Success = true type Failure = false // 混合字面量类型 type Config = { env: "development" | "production" | "test" port: 3000 | 8080 debug: true | false } // 使用字面量类型 function move(direction: Direction): void { console.log(`向${direction}移动`) } move("up") // OK // move("forward") // Error: 不在允许的值范围内')])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• 字面量类型限制值为特定的字面量")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 提供更精确的类型约束")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 常与联合类型结合使用")])),_:1})])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("❓ 条件类型")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i("// 条件类型：根据条件选择类型 type IsString<T> = T extends string ? true : false type Test1 = IsString<string> // true type Test2 = IsString<number> // false // 实用的条件类型 type NonNullable<T> = T extends null | undefined ? never : T type Example1 = NonNullable<string | null> // string type Example2 = NonNullable<number | undefined> // number // 提取函数返回类型 type ReturnType<T> = T extends (...args: any[]) => infer R ? R : never type FuncReturn = ReturnType<() => string> // string")])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• 使用 extends 和 ? : 语法创建条件类型")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 根据类型条件选择不同的类型")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 常用于创建工具类型")])),_:1})])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("🗺️ 映射类型")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i('// 映射类型：基于现有类型创建新类型 type User = { id: number name: string email: string } // 将所有属性变为可选 type PartialUser = { [K in keyof User]?: User[K] } // 将所有属性变为只读 type ReadonlyUser = { readonly [K in keyof User]: User[K] } // 选择特定属性 type UserBasic = { [K in "id" | "name"]: User[K] } // 等价于： // type UserBasic = { // id: number // name: string // }')])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• 使用 in 关键字遍历类型的键")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 可以修改属性的修饰符")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 常用于创建变体类型")])),_:1})])),_:1})])),_:1}),o(g,{class:"section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("⚖️ Type vs Interface")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i("// Interface 的特点 interface UserInterface { name: string age: number } // 可以扩展 interface UserInterface { email: string // 自动合并 } // Type 的特点 type UserType = { name: string age: number } // 不能重复声明 // type UserType = { // Error: 重复标识符 // email: string // } // Type 可以表示联合类型 type StringOrNumber = string | number // Interface 不能表示联合类型 // interface StringOrNumber = string | number // Error // 选择建议： // - 定义对象结构时，优先使用 interface // - 需要联合类型、条件类型时，使用 type // - 需要声明合并时，使用 interface")])),_:1})])),_:1}),o(g,{class:"explanation"},{default:n((()=>[o(y,{class:"exp-text"},{default:n((()=>[i("• Interface 支持声明合并，Type 不支持")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• Type 支持联合类型，Interface 不支持")])),_:1}),o(y,{class:"exp-text"},{default:n((()=>[i("• 根据使用场景选择合适的方式")])),_:1})])),_:1})])),_:1}),o(g,{class:"practice-section"},{default:n((()=>[o(y,{class:"section-title"},{default:n((()=>[i("🎯 实践练习")])),_:1}),o(g,{class:"practice-item"},{default:n((()=>[o(y,{class:"practice-title"},{default:n((()=>[i("练习 1：创建联合类型")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i('// 创建以下类型别名： // 1. OrderStatus: "pending" | "processing" | "shipped" | "delivered" // 2. PaymentMethod: "credit_card" | "paypal" | "bank_transfer" // 3. Priority: "low" | "medium" | "high" | "urgent"')])),_:1})])),_:1})])),_:1}),o(g,{class:"practice-item"},{default:n((()=>[o(y,{class:"practice-title"},{default:n((()=>[i("练习 2：交叉类型应用")])),_:1}),o(g,{class:"code-block"},{default:n((()=>[o(y,{class:"code"},{default:n((()=>[i("// 定义以下类型并创建交叉类型： // BaseProduct: { id: number, name: string, price: number } // Inventory: { stock: number, warehouse: string } // ProductWithInventory: BaseProduct & Inventory")])),_:1})])),_:1})])),_:1})])),_:1}),o(g,{class:"navigation"},{default:n((()=>[o(m,{onClick:_.previousLesson,class:"nav-btn secondary"},{default:n((()=>[i("上一课：接口")])),_:1},8,["onClick"]),o(m,{onClick:_.nextLesson,class:"nav-btn primary"},{default:n((()=>[i("下一课：函数类型")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-51ad8042"]]);export{_ as default};
