function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-index-index.CWcnUenF.js","assets/_plugin-vue_export-helper.BCo6x5W8.js","assets/index-6FgJt302.css","assets/pages-ts-learn-index.TXpaEBNF.js","assets/index-DFrdlxOY.css","assets/pages-ts-learn-basic-types.tDZvBkRi.js","assets/basic-types-n8SDwGA6.css","assets/pages-ts-learn-interfaces.B1Gz-s19.js","assets/interfaces-DpM8MSW5.css","assets/pages-ts-learn-type-aliases.neeDtytv.js","assets/type-aliases-911_2nHg.css","assets/pages-ts-learn-functions.CkJ1mnVc.js","assets/functions-BWA_zLzi.css","assets/pages-ts-learn-classes.6kEubYPL.js","assets/classes-BbLXqEou.css","assets/pages-ts-learn-modules.CPGBF_Gk.js","assets/modules-BZFmQ0Jo.css","assets/pages-ts-learn-generics.GDySUqhN.js","assets/generics-DyvKJMF-.css","assets/pages-ts-learn-advanced-types.Cq59EIzT.js","assets/advanced-types-5Gx8yczh.css","assets/pages-typescript-demo-typescript-demo.B8AdqGkL.js","assets/typescript-demo-BDKEqx7N.css","assets/pages-skeleton-demo-skeleton-demo.sFaOXvF9.js","assets/index.B_fb1lzg.js","assets/index-_cHCZ3ZT.css","assets/index.DI3nc_37.js","assets/index-CTHM763Q.css","assets/index.BAv8G1Ca.js","assets/index-CX24sb_k.css","assets/index.BbQFgwO5.js","assets/index-A93wR-kD.css","assets/index.4kChqqAX.js","assets/index-BFMy8reL.css","assets/index.Dvz5gvUX.js","assets/index-CDvVe6OD.css","assets/index._qKjp6eZ.js","assets/index-BQdHBoXa.css","assets/skeleton-demo-ly5UtTII.css","assets/IconFont-CaGhKUp8.css","assets/CascadeSelection-Cr5fp2yL.css","assets/PickerAddress-DQDlsTvD.css","assets/VerificationCodeButton-B3xKbSBp.css","assets/pages-skeleton-simple-skeleton-simple.D9VVP8gy.js","assets/skeleton-simple-Bdf_q2r6.css","assets/pages-icon-demo-icon-demo.p2djDtHl.js","assets/Icon.DLnR9vuk.js","assets/icon-demo-K7zNbrwy.css","assets/pages-numberbox-demo-numberbox-demo.VF4TlLx8.js","assets/product.q7Ixu4ET.js","assets/numberbox-demo-DzuRocRc.css","assets/pages-modal-demo-modal-demo.COyPpbkd.js","assets/modal-demo-B2uPIOmv.css","assets/pages-tabs-demo-tabs-demo.CIS-o_CS.js","assets/tabs-demo-U89rPYpY.css","assets/pages-countdown-demo-countdown-demo.wz5ch5Vu.js","assets/countdown-demo-CuZ9mjeh.css","assets/pages-bottom-popup-demo-bottom-popup-demo.CI2rcCcV.js","assets/BottomPopup.gFUAg0m5.js","assets/bottom-popup-demo-BJll6pHQ.css","assets/pages-cascade-selection-demo-cascade-selection-demo.CDmwY9tF.js","assets/CascadeSelection.D80cIVcX.js","assets/cascade-selection-demo-DRtKqpkM.css","assets/pages-picker-address-demo-picker-address-demo.QQpE74kd.js","assets/picker-address-demo-C6DaoyGr.css","assets/pages-verification-code-demo-verification-code-demo.aoapn5lJ.js","assets/verification-code-demo-DlTY0U1r.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){const t=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(n.map((n=>{if((n=function(e){return"/"+e}(n))in e)return;e[n]=!0;const r=n.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let e=t.length-1;e>=0;e--){const o=t[e];if(o.href===n&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=n,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,t)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${n}`))))})):void 0})))}return r.then((()=>t())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const o={},r=[],i=()=>{},s=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,p=(e,t)=>d.call(e,t),f=Array.isArray,h=e=>"[object Map]"===x(e),g=e=>"[object Set]"===x(e),m=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,b=e=>(_(e)||m(e))&&m(e.then)&&m(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>"[object Object]"===x(e),T=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),k=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,O=k((e=>e.replace(E,((e,t)=>t?t.toUpperCase():"")))),L=/\B([A-Z])/g,$=k((e=>e.replace(L,"-$1").toLowerCase())),M=k((e=>e.charAt(0).toUpperCase()+e.slice(1))),A=k((e=>e?`on${M(e)}`:"")),P=(e,t)=>!Object.is(e,t),B=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},R=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},I=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let N;const j=()=>N||(N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function V(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?q(o):V(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||_(e))return e}const D=/;(?![^(]*\))/g,H=/:([^]+)/,F=/\/\*[^]*?\*\//g;function q(e){const t={};return e.replace(F,"").split(D).forEach((e=>{if(e){const n=e.split(H);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function W(e){let t="";if(v(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const o=W(e[n]);o&&(t+=o+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const z=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function U(e){return!!e||""===e}const Y=e=>v(e)?e:null==e?"":f(e)||_(e)&&(e.toString===w||!m(e.toString))?JSON.stringify(e,X,2):String(e),X=(e,t)=>t&&t.__v_isRef?X(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[K(t,o)+" =>"]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>K(e)))}:y(t)?K(t):!_(t)||f(t)||S(t)?t:String(t),K=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},G=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),J=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),Z=["list-item"].map((e=>"uni-"+e));function Q(e){if(-1!==Z.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==G.indexOf(t)||-1!==J.indexOf(t)}const ee=/^([a-z-]+:)?\/\//i,te=/^data:.*,.*/;function ne(e){return 0===e.indexOf("/")}function oe(e){return ne(e)?e:"/"+e}function re(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}let ie;function se(){return ie||(ie=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),ie)}function ae(e){if(!e)return;let t=e.type.name;for(;t&&Q($(t));)t=(e=e.parent).type.name;return e.proxy}function le(e){return 1===e.nodeType}function ce(e){const t=se();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),V(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),V(t)}if(v(e))return q(e);if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?q(o):ce(o);if(r)for(const e in r)t[e]=r[e]}return t}return V(e)}function ue(e){let t="";const n=se();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(f(e))for(let o=0;o<e.length;o++){const n=ue(e[o]);n&&(t+=n+" ")}else t=W(e);return t.trim()}function de(e){return O(e.substring(5))}const pe=re((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[de(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[de(t)],o.call(this,t)}}));function fe(e){return c({},e.dataset,e.__uniDataset)}const he=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function ge(e){return{passive:e}}function me(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:fe(e),offsetTop:n,offsetLeft:o}}function ve(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function ye(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=ve(e[n])}catch(o){t[n]=e[n]}})),t}const _e=/\+/g;function be(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(_e," ");let r=e.indexOf("="),i=ve(r<0?e:e.slice(0,r)),s=r<0?null:ve(e.slice(r+1));if(i in t){let e=t[i];f(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function we(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class xe{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Se=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Te=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Ce=[];const ke=re(((e,t)=>t(e))),Ee=function(){};Ee.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Oe=Ee;const Le={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function $e(e,t,n){if(v(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Le?Le[o]:o}return r}var o;return t}function Me(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=S(s)?Me(s,t,n):f(s)?s.map((e=>S(e)?Me(e,t,n):$e(o,e))):$e(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ae,Pe;class Be{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ae,!e&&Ae&&(this.index=(Ae.scopes||(Ae.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Ae;try{return Ae=this,e()}finally{Ae=t}}}on(){Ae=this}off(){Ae=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function Re(e){return new Be(e)}class Ie{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=Ae){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,qe();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),We()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=De,t=Pe;try{return De=!0,Pe=this,this._runnings++,Ne(this),this.fn()}finally{je(this),this._runnings--,Pe=t,De=e}}stop(){var e;this.active&&(Ne(this),je(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Ne(e){e._trackId++,e._depsLength=0}function je(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Ve(e.deps[t],e);e.deps.length=e._depsLength}}function Ve(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let De=!0,He=0;const Fe=[];function qe(){Fe.push(De),De=!1}function We(){const e=Fe.pop();De=void 0===e||e}function ze(){He++}function Ue(){for(He--;!He&&Xe.length;)Xe.shift()()}function Ye(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Ve(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Xe=[];function Ke(e,t,n){ze();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Xe.push(o.scheduler)))}Ue()}const Ge=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},Je=new WeakMap,Ze=Symbol(""),Qe=Symbol("");function et(e,t,n){if(De&&Pe){let t=Je.get(e);t||Je.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Ge((()=>t.delete(n)))),Ye(Pe,o)}}function tt(e,t,n,o,r,i){const s=Je.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&f(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!y(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":f(e)?T(n)&&a.push(s.get("length")):(a.push(s.get(Ze)),h(e)&&a.push(s.get(Qe)));break;case"delete":f(e)||(a.push(s.get(Ze)),h(e)&&a.push(s.get(Qe)));break;case"set":h(e)&&a.push(s.get(Ze))}ze();for(const l of a)l&&Ke(l,4);Ue()}const nt=n("__proto__,__v_isRef,__isVue"),ot=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),rt=it();function it(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Yt(this);for(let t=0,r=this.length;t<r;t++)et(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Yt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){qe(),ze();const n=Yt(this)[t].apply(this,e);return Ue(),We(),n}})),e}function st(e){const t=Yt(this);return et(t,0,e),t.hasOwnProperty(e)}class at{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Nt:It:r?Rt:Bt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=f(e);if(!o){if(i&&p(rt,t))return Reflect.get(rt,t,n);if("hasOwnProperty"===t)return st}const s=Reflect.get(e,t,n);return(y(t)?ot.has(t):nt(t))?s:(o||et(e,0,t),r?s:en(s)?i&&T(t)?s:s.value:_(s)?o?Ht(s):Vt(s):s)}}class lt extends at{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Wt(r);if(zt(n)||Wt(n)||(r=Yt(r),n=Yt(n)),!f(e)&&en(r)&&!en(n))return!t&&(r.value=n,!0)}const i=f(e)&&T(t)?Number(t)<e.length:p(e,t),s=Reflect.set(e,t,n,o);return e===Yt(o)&&(i?P(n,r)&&tt(e,"set",t,n):tt(e,"add",t,n)),s}deleteProperty(e,t){const n=p(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&tt(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return y(t)&&ot.has(t)||et(e,0,t),n}ownKeys(e){return et(e,0,f(e)?"length":Ze),Reflect.ownKeys(e)}}class ct extends at{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const ut=new lt,dt=new ct,pt=new lt(!0),ft=e=>e,ht=e=>Reflect.getPrototypeOf(e);function gt(e,t,n=!1,o=!1){const r=Yt(e=e.__v_raw),i=Yt(t);n||(P(t,i)&&et(r,0,t),et(r,0,i));const{has:s}=ht(r),a=o?ft:n?Gt:Kt;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function mt(e,t=!1){const n=this.__v_raw,o=Yt(n),r=Yt(e);return t||(P(e,r)&&et(o,0,e),et(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function vt(e,t=!1){return e=e.__v_raw,!t&&et(Yt(e),0,Ze),Reflect.get(e,"size",e)}function yt(e){e=Yt(e);const t=Yt(this);return ht(t).has.call(t,e)||(t.add(e),tt(t,"add",e,e)),this}function _t(e,t){t=Yt(t);const n=Yt(this),{has:o,get:r}=ht(n);let i=o.call(n,e);i||(e=Yt(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?P(t,s)&&tt(n,"set",e,t):tt(n,"add",e,t),this}function bt(e){const t=Yt(this),{has:n,get:o}=ht(t);let r=n.call(t,e);r||(e=Yt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&tt(t,"delete",e,void 0),i}function wt(){const e=Yt(this),t=0!==e.size,n=e.clear();return t&&tt(e,"clear",void 0,void 0),n}function xt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Yt(i),a=t?ft:e?Gt:Kt;return!e&&et(s,0,Ze),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function St(e,t,n){return function(...o){const r=this.__v_raw,i=Yt(r),s=h(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?ft:t?Gt:Kt;return!t&&et(i,0,l?Qe:Ze),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Tt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Ct(){const e={get(e){return gt(this,e)},get size(){return vt(this)},has:mt,add:yt,set:_t,delete:bt,clear:wt,forEach:xt(!1,!1)},t={get(e){return gt(this,e,!1,!0)},get size(){return vt(this)},has:mt,add:yt,set:_t,delete:bt,clear:wt,forEach:xt(!1,!0)},n={get(e){return gt(this,e,!0)},get size(){return vt(this,!0)},has(e){return mt.call(this,e,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:xt(!0,!1)},o={get(e){return gt(this,e,!0,!0)},get size(){return vt(this,!0)},has(e){return mt.call(this,e,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:xt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=St(r,!1,!1),n[r]=St(r,!0,!1),t[r]=St(r,!1,!0),o[r]=St(r,!0,!0)})),[e,n,t,o]}const[kt,Et,Ot,Lt]=Ct();function $t(e,t){const n=t?e?Lt:Ot:e?Et:kt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(p(n,o)&&o in t?n:t,o,r)}const Mt={get:$t(!1,!1)},At={get:$t(!1,!0)},Pt={get:$t(!0,!1)},Bt=new WeakMap,Rt=new WeakMap,It=new WeakMap,Nt=new WeakMap;function jt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function Vt(e){return Wt(e)?e:Ft(e,!1,ut,Mt,Bt)}function Dt(e){return Ft(e,!1,pt,At,Rt)}function Ht(e){return Ft(e,!0,dt,Pt,It)}function Ft(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=jt(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function qt(e){return Wt(e)?qt(e.__v_raw):!(!e||!e.__v_isReactive)}function Wt(e){return!(!e||!e.__v_isReadonly)}function zt(e){return!(!e||!e.__v_isShallow)}function Ut(e){return qt(e)||Wt(e)}function Yt(e){const t=e&&e.__v_raw;return t?Yt(t):e}function Xt(e){return Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const Kt=e=>_(e)?Vt(e):e,Gt=e=>_(e)?Ht(e):e;class Jt{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ie((()=>e(this._value)),(()=>Qt(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Yt(this);return e._cacheable&&!e.effect.dirty||!P(e._value,e._value=e.effect.run())||Qt(e,4),Zt(e),e.effect._dirtyLevel>=2&&Qt(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function Zt(e){var t;De&&Pe&&(e=Yt(e),Ye(Pe,null!=(t=e.dep)?t:e.dep=Ge((()=>e.dep=void 0),e instanceof Jt?e:void 0)))}function Qt(e,t=4,n){const o=(e=Yt(e)).dep;o&&Ke(o,t)}function en(e){return!(!e||!0!==e.__v_isRef)}function tn(e){return nn(e,!1)}function nn(e,t){return en(e)?e:new on(e,t)}class on{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Yt(e),this._value=t?e:Kt(e)}get value(){return Zt(this),this._value}set value(e){const t=this.__v_isShallow||zt(e)||Wt(e);e=t?e:Yt(e),P(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Kt(e),Qt(this,4))}}function rn(e){return en(e)?e.value:e}const sn={get:(e,t,n)=>rn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return en(r)&&!en(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function an(e){return qt(e)?e:new Proxy(e,sn)}function ln(e,t,n,o){try{return o?e(...o):e()}catch(r){un(r,t,n)}}function cn(e,t,n,o){if(m(e)){const r=ln(e,t,n,o);return r&&b(r)&&r.catch((e=>{un(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(cn(e[i],t,n,o));return r}function un(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void ln(s,null,10,[e,r,i])}dn(e,n,r,o)}function dn(e,t,n,o=!0){console.error(e)}let pn=!1,fn=!1;const hn=[];let gn=0;const mn=[];let vn=null,yn=0;const _n=Promise.resolve();let bn=null;function wn(e){const t=bn||_n;return e?t.then(this?e.bind(this):e):t}function xn(e){hn.length&&hn.includes(e,pn&&e.allowRecurse?gn+1:gn)||(null==e.id?hn.push(e):hn.splice(function(e){let t=gn+1,n=hn.length;for(;t<n;){const o=t+n>>>1,r=hn[o],i=kn(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),Sn())}function Sn(){pn||fn||(fn=!0,bn=_n.then(On))}function Tn(e,t,n=(pn?gn+1:0)){for(;n<hn.length;n++){const t=hn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;hn.splice(n,1),n--,t()}}}function Cn(e){if(mn.length){const e=[...new Set(mn)].sort(((e,t)=>kn(e)-kn(t)));if(mn.length=0,vn)return void vn.push(...e);for(vn=e,yn=0;yn<vn.length;yn++)vn[yn]();vn=null,yn=0}}const kn=e=>null==e.id?1/0:e.id,En=(e,t)=>{const n=kn(e)-kn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function On(e){fn=!1,pn=!0,hn.sort(En);try{for(gn=0;gn<hn.length;gn++){const e=hn[gn];e&&!1!==e.active&&ln(e,null,14)}}finally{gn=0,hn.length=0,Cn(),pn=!1,bn=null,(hn.length||mn.length)&&On()}}function Ln(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in r){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=r[e]||o;s&&(i=n.map((e=>v(e)?e.trim():e))),t&&(i=n.map(I))}let l,c=r[l=A(t)]||r[l=A(O(t))];!c&&s&&(c=r[l=A($(t))]),c&&cn(c,e,6,$n(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,cn(u,e,6,$n(e,u,i))}}function $n(e,t,n){if(1!==n.length)return n;if(m(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&p(o,"type")&&p(o,"timeStamp")&&p(o,"target")&&p(o,"currentTarget")&&p(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Mn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!m(e)){const o=e=>{const n=Mn(e,t,!0);n&&(a=!0,c(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(f(i)?i.forEach((e=>s[e]=null)):c(s,i),_(e)&&o.set(e,s),s):(_(e)&&o.set(e,null),null)}function An(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),p(e,t[0].toLowerCase()+t.slice(1))||p(e,$(t))||p(e,t))}let Pn=null,Bn=null;function Rn(e){const t=Pn;return Pn=e,Bn=e&&e.type.__scopeId||null,t}function In(e,t=Pn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&qr(-1);const r=Rn(t);let i;try{i=e(...n)}finally{Rn(r),o._d&&qr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Nn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:c,emit:u,render:d,renderCache:p,data:f,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const _=Rn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=oi(d.call(t,e,p,i,h,f,g)),y=c}else{const e=t;0,v=oi(e.length>1?e(i,{attrs:c,slots:a,emit:u}):e(i,null)),y=t.props?c:jn(c)}}catch(w){Vr.length=0,un(w,e,1),v=Qr(Nr)}let b=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(l)&&(y=Vn(y,s)),b=ei(b,y))}return n.dirs&&(b=ei(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),v=b,Rn(_),v}const jn=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},Vn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Dn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!An(n,i))return!0}return!1}function Hn(e,t){return Wn("components",e,!0,t)||e}const Fn=Symbol.for("v-ndc");function qn(e){return v(e)?Wn("components",e,!1)||e:e||Fn}function Wn(e,t,n=!0,o=!1){const r=Pn||ui;if(r){const n=r.type;if("components"===e){const e=wi(n,!1);if(e&&(e===t||e===O(t)||e===M(O(t))))return n}const i=zn(r[e]||n[e],t)||zn(r.appContext[e],t);return!i&&o?n:i}}function zn(e,t){return e&&(e[t]||e[O(t)]||e[M(O(t))])}const Un=e=>e.__isSuspense;const Yn=Symbol.for("v-scx");function Xn(e,t){return Jn(e,null,t)}const Kn={};function Gn(e,t,n){return Jn(e,t,n)}function Jn(e,t,{immediate:n,deep:r,flush:s,once:a,onTrack:l,onTrigger:c}=o){if(t&&a){const e=t;t=(...t)=>{e(...t),k()}}const d=ui,p=e=>!0===r?e:eo(e,!1===r?1:void 0);let h,g,v=!1,y=!1;if(en(e)?(h=()=>e.value,v=zt(e)):qt(e)?(h=()=>p(e),v=!0):f(e)?(y=!0,v=e.some((e=>qt(e)||zt(e))),h=()=>e.map((e=>en(e)?e.value:qt(e)?p(e):m(e)?ln(e,d,2):void 0))):h=m(e)?t?()=>ln(e,d,2):()=>(g&&g(),cn(e,d,3,[b])):i,t&&r){const e=h;h=()=>eo(e())}let _,b=e=>{g=T.onStop=()=>{ln(e,d,4),g=T.onStop=void 0}};if(vi){if(b=i,t?n&&cn(t,d,3,[h(),y?[]:void 0,b]):h(),"sync"!==s)return i;{const e=gr(Yn);_=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(Kn):Kn;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(r||v||(y?e.some(((e,t)=>P(e,w[t]))):P(e,w)))&&(g&&g(),cn(t,d,3,[e,w===Kn?void 0:y&&w[0]===Kn?[]:w,b]),w=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===s?S=x:"post"===s?S=()=>Lr(x,d&&d.suspense):(x.pre=!0,d&&(x.id=d.uid),S=()=>xn(x));const T=new Ie(h,i,S),C=Ae,k=()=>{T.stop(),C&&u(C.effects,T)};return t?n?x():w=T.run():"post"===s?Lr(T.run.bind(T),d&&d.suspense):T.run(),_&&_.push(k),k}function Zn(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?Qn(o,e):()=>o[e]:e.bind(o,o);let i;m(t)?i=t:(i=t.handler,n=t);const s=hi(this),a=Jn(r,i.bind(o),n);return s(),a}function Qn(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function eo(e,t,n=0,o){if(!_(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),en(e))eo(e.value,t,n,o);else if(f(e))for(let r=0;r<e.length;r++)eo(e[r],t,n,o);else if(g(e)||h(e))e.forEach((e=>{eo(e,t,n,o)}));else if(S(e))for(const r in e)eo(e[r],t,n,o);return e}function to(e,t){if(null===Pn)return e;const n=bi(Pn)||Pn.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,s,a,l=o]=t[i];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&eo(s),r.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function no(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(qe(),cn(l,n,8,[e.el,a,e,t]),We())}}const oo=Symbol("_leaveCb"),ro=Symbol("_enterCb");const io=[Function,Array],so={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:io,onEnter:io,onAfterEnter:io,onEnterCancelled:io,onBeforeLeave:io,onLeave:io,onAfterLeave:io,onLeaveCancelled:io,onBeforeAppear:io,onAppear:io,onAfterAppear:io,onAppearCancelled:io},ao={name:"BaseTransition",props:so,setup(e,{slots:t}){const n=di(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ro((()=>{e.isMounted=!0})),jo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&ho(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Nr){i=e;break}const s=Yt(e),{mode:a}=s;if(o.isLeaving)return uo(i);const l=po(i);if(!l)return uo(i);const c=co(l,s,o,n);fo(l,c);const u=n.subTree,d=u&&po(u);if(d&&d.type!==Nr&&!Xr(l,d)){const e=co(d,s,o,n);if(fo(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},uo(i);"in-out"===a&&l.type!==Nr&&(e.delayLeave=(e,t,n)=>{lo(o,d)[String(d.key)]=d,e[oo]=()=>{t(),e[oo]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function lo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function co(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:p,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:_}=t,b=String(e.key),w=lo(n,e),x=(e,t)=>{e&&cn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),f(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=m||a}t[oo]&&t[oo](!0);const i=w[b];i&&Xr(e,i)&&i.el[oo]&&i.el[oo](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=_||u}let s=!1;const a=e[ro]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[ro]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[ro]&&t[ro](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[oo]=n=>{i||(i=!0,o(),x(n?g:h,[t]),t[oo]=void 0,w[r]===e&&delete w[r])};w[r]=e,p?S(p,[t,s]):s()},clone:e=>co(e,t,n,o)};return T}function uo(e){if(_o(e))return(e=ei(e)).children=null,e}function po(e){return _o(e)?e.children?e.children[0]:void 0:e}function fo(e,t){6&e.shapeFlag&&e.component?fo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ho(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Rr?(128&s.patchFlag&&r++,o=o.concat(ho(s.children,t,a))):(t||s.type!==Nr)&&o.push(null!=a?ei(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function go(e,t){return m(e)?(()=>c({name:e.name},t,{setup:e}))():e}const mo=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function vo(e){m(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return go({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=ui;if(l)return()=>yo(l,e);const t=t=>{c=null,un(t,e,13,!o)};if(s&&e.suspense||vi)return d().then((t=>()=>yo(t,e))).catch((e=>(t(e),()=>o?Qr(o,{error:e}):null)));const a=tn(!1),u=tn(),p=tn(!!r);return r&&setTimeout((()=>{p.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&_o(e.parent.vnode)&&(e.parent.effect.dirty=!0,xn(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?yo(l,e):u.value&&o?Qr(o,{error:u.value}):n&&!p.value?Qr(n):void 0}})}function yo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=Qr(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const _o=e=>e.type.__isKeepAlive;class bo{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const wo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=di(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new bo(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Xr(t,i)||"key"===e.matchBy&&t.key!==i.key?(Oo(o=t),u(o,n,a,!0)):i&&Oo(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,p=d("div");function f(t){r.forEach(((n,o)=>{const i=$o(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,B(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Lr((()=>{i.isDeactivated=!1,i.a&&B(i.a);const t=e.props&&e.props.onVnodeMounted;t&&ai(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&Mo(t.bda),c(e,p,null,1,a),Lr((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&B(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&ai(n,t.parent,e),t.isDeactivated=!0}),a)},Gn((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&f((t=>So(e,t))),t&&f((e=>!So(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,Lo(n.subTree))};return Ro(g),No(g),jo((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=Lo(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&B(l.component.bda),Oo(l);const e=l.component.da;e&&Lr(e,a)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Yr(o)||!(4&o.shapeFlag)&&!Un(o.type))return i=null,o;let s=Lo(o);const a=s.type,l=$o(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!So(c,l))||u&&l&&So(u,l))return i=s,o;const d=null==s.key?a:s.key,p=r.get(d);return s.el&&(s=ei(s),Un(o.type)&&(o.ssContent=s)),h=d,p&&(s.el=p.el,s.component=p.component,s.transition&&fo(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Un(o.type)?o:s}}},xo=wo;function So(e,t){return f(e)?e.some((e=>So(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===x(e)&&e.test(t)}function To(e,t){ko(e,"a",t)}function Co(e,t){ko(e,"da",t)}function ko(e,t,n=ui){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Ao(t,o,n),n){let e=n.parent;for(;e&&e.parent;)_o(e.parent.vnode)&&Eo(o,t,n,e),e=e.parent}}function Eo(e,t,n,o){const r=Ao(t,e,o,!0);Vo((()=>{u(o[t],r)}),n)}function Oo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Lo(e){return Un(e.type)?e.ssContent:e}function $o(e,t){if("name"===t){const t=e.type;return wi(mo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Mo(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Ao(e,t,n=ui,o=!1){if(n){if(r=e,Se.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;cn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;qe();const r=hi(n),i=cn(t,n,e,o);return r(),We(),i});return o?i.unshift(s):i.push(s),s}var r}const Po=e=>(t,n=ui)=>(!vi||"sp"===e)&&Ao(e,((...e)=>t(...e)),n),Bo=Po("bm"),Ro=Po("m"),Io=Po("bu"),No=Po("u"),jo=Po("bum"),Vo=Po("um"),Do=Po("sp"),Ho=Po("rtg"),Fo=Po("rtc");function qo(e,t=ui){Ao("ec",e,t)}function Wo(e,t,n,o){let r;const i=n&&n[o];if(f(e)||v(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(_(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function zo(e,t,n={},o,r){if(Pn.isCE||Pn.parent&&mo(Pn.parent)&&Pn.parent.isCE)return"default"!==t&&(n.name=t),Qr("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Hr();const s=i&&Uo(i(n)),a=Ur(Rr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Uo(e){return e.some((e=>!Yr(e)||e.type!==Nr&&!(e.type===Rr&&!Uo(e.children))))?e:null}const Yo=e=>{if(!e)return null;if(mi(e)){return bi(e)||e.proxy}return Yo(e.parent)},Xo=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Yo(e.parent),$root:e=>Yo(e.root),$emit:e=>e.emit,$options:e=>nr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,xn(e.update)})(e)),$nextTick:e=>e.n||(e.n=wn.bind(e.proxy)),$watch:e=>Zn.bind(e)}),Ko=(e,t)=>e!==o&&!e.__isScriptSetup&&p(e,t),Go={get({_:e},t){const{ctx:n,setupState:r,data:i,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return s[t]}else{if(Ko(r,t))return a[t]=1,r[t];if(i!==o&&p(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&p(u,t))return a[t]=3,s[t];if(n!==o&&p(n,t))return a[t]=4,n[t];Zo&&(a[t]=0)}}const d=Xo[t];let f,h;return d?("$attrs"===t&&et(e,0,t),d(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==o&&p(n,t)?(a[t]=4,n[t]):(h=c.config.globalProperties,p(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:s}=e;return Ko(i,t)?(i[t]=n,!0):r!==o&&p(r,t)?(r[t]=n,!0):!p(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:s}},a){let l;return!!n[a]||e!==o&&p(e,a)||Ko(t,a)||(l=s[0])&&p(l,a)||p(r,a)||p(Xo,a)||p(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:p(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Jo(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Zo=!0;function Qo(e){const t=nr(e),n=e.proxy,o=e.ctx;Zo=!1,t.beforeCreate&&er(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:a,watch:l,provide:c,inject:u,created:d,beforeMount:p,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:T,render:C,renderTracked:k,renderTriggered:E,errorCaptured:O,serverPrefetch:L,expose:$,inheritAttrs:M,components:A,directives:P,filters:B}=t;if(u&&function(e,t,n=i){f(e)&&(e=sr(e));for(const o in e){const n=e[o];let r;r=_(n)?"default"in n?gr(n.from||o,n.default,!0):gr(n.from||o):gr(n),en(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),a)for(const i in a){const e=a[i];m(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=Vt(t))}if(Zo=!0,s)for(const f in s){const e=s[f],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):i,r=!m(e)&&m(e.set)?e.set.bind(n):i,a=xi({get:t,set:r});Object.defineProperty(o,f,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const i in l)tr(l[i],o,n,i);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{hr(t,e[t])}))}function R(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&er(d,e,"c"),R(Bo,p),R(Ro,h),R(Io,g),R(No,v),R(To,y),R(Co,b),R(qo,O),R(Fo,k),R(Ho,E),R(jo,x),R(Vo,T),R(Do,L),f($))if($.length){const t=e.exposed||(e.exposed={});$.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});C&&e.render===i&&(e.render=C),null!=M&&(e.inheritAttrs=M),A&&(e.components=A),P&&(e.directives=P);const I=e.appContext.config.globalProperties.$applyOptions;I&&I(t,e,n)}function er(e,t,n){cn(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function tr(e,t,n,o){const r=o.includes(".")?Qn(n,o):()=>n[o];if(v(e)){const n=t[e];m(n)&&Gn(r,n)}else if(m(e))Gn(r,e.bind(n));else if(_(e))if(f(e))e.forEach((e=>tr(e,t,n,o)));else{const o=m(e.handler)?e.handler.bind(n):t[e.handler];m(o)&&Gn(r,o,e)}}function nr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>or(l,e,s,!0))),or(l,t,s)):l=t,_(t)&&i.set(t,l),l}function or(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&or(e,i,n,!0),r&&r.forEach((t=>or(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=rr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const rr={data:ir,props:cr,emits:cr,methods:lr,computed:lr,beforeCreate:ar,created:ar,beforeMount:ar,mounted:ar,beforeUpdate:ar,updated:ar,beforeDestroy:ar,beforeUnmount:ar,destroyed:ar,unmounted:ar,activated:ar,deactivated:ar,errorCaptured:ar,serverPrefetch:ar,components:lr,directives:lr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=ar(e[o],t[o]);return n},provide:ir,inject:function(e,t){return lr(sr(e),sr(t))}};function ir(e,t){return t?e?function(){return c(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function sr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ar(e,t){return e?[...new Set([].concat(e,t))]:t}function lr(e,t){return e?c(Object.create(null),e,t):t}function cr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:c(Object.create(null),Jo(e),Jo(null!=t?t:{})):t}function ur(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let dr=0;function pr(e,t){return function(n,o=null){m(n)||(n=c({},n)),null==o||_(o)||(o=null);const r=ur(),i=new WeakSet;let s=!1;const a=r.app={_uid:dr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Ti,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&m(e.install)?(i.add(e),e.install(a,...t)):m(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=Qr(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,bi(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=fr;fr=a;try{return e()}finally{fr=t}}};return a}}let fr=null;function hr(e,t){if(ui){let n=ui.provides;const o=ui.parent&&ui.parent.provides;o===n&&(n=ui.provides=Object.create(o)),n[e]=t,"app"===ui.type.mpType&&ui.appContext.app.provide(e,t)}else;}function gr(e,t,n=!1){const o=ui||Pn;if(o||fr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:fr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&m(t)?t.call(o&&o.proxy):t}}function mr(e,t,n,r){const[i,s]=e.propsOptions;let a,l=!1;if(t)for(let o in t){if(C(o))continue;const c=t[o];let u;i&&p(i,u=O(o))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:An(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(s){const t=Yt(n),r=a||o;for(let o=0;o<s.length;o++){const a=s[o];n[a]=vr(i,t,a,r[a],e,!p(r,a))}}return l}function vr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=p(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&m(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=hi(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==$(n)||(o=!0))}return o}function yr(e,t,n=!1){const i=t.propsCache,s=i.get(e);if(s)return s;const a=e.props,l={},u=[];let d=!1;if(!m(e)){const o=e=>{d=!0;const[n,o]=yr(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!d)return _(e)&&i.set(e,r),r;if(f(a))for(let r=0;r<a.length;r++){const e=O(a[r]);_r(e)&&(l[e]=o)}else if(a)for(const o in a){const e=O(o);if(_r(e)){const t=a[o],n=l[e]=f(t)||m(t)?{type:t}:c({},t);if(n){const t=xr(Boolean,n.type),o=xr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||p(n,"default"))&&u.push(e)}}}const h=[l,u];return _(e)&&i.set(e,h),h}function _r(e){return"$"!==e[0]&&!C(e)}function br(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function wr(e,t){return br(e)===br(t)}function xr(e,t){return f(t)?t.findIndex((t=>wr(t,e))):m(t)&&wr(t,e)?0:-1}const Sr=e=>"_"===e[0]||"$stable"===e,Tr=e=>f(e)?e.map(oi):[oi(e)],Cr=(e,t,n)=>{if(t._n)return t;const o=In(((...e)=>Tr(t(...e))),n);return o._c=!1,o},kr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Sr(r))continue;const n=e[r];if(m(n))t[r]=Cr(0,n,o);else if(null!=n){const e=Tr(n);t[r]=()=>e}}},Er=(e,t)=>{const n=Tr(t);e.slots.default=()=>n};function Or(e,t,n,r,i=!1){if(f(e))return void e.forEach(((e,o)=>Or(e,t&&(f(t)?t[o]:t),n,r,i)));if(mo(r)&&!i)return;const s=4&r.shapeFlag?bi(r.component)||r.component.proxy:r.el,a=i?null:s,{i:l,r:c}=e,d=t&&t.r,h=l.refs===o?l.refs={}:l.refs,g=l.setupState;if(null!=d&&d!==c&&(v(d)?(h[d]=null,p(g,d)&&(g[d]=null)):en(d)&&(d.value=null)),m(c))ln(c,l,12,[a,h]);else{const t=v(c),o=en(c);if(t||o){const r=()=>{if(e.f){const n=t?p(g,c)?g[c]:h[c]:c.value;i?f(n)&&u(n,s):f(n)?n.includes(s)||n.push(s):t?(h[c]=[s],p(g,c)&&(g[c]=h[c])):(c.value=[s],e.k&&(h[e.k]=c.value))}else t?(h[c]=a,p(g,c)&&(g[c]=a)):o&&(c.value=a,e.k&&(h[e.k]=a))};a?(r.id=-1,Lr(r,n)):r()}}}const Lr=function(e,t){var n;t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?mn.push(...n):vn&&vn.includes(n,n.allowRecurse?yn+1:yn)||mn.push(n),Sn())};function $r(e){return function(e,t){j().__VUE__=!0;const{insert:n,remove:s,patchProp:a,forcePatchProp:l,createElement:u,createText:d,createComment:f,setText:h,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=i,insertStaticContent:_}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Xr(e,t)&&(o=te(e),G(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Ir:x(e,t,n,o);break;case Nr:S(e,t,n,o);break;case jr:null==e&&T(t,n,o,s);break;case Rr:D(e,t,n,o,r,i,s,a,l);break;default:1&d?L(e,t,n,o,r,i,s,a,l):6&d?H(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&Or(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=f(t.children||""),o,r):t.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=_(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),s(e),e=n;s(t)},L=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?M(t,n,o,r,i,s,a,l):I(e,t,r,i,s,a,l)},M=(e,t,o,r,i,s,l,c)=>{let d,p;const{props:f,shapeFlag:h,transition:m,dirs:v}=e;if(d=e.el=u(e.type,s,f&&f.is,f),8&h?g(d,e.children):16&h&&P(e.children,d,null,r,i,Mr(e,s),l,c),v&&no(e,null,r,"created"),A(d,e,e.scopeId,l,r),f){for(const t in f)"value"===t||C(t)||a(d,t,null,f[t],s,e.children,r,i,ee);"value"in f&&a(d,"value",null,f.value,s),(p=f.onVnodeBeforeMount)&&ai(p,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),v&&no(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,m);y&&m.beforeEnter(d),n(d,t,o),((p=f&&f.onVnodeMounted)||y||v)&&Lr((()=>{p&&ai(p,r,e),y&&m.enter(d),v&&no(e,null,r,"mounted")}),i)},A=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;A(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},P=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?ri(e[c]):oi(e[c]);w(null,l,t,n,o,r,i,s,a)}},I=(e,t,n,r,i,s,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:p,dirs:f}=t;d|=16&e.patchFlag;const h=e.props||o,m=t.props||o;let v;if(n&&Ar(n,!1),(v=m.onVnodeBeforeUpdate)&&ai(v,n,t,e),f&&no(t,e,n,"beforeUpdate"),n&&Ar(n,!0),p?N(e.dynamicChildren,p,u,n,r,Mr(t,i),s):c||U(e,t,u,null,n,r,Mr(t,i),s,!1),d>0){if(16&d)V(u,t,h,m,n,r,i);else if(2&d&&h.class!==m.class&&a(u,"class",null,m.class,i),4&d&&a(u,"style",h.style,m.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const s=o[t],c=h[s],d=m[s];(d!==c||"value"===s||l&&l(u,s))&&a(u,s,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=p||V(u,t,h,m,n,r,i);((v=m.onVnodeUpdated)||f)&&Lr((()=>{v&&ai(v,n,t,e),f&&no(t,e,n,"updated")}),r)},N=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Rr||!Xr(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},V=(e,t,n,r,i,s,c)=>{if(n!==r){if(n!==o)for(const o in n)C(o)||o in r||a(e,o,n[o],null,c,t.children,i,s,ee);for(const o in r){if(C(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&a(e,o,d,u,c,t.children,i,s,ee)}"value"in r&&a(e,"value",n.value,r.value,c)}},D=(e,t,o,r,i,s,a,l,c)=>{const u=t.el=e?e.el:d(""),p=t.anchor=e?e.anchor:d("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(u,o,r),n(p,o,r),P(t.children||[],o,p,i,s,a,l,c)):f>0&&64&f&&h&&e.dynamicChildren?(N(e.dynamicChildren,h,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Pr(e,t,!0)):U(e,t,o,p,i,s,a,l,c)},H=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):F(t,n,o,r,i,s,l):q(e,t,l)},F=(e,t,n,r,i,s,a)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||li,s={uid:ci++,vnode:e,type:r,parent:t,appContext:i,get renderer(){return"app"===r.mpType?"app":this.$pageInstance&&this.$pageInstance==s?"page":"component"},root:null,next:null,subTree:null,effect:null,update:null,scope:new Be(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:yr(r,i),emitsOptions:Mn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=Ln.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,r,i);if(_o(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&fi(t);const{props:n,children:o}=e.vnode,r=mi(e);(function(e,t,n,o=!1){const r={},i={};R(i,Kr,1),e.propsDefaults=Object.create(null),mr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Dt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Yt(t),R(t,"_",n)):kr(t,e.slots={})}else e.slots={},t&&Er(e,t);R(e.slots,Kr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Xt(new Proxy(e.ctx,Go));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(et(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=hi(e);qe();const i=ln(o,e,0,[e.props,n]);if(We(),r(),b(i)){if(i.then(gi,gi),t)return i.then((n=>{yi(e,n,t)})).catch((t=>{un(t,e,0)}));e.asyncDep=i}else yi(e,i,t)}else _i(e,t)}(e,t):void 0;t&&fi(!1)}(l),l.asyncDep){if(i&&i.registerDep(l,W),!e.el){const e=l.subTree=Qr(Nr);S(null,e,t,n)}}else W(l,e,t,n,i,s,a)},q=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||Dn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?Dn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!An(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void z(o,t,n);o.next=t,function(e){const t=hn.indexOf(e);t>gn&&hn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},W=(e,t,n,o,r,s,a)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:i,vnode:c}=e;{const n=Br(e);if(n)return t&&(t.el=c.el,z(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;Ar(e,!1),t?(t.el=c.el,z(e,t,a)):t=c,n&&B(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&ai(u,i,t,c),Ar(e,!0);const p=Nn(e),f=e.subTree;e.subTree=p,w(f,p,m(f.el),te(f),e,r,s),t.el=p.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,p.el),o&&Lr(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Lr((()=>ai(u,i,t,c)),r)}else{let i;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,p=mo(t);if(Ar(e,!1),c&&B(c),!p&&(i=l&&l.onVnodeBeforeMount)&&ai(i,d,t),Ar(e,!0),a&&se){const n=()=>{e.subTree=Nn(e),se(a,e.subTree,e,r,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=Nn(e);w(null,i,n,o,e,r,s),t.el=i.el}if(u&&Lr(u,r),!p&&(i=l&&l.onVnodeMounted)){const e=t;Lr((()=>ai(i,d,e)),r)}(256&t.shapeFlag||d&&mo(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&Mo(e.ba),e.a&&Lr(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new Ie(l,i,(()=>xn(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Ar(e,!0),u()},z=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Yt(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;mr(e,t,r,i)&&(c=!0);for(const i in a)t&&(p(t,i)||(o=$(i))!==i&&p(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=vr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&p(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(An(e.emitsOptions,s))continue;const u=t[s];if(l)if(p(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=O(s);r[t]=vr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&tt(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let s=!0,a=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?s=!1:(c(i,t),n||1!==e||delete i._):(s=!t.$stable,kr(t,i)),a=t}else t&&(Er(e,t),a={default:1});if(s)for(const o in i)Sr(o)||null!=a[o]||delete i[o]})(e,t.children,n),qe(),Tn(e),We()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:f}=t;if(p>0){if(128&p)return void X(c,d,n,o,r,i,s,a,l);if(256&p)return void Y(c,d,n,o,r,i,s,a,l)}8&f?(16&u&&ee(c,r,i),d!==c&&g(n,d)):16&u?16&f?X(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&g(n,""),16&f&&P(d,n,o,r,i,s,a,l))},Y=(e,t,n,o,i,s,a,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,p=Math.min(u,d);let f;for(f=0;f<p;f++){const o=t[f]=c?ri(t[f]):oi(t[f]);w(e[f],o,n,null,i,s,a,l,c)}u>d?ee(e,i,s,!0,!1,p):P(t,n,o,i,s,a,l,c,p)},X=(e,t,n,o,i,s,a,l,c)=>{let u=0;const d=t.length;let p=e.length-1,f=d-1;for(;u<=p&&u<=f;){const o=e[u],r=t[u]=c?ri(t[u]):oi(t[u]);if(!Xr(o,r))break;w(o,r,n,null,i,s,a,l,c),u++}for(;u<=p&&u<=f;){const o=e[p],r=t[f]=c?ri(t[f]):oi(t[f]);if(!Xr(o,r))break;w(o,r,n,null,i,s,a,l,c),p--,f--}if(u>p){if(u<=f){const e=f+1,r=e<d?t[e].el:o;for(;u<=f;)w(null,t[u]=c?ri(t[u]):oi(t[u]),n,r,i,s,a,l,c),u++}}else if(u>f)for(;u<=p;)G(e[u],i,s,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=f;u++){const e=t[u]=c?ri(t[u]):oi(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const _=f-g+1;let b=!1,x=0;const S=new Array(_);for(u=0;u<_;u++)S[u]=0;for(u=h;u<=p;u++){const o=e[u];if(y>=_){G(o,i,s,!0);continue}let r;if(null!=o.key)r=m.get(o.key);else for(v=g;v<=f;v++)if(0===S[v-g]&&Xr(o,t[v])){r=v;break}void 0===r?G(o,i,s,!0):(S[r-g]=u+1,r>=x?x=r:b=!0,w(o,t[r],n,null,i,s,a,l,c),y++)}const T=b?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):r;for(v=T.length-1,u=_-1;u>=0;u--){const e=g+u,r=t[e],p=e+1<d?t[e+1].el:o;0===S[u]?w(null,r,n,p,i,s,a,l,c):b&&(v<0||u!==T[v]?K(r,n,p,2):v--)}}},K=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void K(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===Rr){n(s,t,o);for(let e=0;e<c.length;e++)K(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===jr)return void k(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Lr((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},G=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p}=e;if(null!=a&&Or(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const f=1&u&&p,h=!mo(e);let g;if(h&&(g=s&&s.onVnodeBeforeUnmount)&&ai(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);f&&no(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==Rr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Rr&&384&d||!r&&16&u)&&ee(l,t,n),o&&J(e)}(h&&(g=s&&s.onVnodeUnmounted)||f)&&Lr((()=>{g&&ai(g,t,e),f&&no(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Rr)return void Z(n,o);if(t===jr)return void E(e);const i=()=>{s(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,s=()=>t(n,i);o?o(e.el,i,s):s()}else i()},Z=(e,t)=>{let n;for(;e!==t;)n=v(e),s(e),e=n;s(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&B(o),r.stop(),i&&(i.active=!1,G(s,e,t,n)),a&&Lr(a,t),Lr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)G(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&G(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,Tn(),Cn(),ne=!1),t._vnode=e},re={p:w,um:G,m:K,r:J,mt:F,mc:P,pc:U,pbc:N,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:pr(oe,ie)}}(e)}function Mr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ar({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Pr(e,t,n=!1){const o=e.children,r=t.children;if(f(o)&&f(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=ri(r[i]),t.el=e.el),n||Pr(e,t)),t.type===Ir&&(t.el=e.el)}}function Br(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Br(t)}const Rr=Symbol.for("v-fgt"),Ir=Symbol.for("v-txt"),Nr=Symbol.for("v-cmt"),jr=Symbol.for("v-stc"),Vr=[];let Dr=null;function Hr(e=!1){Vr.push(Dr=e?null:[])}let Fr=1;function qr(e){Fr+=e}function Wr(e){return e.dynamicChildren=Fr>0?Dr||r:null,Vr.pop(),Dr=Vr[Vr.length-1]||null,Fr>0&&Dr&&Dr.push(e),e}function zr(e,t,n,o,r,i){return Wr(Zr(e,t,n,o,r,i,!0))}function Ur(e,t,n,o,r){return Wr(Qr(e,t,n,o,r,!0))}function Yr(e){return!!e&&!0===e.__v_isVNode}function Xr(e,t){return e.type===t.type&&e.key===t.key}const Kr="__vInternal",Gr=({key:e})=>null!=e?e:null,Jr=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||en(e)||m(e)?{i:Pn,r:e,k:t,f:!!n}:e:null);function Zr(e,t=null,n=null,o=0,r=null,i=(e===Rr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Gr(t),ref:t&&Jr(t),scopeId:Bn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Pn};return a?(ii(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),Fr>0&&!s&&Dr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Dr.push(l),l}const Qr=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Fn||(e=Nr);if(Yr(e)){const o=ei(e,t,!0);return n&&ii(o,n),Fr>0&&!i&&Dr&&(6&o.shapeFlag?Dr[Dr.indexOf(e)]=o:Dr.push(o)),o.patchFlag|=-2,o}s=e,m(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Ut(e)||Kr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=ue(e)),_(n)&&(Ut(n)&&!f(n)&&(n=c({},n)),t.style=ce(n))}const a=v(e)?1:Un(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:m(e)?2:0;return Zr(e,t,n,o,r,a,i,!0)};function ei(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?si(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Gr(a),ref:t&&t.ref?n&&r?f(r)?r.concat(Jr(t)):[r,Jr(t)]:Jr(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Rr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ei(e.ssContent),ssFallback:e.ssFallback&&ei(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ti(e=" ",t=0){return Qr(Ir,null,e,t)}function ni(e="",t=!1){return t?(Hr(),Ur(Nr,null,e)):Qr(Nr,null,e)}function oi(e){return null==e||"boolean"==typeof e?Qr(Nr):f(e)?Qr(Rr,null,e.slice()):"object"==typeof e?ri(e):Qr(Ir,null,String(e))}function ri(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ei(e)}function ii(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),ii(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Kr in t?3===o&&Pn&&(1===Pn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Pn}}else m(t)?(t={default:t,_ctx:Pn},n=32):(t=String(t),64&o?(n=16,t=[ti(t)]):n=8);e.children=t,e.shapeFlag|=n}function si(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=ue([t.class,o.class]));else if("style"===e)t.style=ce([t.style,o.style]);else if(a(e)){const n=t[e],r=o[e];!r||n===r||f(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function ai(e,t,n,o=null){cn(e,t,7,[n,o])}const li=ur();let ci=0;let ui=null;const di=()=>ui||Pn;let pi,fi;{const e=j(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};pi=t("__VUE_INSTANCE_SETTERS__",(e=>ui=e)),fi=t("__VUE_SSR_SETTERS__",(e=>vi=e))}const hi=e=>{const t=ui;return pi(e),e.scope.on(),()=>{e.scope.off(),pi(t)}},gi=()=>{ui&&ui.scope.off(),pi(null)};function mi(e){return 4&e.vnode.shapeFlag}let vi=!1;function yi(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=an(t)),_i(e,n)}function _i(e,t,n){const o=e.type;e.render||(e.render=o.render||i);{const t=hi(e);qe();try{Qo(e)}finally{We(),t()}}}function bi(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(an(Xt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Xo?Xo[n](e):void 0,has:(e,t)=>t in e||t in Xo}))}function wi(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const xi=(e,t)=>{const n=function(e,t,n=!1){let o,r;const s=m(e);return s?(o=e,r=i):(o=e.get,r=e.set),new Jt(o,r,s||!r,n)}(e,0,vi);return n};function Si(e,t,n){const o=arguments.length;return 2===o?_(t)&&!f(t)?Yr(t)?Qr(e,null,[t]):Qr(e,t):Qr(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Yr(n)&&(n=[n]),Qr(e,t,n))}const Ti="3.4.21",Ci="undefined"!=typeof document?document:null,ki=Ci&&Ci.createElement("template"),Ei={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?Ci.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Ci.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Ci.createElement(e,{is:n}):Ci.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Ci.createTextNode(e),createComment:e=>Ci.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ci.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{ki.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=ki.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Oi="transition",Li=Symbol("_vtc"),$i=(e,{slots:t})=>Si(ao,function(e){const t={};for(const c in e)c in Mi||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=s,appearToClass:d=a,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[Bi(e.enter),Bi(e.leave)];{const t=Bi(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:C=b,onAppearCancelled:k=w}=t,E=(e,t,n)=>{Ii(e,t?d:a),Ii(e,t?u:s),n&&n()},O=(e,t)=>{e._isLeaving=!1,Ii(e,p),Ii(e,h),Ii(e,f),t&&t()},L=e=>(t,n)=>{const r=e?C:b,s=()=>E(t,e,n);Ai(r,[t,s]),Ni((()=>{Ii(t,e?l:i),Ri(t,e?d:a),Pi(r)||Vi(t,o,m,s)}))};return c(t,{onBeforeEnter(e){Ai(y,[e]),Ri(e,i),Ri(e,s)},onBeforeAppear(e){Ai(T,[e]),Ri(e,l),Ri(e,u)},onEnter:L(!1),onAppear:L(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);Ri(e,p),document.body.offsetHeight,Ri(e,f),Ni((()=>{e._isLeaving&&(Ii(e,p),Ri(e,h),Pi(x)||Vi(e,o,v,n))})),Ai(x,[e,n])},onEnterCancelled(e){E(e,!1),Ai(w,[e])},onAppearCancelled(e){E(e,!0),Ai(k,[e])},onLeaveCancelled(e){O(e),Ai(S,[e])}})}(e),t);$i.displayName="Transition";const Mi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};$i.props=c({},so,Mi);const Ai=(e,t=[])=>{f(e)?e.forEach((e=>e(...t))):e&&e(...t)},Pi=e=>!!e&&(f(e)?e.some((e=>e.length>1)):e.length>1);function Bi(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Ri(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Li]||(e[Li]=new Set)).add(t)}function Ii(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Li];n&&(n.delete(t),n.size||(e[Li]=void 0))}function Ni(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let ji=0;function Vi(e,t,n,o){const r=e._endId=++ji,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=Di(r,i),a=o("animationDelay"),l=o("animationDuration"),c=Di(a,l);let u=null,d=0,p=0;t===Oi?s>0&&(u=Oi,d=s,p=i.length):"animation"===t?c>0&&(u="animation",d=c,p=l.length):(d=Math.max(s,c),u=d>0?s>c?Oi:"animation":null,p=u?u===Oi?i.length:l.length:0);const f=u===Oi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:p,hasTransform:f}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,p),i()},p=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,p)}function Di(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Hi(t)+Hi(e[n]))))}function Hi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Fi=Symbol("_vod"),qi=Symbol("_vsh"),Wi={beforeMount(e,{value:t},{transition:n}){e[Fi]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):zi(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),zi(e,!0),o.enter(e)):o.leave(e,(()=>{zi(e,!1)})):zi(e,t))},beforeUnmount(e,{value:t}){zi(e,t)}};function zi(e,t){e.style.display=t?e[Fi]:"none",e[qi]=!t}const Ui=Symbol(""),Yi=/(^|;)\s*display\s*:/;const Xi=/\s*!important$/;function Ki(e,t,n){if(f(n))n.forEach((n=>Ki(e,t,n)));else if(null==n&&(n=""),n=is(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Ji[t];if(n)return n;let o=O(t);if("filter"!==o&&o in e)return Ji[t]=o;o=M(o);for(let r=0;r<Gi.length;r++){const n=Gi[r]+o;if(n in e)return Ji[t]=n}return t}(e,t);Xi.test(n)?e.setProperty($(o),n.replace(Xi,""),"important"):e[o]=n}}const Gi=["Webkit","Moz","ms"],Ji={};const{unit:Zi,unitRatio:Qi,unitPrecision:es}={unit:"rem",unitRatio:10/320,unitPrecision:5},ts=(ns=Zi,os=Qi,rs=es,e=>e.replace(he,((e,t)=>{if(!t)return e;if(1===os)return`${t}${ns}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*os,rs);return 0===n?"0":`${n}${ns}`})));var ns,os,rs;const is=e=>v(e)?ts(e):e,ss="http://www.w3.org/1999/xlink";const as=Symbol("_vei");function ls(e,t,n,o,r=null){const i=e[as]||(e[as]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(cs.test(e)){let n;for(t={};n=e.match(cs);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):$(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&f(i)){const n=ps(e,i);for(let o=0;o<n.length;o++){const i=n[o];cn(i,t,5,i.__wwe?[e]:r(e))}}else cn(ps(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>us||(ds.then((()=>us=0)),us=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const cs=/(?:Once|Passive|Capture)$/;let us=0;const ds=Promise.resolve();function ps(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const fs=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const hs=["ctrl","shift","alt","meta"],gs={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>hs.some((n=>e[`${n}Key`]&&!t.includes(n)))},ms=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=gs[t[e]];if(o&&o(n,t))return}return e(n,...o)})},vs=c({patchProp:(e,t,n,o,r,i,s,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;wn((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[Li];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=v(n);let i=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Ki(o,t,"")}else for(const e in t)null==n[e]&&Ki(o,e,"");for(const e in n)"display"===e&&(i=!0),Ki(o,e,n[e])}else if(r){if(t!==n){const e=o[Ui];e&&(n+=";"+e),o.cssText=n,i=Yi.test(n)}}else t&&e.removeAttribute("style");Fi in e&&(e[Fi]=i?o.display:"",e[qi]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)Ki(o,a,s[a])}(e,n,o):a(t)?l(t)||ls(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&fs(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(fs(t)&&v(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=U(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(ss,t.slice(6,t.length)):e.setAttributeNS(ss,t,n);else{const o=z(t);null==n||o&&!U(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Ei);let ys;const _s=(...e)=>{const t=(ys||(ys=$r(vs))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;m(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const bs="undefined"!=typeof document;const ws=Object.assign;function xs(e,t){const n={};for(const o in t){const r=t[o];n[o]=Ts(r)?r.map(e):e(r)}return n}const Ss=()=>{},Ts=Array.isArray,Cs=/#/g,ks=/&/g,Es=/\//g,Os=/=/g,Ls=/\?/g,$s=/\+/g,Ms=/%5B/g,As=/%5D/g,Ps=/%5E/g,Bs=/%60/g,Rs=/%7B/g,Is=/%7C/g,Ns=/%7D/g,js=/%20/g;function Vs(e){return encodeURI(""+e).replace(Is,"|").replace(Ms,"[").replace(As,"]")}function Ds(e){return Vs(e).replace($s,"%2B").replace(js,"+").replace(Cs,"%23").replace(ks,"%26").replace(Bs,"`").replace(Rs,"{").replace(Ns,"}").replace(Ps,"^")}function Hs(e){return null==e?"":function(e){return Vs(e).replace(Cs,"%23").replace(Ls,"%3F")}(e).replace(Es,"%2F")}function Fs(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const qs=/\/$/;function Ws(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:Fs(s)}}function zs(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Us(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ys(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Xs(e[n],t[n]))return!1;return!0}function Xs(e,t){return Ts(e)?Ks(e,t):Ts(t)?Ks(t,e):e===t}function Ks(e,t){return Ts(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var Gs,Js,Zs,Qs;function ea(e){if(!e)if(bs){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(qs,"")}(Js=Gs||(Gs={})).pop="pop",Js.push="push",(Qs=Zs||(Zs={})).back="back",Qs.forward="forward",Qs.unknown="";const ta=/^[^#]+#/;function na(e,t){return e.replace(ta,"#")+t}const oa=()=>({left:window.scrollX,top:window.scrollY});function ra(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function ia(e,t){return(history.state?history.state.position-t:-1)+e}const sa=new Map;function aa(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),zs(n,"")}return zs(n,e)+o+r}function la(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?oa():null}}function ca(e){const{history:t,location:n}=window,o={value:aa(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=ws({},r.value,t.state,{forward:e,scroll:oa()});i(s.current,s,!0),i(e,ws({},la(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,ws({},t.state,la(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function ua(e){const t=ca(e=ea(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=aa(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:Gs.pop,direction:u?u>0?Zs.forward:Zs.back:Zs.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(ws({},e.state,{scroll:oa()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=ws({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:na.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function da(e){return"string"==typeof e||"symbol"==typeof e}const pa={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},fa=Symbol("");var ha,ga;function ma(e,t){return ws(new Error,{type:e,[fa]:!0},t)}function va(e,t){return e instanceof Error&&fa in e&&(null==t||!!(e.type&t))}(ga=ha||(ha={}))[ga.aborted=4]="aborted",ga[ga.cancelled=8]="cancelled",ga[ga.duplicated=16]="duplicated";const ya={sensitive:!1,strict:!1,start:!0,end:!0},_a=/[.+*?^${}()[\]/\\]/g;function ba(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function wa(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=ba(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(xa(o))return 1;if(xa(r))return-1}return r.length-o.length}function xa(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Sa={type:0,value:""},Ta=/[a-zA-Z0-9_]/;function Ca(e,t,n){const o=function(e,t){const n=ws({},ya,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(_a,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){s+=10;try{new RegExp(`(${d})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+a.message)}}let p=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(p=c&&l.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),r+=p,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===d&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(Ts(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ts(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Sa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function p(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&d(),s()):":"===a?(d(),n=1):p();break;case 4:p(),n=o;break;case 1:"("===a?n=2:Ta.test(a)?p():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),s(),r}(e.path),n),r=ws(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ka(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Oa(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Ma(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(ws({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Ca(t,n,c),o?o.alias.push(d):(p=p||d,p!==d&&p.alias.push(d),a&&e.name&&!La(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&s(d)}return p?()=>{i(p)}:Ss}function i(e){if(da(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&wa(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Aa(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!La(e)&&o.set(e.record.name,e)}return t=Ma({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw ma(1,{location:e});s=r.record.name,a=ws(Ea(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Ea(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw ma(1,{location:e,currentLocation:t});s=r.record.name,a=ws({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:$a(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ea(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Oa(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function La(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function $a(e){return e.reduce(((e,t)=>ws(e,t.meta)),{})}function Ma(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Aa(e,t){return t.children.some((t=>t===e||Aa(e,t)))}function Pa(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace($s," "),r=e.indexOf("="),i=Fs(r<0?e:e.slice(0,r)),s=r<0?null:Fs(e.slice(r+1));if(i in t){let e=t[i];Ts(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Ba(e){let t="";for(let n in e){const o=e[n];if(n=Ds(n).replace(Os,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ts(o)?o.map((e=>e&&Ds(e))):[o&&Ds(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Ra(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ts(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Ia=Symbol(""),Na=Symbol(""),ja=Symbol(""),Va=Symbol(""),Da=Symbol("");function Ha(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Fa(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(ma(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(ma(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function qa(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(Fa(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Fa(c,n,o,a,e,r)()}))))}}var s;return i}function Wa(e){const t=gr(ja),n=gr(Va),o=xi((()=>t.resolve(rn(e.to)))),r=xi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Us.bind(null,r));if(s>-1)return s;const a=Ua(e[t-2]);return t>1&&Ua(r)===a&&i[i.length-1].path!==a?i.findIndex(Us.bind(null,e[t-2])):s})),i=xi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Ts(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=xi((()=>r.value>-1&&r.value===n.matched.length-1&&Ys(n.params,o.value.params)));return{route:o,href:xi((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[rn(e.replace)?"replace":"push"](rn(e.to)).catch(Ss):Promise.resolve()}}}const za=go({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Wa,setup(e,{slots:t}){const n=Vt(Wa(e)),{options:o}=gr(ja),r=xi((()=>({[Ya(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Ya(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Si("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Ua(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ya=(e,t,n)=>null!=e?e:null!=t?t:n;function Xa(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Ka=go({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=gr(Da),r=xi((()=>e.route||o.value)),i=gr(Na,0),s=xi((()=>{let e=rn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=xi((()=>r.value.matched[s.value]));hr(Na,xi((()=>s.value+1))),hr(Ia,a),hr(Da,r);const l=tn();return Gn((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Us(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return Xa(n.default,{Component:c,route:o});const u=s.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,p=Si(c,ws({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return Xa(n.default,{Component:p,route:o})||p}}});function Ga(e){const t=ka(e.routes,e),n=e.parseQuery||Pa,o=e.stringifyQuery||Ba,r=e.history,i=Ha(),s=Ha(),a=Ha(),l=nn(pa,!0);let c=pa;bs&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=xs.bind(null,(e=>""+e)),d=xs.bind(null,Hs),p=xs.bind(null,Fs);function f(e,i){if(i=ws({},i||l.value),"string"==typeof e){const o=Ws(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return ws(o,s,{params:p(s.params),hash:Fs(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=ws({},e,{path:Ws(n,e.path,i.path).path});else{const t=ws({},e.params);for(const e in t)null==t[e]&&delete t[e];s=ws({},e,{params:d(t)}),i.params=d(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(p(a.params));const f=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,ws({},e,{hash:(h=c,Vs(h).replace(Rs,"{").replace(Ns,"}").replace(Ps,"^")),path:a.path}));var h;const g=r.createHref(f);return ws({fullPath:f,hash:c,query:o===Ba?Ra(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Ws(n,e,l.value.path):ws({},e)}function g(e,t){if(c!==e)return ma(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),ws({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=f(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(ws(h(u),{state:"object"==typeof u?ws({},i,u.state):i,force:s,replace:a}),t||n);const d=n;let p;return d.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Us(t.matched[o],n.matched[r])&&Ys(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(p=ma(16,{to:d,from:r}),M(r,r,!0,!1)),(p?Promise.resolve(p):w(d,r)).catch((e=>va(e)?va(e,2)?e:$(e):L(e,d,r))).then((e=>{if(e){if(va(e,2))return y(ws({replace:a},h(e.to),{state:"object"==typeof e.to?ws({},i,e.to.state):i,force:s}),t||d)}else e=S(d,r,!0,a,i);return x(d,r,e),e}))}function _(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function b(e){const t=B.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>Us(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>Us(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=qa(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Fa(o,e,t))}));const l=_.bind(null,e,t);return n.push(l),I(n).then((()=>{n=[];for(const o of i.list())n.push(Fa(o,e,t));return n.push(l),I(n)})).then((()=>{n=qa(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Fa(o,e,t))}));return n.push(l),I(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(Ts(o.beforeEnter))for(const r of o.beforeEnter)n.push(Fa(r,e,t));else n.push(Fa(o.beforeEnter,e,t));return n.push(l),I(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=qa(a,"beforeRouteEnter",e,t,b),n.push(l),I(n)))).then((()=>{n=[];for(const o of s.list())n.push(Fa(o,e,t));return n.push(l),I(n)})).catch((e=>va(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>b((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===pa,c=bs?history.state:{};n&&(o||a?r.replace(e.fullPath,ws({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,M(e,t,n,a),$()}let T;function C(){T||(T=r.listen(((e,t,n)=>{if(!R.listening)return;const o=f(e),i=v(o);if(i)return void y(ws(i,{replace:!0}),o).catch(Ss);c=o;const s=l.value;var a,u;bs&&(a=ia(s.fullPath,n.delta),u=oa(),sa.set(a,u)),w(o,s).catch((e=>va(e,12)?e:va(e,2)?(y(e.to,o).then((e=>{va(e,20)&&!n.delta&&n.type===Gs.pop&&r.go(-1,!1)})).catch(Ss),Promise.reject()):(n.delta&&r.go(-n.delta,!1),L(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!va(e,8)?r.go(-n.delta,!1):n.type===Gs.pop&&va(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(Ss)})))}let k,E=Ha(),O=Ha();function L(e,t,n){$(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function $(e){return k||(k=!e,C(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function M(t,n,o,r){const{scrollBehavior:i}=e;if(!bs||!i)return Promise.resolve();const s=!o&&function(e){const t=sa.get(e);return sa.delete(e),t}(ia(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return wn().then((()=>i(t,n,s))).then((e=>e&&ra(e))).catch((e=>L(e,t,n)))}const A=e=>r.go(e);let P;const B=new Set,R={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return da(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:f,options:e,push:m,replace:function(e){return m(ws(h(e),{replace:!0}))},go:A,back:()=>A(-1),forward:()=>A(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:O.add,isReady:function(){return k&&l.value!==pa?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",za),e.component("RouterView",Ka),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>rn(l)}),bs&&!P&&l.value===pa&&(P=!0,m(r.location).catch((e=>{})));const t={};for(const o in pa)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(ja,this),e.provide(Va,Dt(t)),e.provide(Da,l);const n=e.unmount;B.add(e),e.unmount=function(){B.delete(e),B.size<1&&(c=pa,T&&T(),T=null,l.value=pa,P=!1,k=!1),n()}}};function I(e){return e.reduce(((e,t)=>e.then((()=>b(t)))),Promise.resolve())}return R}function Ja(){return gr(Va)}const Za=["{","}"];const Qa=/^(?:\d)+/,el=/^(?:\w)+/;const tl=Object.prototype.hasOwnProperty,nl=(e,t)=>tl.call(e,t),ol=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Za){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=Qa.test(t)?"list":a&&el.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function rl(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class il{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||ol,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=rl(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{nl(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=rl(t,this.messages))&&(o=this.messages[t]):n=t,nl(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function sl(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Fu?Fu():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new il({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Mf().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}const al=re((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let ll;function cl(){if(!ll){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,ll=sl(e),al()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>ll.add(e,__uniConfig.locales[e]))),ll.setLocale(e)}}return ll}function ul(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const dl=re((()=>{const e="uni.async.",t=["error"];cl().add("en",ul(e,t,["The connection timed out, click the screen to try again."]),!1),cl().add("es",ul(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),cl().add("fr",ul(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),cl().add("zh-Hans",ul(e,t,["连接服务器超时，点击屏幕重试"]),!1),cl().add("zh-Hant",ul(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),pl=re((()=>{const e="uni.showToast.",t=["unpaired"];cl().add("en",ul(e,t,["Please note showToast must be paired with hideToast"]),!1),cl().add("es",ul(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),cl().add("fr",ul(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),cl().add("zh-Hans",ul(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),cl().add("zh-Hant",ul(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),fl=re((()=>{const e="uni.showLoading.",t=["unpaired"];cl().add("en",ul(e,t,["Please note showLoading must be paired with hideLoading"]),!1),cl().add("es",ul(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),cl().add("fr",ul(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),cl().add("zh-Hans",ul(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),cl().add("zh-Hant",ul(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),hl=re((()=>{const e="uni.showModal.",t=["cancel","confirm"];cl().add("en",ul(e,t,["Cancel","OK"]),!1),cl().add("es",ul(e,t,["Cancelar","OK"]),!1),cl().add("fr",ul(e,t,["Annuler","OK"]),!1),cl().add("zh-Hans",ul(e,t,["取消","确定"]),!1),cl().add("zh-Hant",ul(e,t,["取消","確定"]),!1)}));function gl(e){const t=new Oe;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let ml=1;const vl=Object.create(null);function yl(e,t){return e+"."+t}function _l({id:e,name:t,args:n},o){t=yl(o,t);const r=t=>{e&&og.publishHandler("invokeViewApi."+e,t)},i=vl[t];i?i(n,r):r({})}const bl=c(gl("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=og,i=n?ml++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),wl=ge(!0);let xl;function Sl(){xl&&(clearTimeout(xl),xl=null)}let Tl=0,Cl=0;function kl(e){if(Sl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Tl=t,Cl=n,xl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function El(e){if(!xl)return;if(1!==e.touches.length)return Sl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Tl)>10||Math.abs(n-Cl)>10?Sl():void 0}function Ol(e,t){const n=Number(e);return isNaN(n)?t:n}function Ll(){const e=__uniConfig.globalStyle||{},t=Ol(e.rpxCalcMaxDeviceWidth,960),n=Ol(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function $l(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ml,Al,Pl=["top","left","right","bottom"],Bl={};function Rl(){return Al="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Il(){if(Al="string"==typeof Al?Al:Rl()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Pl.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Ml=!0}else Pl.forEach((function(e){Bl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Al+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){jl.length||setTimeout((function(){var e={};jl.forEach((function(t){e[t]=Bl[t]})),jl.length=0,Vl.forEach((function(t){t(e)}))}),0);jl.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Bl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Nl(e){return Ml||Il(),Bl[e]}var jl=[];var Vl=[];const Dl=$l({get support(){return 0!=("string"==typeof Al?Al:Rl()).length},get top(){return Nl("top")},get left(){return Nl("left")},get right(){return Nl("right")},get bottom(){return Nl("bottom")},onChange:function(e){Rl()&&(Ml||Il(),"function"==typeof e&&Vl.push(e))},offChange:function(e){var t=Vl.indexOf(e);t>=0&&Vl.splice(t,1)}}),Hl=ms((()=>{}),["prevent"]),Fl=ms((e=>{}),["stop"]);function ql(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Wl(){const e=ql(document.documentElement.style,"--window-top");return e?e+Dl.top:0}function zl(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function Ul(e){return Symbol(e)}function Yl(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function Xl(e,t=!1){if(t)return function(e){if(!Yl(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>Hu(parseFloat(t))+"px"))}(e);if(v(e)){const t=parseInt(e)||0;return Yl(e)?Hu(t):t}return e}function Kl(e){return e.$page}function Gl(e){return 0===e.tagName.indexOf("UNI-")}const Jl="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Zl="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",Ql="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z";function ec(e,t="#000",n=27){return Qr("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Qr("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function tc(){{const{$pageInstance:o}=di();return o&&(e=o.proxy,(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id))}var e,t,n}function nc(){const e=Ed(),t=e.length;if(t)return e[t-1]}function oc(){var e;const t=null==(e=nc())?void 0:e.$page;if(t)return t.meta}function rc(){const e=oc();return e?e.id:-1}function ic(){const e=nc();if(e)return e.$vm}const sc=["navigationBar","pullToRefresh"];function ac(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);sc.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function lc(e,t,n){if(v(e))n=t,t=e,e=ic();else if("number"==typeof e){const t=Ed().find((t=>Kl(t).id===e));e=t?t.$vm:ic()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function cc(e){e.preventDefault()}let uc,dc=0;function pc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-dc)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(dc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(uc=setTimeout(s,300))),o=!1};return function(){clearTimeout(uc),o||requestAnimationFrame(s),o=!0}}function fc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return fc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),oe(i.concat(n).join("/"))}function hc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function gc(){Ll(),pe(Gl),window.addEventListener("touchstart",kl,wl),window.addEventListener("touchmove",El,wl),window.addEventListener("touchend",Sl,wl),window.addEventListener("touchcancel",Sl,wl)}class mc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(le(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&le(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=bc(this.$el.querySelector(e));return t?vc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=bc(n[o]);e&&t.push(vc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||v(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:$(n);(v(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(v(e)&&(e=q(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];m(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&og.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function vc(e,t=!0){if(t&&e&&(e=ae(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new mc(e)),e.$el.__wxsComponentDescriptor}function yc(e,t){return vc(e,t)}function _c(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>yc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=ae(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,yc(r,!1)]}}function bc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function wc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=me(t?r:function(e){for(;!Gl(e);)e=e.parentElement;return e}(r)),a=me(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e instanceof CustomEvent&&S(e.detail)&&(l.detail=e.detail),e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function xc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Sc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Tc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!Gl(o);if(r)return _c(e,t,n,!1)||[e];const i=wc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=Wl();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[xc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=Wl();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[xc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=Wl();i.touches=Sc(e.touches,t),i.changedTouches=Sc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return _c(i,t,n)||[i]},createNativeEvent:wc},Symbol.toStringTag,{value:"Module"});function Cc(e){!function(e){const t=e.globalProperties;c(t,Tc),t.$gcd=yc}(e._context.config)}let kc=1;function Ec(e){return(e||rc())+".invokeViewApi"}const Oc=c(gl("view"),{invokeOnCallback:(e,t)=>rg.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=rg,s=o?kc++:0;o&&r("invokeViewApi."+s,o,!0),i(Ec(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=rg,a=kc++,l="invokeViewApi."+a;return r(l,n),s(Ec(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Lc(e){lc(nc(),"onResize",e),rg.invokeOnCallback("onWindowResize",e)}function $c(e){const t=nc();lc(Mf(),"onShow",e),lc(t,"onShow")}function Mc(){lc(Mf(),"onHide"),lc(nc(),"onHide")}const Ac=["onPageScroll","onReachBottom"];function Pc(){Ac.forEach((e=>rg.subscribe(e,function(e){return(t,n)=>{lc(parseInt(n),e,t)}}(e))))}function Bc(){!function(){const{on:e}=rg;e("onResize",Lc),e("onAppEnterForeground",$c),e("onAppEnterBackground",Mc)}(),Pc()}function Rc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new xe(this.$page.id)),e.eventChannel}}function Ic(e){e._context.config.globalProperties.getOpenerEventChannel=Rc}function Nc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function jc(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${Hu(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function Vc(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(jc)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?jc(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const Dc={props:["animation"],watch:{animation:{deep:!0,handler(){Vc(this)}}},mounted(){Vc(this)}},Hc=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Dc),Fc(e)},Fc=e=>(e.__reserved=!0,e.compatConfig={MODE:3},go(e));function qc(e){return e.__wwe=!0,e}function Wc(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=me(n),{type:t.__evName||o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const zc={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function Uc(e){const t=tn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:qc((function(e){e.touches.length>1||s(e)})),onMousedown:qc((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:qc((function(){a()})),onMouseup:qc((function(){r&&l()})),onTouchcancel:qc((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function Yc(e,t){return v(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const Xc=Ul("uf"),Kc=Ul("ul");function Gc(e,t){Jc(e.id,t),Gn((()=>e.id),((e,n)=>{Zc(n,t,!0),Jc(e,t,!0)})),Vo((()=>{Zc(e.id,t)}))}function Jc(e,t,n){const o=tc();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&og.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?og.on(r,t[r]):e&&og.on(`uni-${r}-${o}-${e}`,t[r])}))}function Zc(e,t,n){const o=tc();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&og.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?og.off(r,t[r]):e&&og.off(`uni-${r}-${o}-${e}`,t[r])}))}const Qc=Hc({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=tn(null),o=gr(Xc,!1),{hovering:r,binding:i}=Uc(e),s=qc(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=gr(Kc,!1);return a&&(a.addHandler(s),jo((()=>{a.removeHandler(s)}))),Gc(e,{"label-click":s}),()=>{const o=e.hoverClass,a=Yc(e,"disabled"),l=Yc(e,"loading"),c=Yc(e,"plain"),u=o&&"none"!==o;return Qr("uni-button",si({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),eu=Ul("upm");function tu(){return gr(eu)}function nu(e){const t=function(e){return Vt(function(e){{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Ed().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(ac(Ja().meta,e)))))}(e);return hr(eu,t),t}function ou(){return Ja()}function ru(){return history.state&&history.state.__id__||1}const iu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function su(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function au(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let lu=1;const cu={};function uu(e,t,n){if("number"==typeof e){const o=cu[e];if(o)return o.keepAlive||delete cu[e],o.callback(t,n)}return t}const du="success",pu="fail",fu="complete";function hu(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];m(o)&&(t[n]=au(o),delete e[n])}return t}(t),a=m(r),l=m(i),c=m(s),u=lu++;return function(e,t,n,o=!1){cu[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),m(n)&&n(u),u.errMsg===e+":ok"?(m(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const gu="success",mu="fail",vu="complete",yu={},_u={};function bu(e,t){return function(n){return e(n,t)||n}}function wu(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(bu(i,n));else{const e=i(t,n);if(b(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function xu(e,t={}){return[gu,mu,vu].forEach((n=>{const o=e[n];if(!f(o))return;const r=t[n];t[n]=function(e){wu(o,e,t).then((e=>m(r)&&r(e)||e))}})),t}function Su(e,t){const n=[];f(yu.returnValue)&&n.push(...yu.returnValue);const o=_u[e];return o&&f(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Tu(e){const t=Object.create(null);Object.keys(yu).forEach((e=>{"returnValue"!==e&&(t[e]=yu[e].slice())}));const n=_u[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Cu(e,t,n,o){const r=Tu(e);if(r&&Object.keys(r).length){if(f(r.invoke)){return wu(r.invoke,n).then((n=>t(xu(Tu(e),n),...o)))}return t(xu(r,n),...o)}return t(n,...o)}function ku(e,t){return(n={},...o)=>function(e){return!(!S(e)||![du,pu,fu].find((t=>m(e[t]))))}(n)?Su(e,Cu(e,t,c({},n),o)):Su(e,new Promise(((r,i)=>{Cu(e,t,c({},n,{success:r,fail:i}),o)})))}function Eu(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,uu(e,c({errMsg:i},o))}function Ou(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(v(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(m(s)){const o=s(e[0][t],n);if(v(o))return o}else p(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Lu(e,t,n,o){return n=>{const r=hu(e,n,o),i=Ou(0,[n],0,o);return i?Eu(r,e,i):t(n,{resolve:t=>function(e,t,n){return uu(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Eu(r,e,function(e){return!e||v(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function $u(e,t,n,o){return ku(e,Lu(e,t,0,o))}function Mu(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Ou(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Au(e,t,n,o){return ku(e,function(e,t,n,o){return Lu(e,t,0,o)}(e,t,0,o))}let Pu=!1,Bu=0,Ru=0,Iu=960,Nu=375,ju=750;function Vu(){let e,t,n;{const{windowWidth:o,pixelRatio:r,platform:i}=function(){const e=Zd(),t=tp(ep(e,Qd(e)));return{platform:Yd?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();e=o,t=r,n=i}Bu=e,Ru=t,Pu="ios"===n}function Du(e,t){const n=Number(e);return isNaN(n)?t:n}const Hu=Mu(0,((e,t)=>{if(0===Bu&&(Vu(),function(){const e=__uniConfig.globalStyle||{};Iu=Du(e.rpxCalcMaxDeviceWidth,960),Nu=Du(e.rpxCalcBaseDeviceWidth,375),ju=Du(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Bu;n=e===ju||n<=Iu?n:Nu;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Ru&&Pu?.5:1),e<0?-o:o})),Fu=Mu(0,(()=>{const e=Mf();return e&&e.$vm?e.$vm.$locale:cl().getLocale()})),qu={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const Wu=["wgs84","gcj02"],zu={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===Wu.indexOf(e)?t.type=Wu[0]:t.type=e},altitude(e,t){t.altitude=e||!1}}},Uu=(Boolean,"json"),Yu=["text","arraybuffer"],Xu=encodeURIComponent;ArrayBuffer,Boolean;const Ku={formatArgs:{method(e,t){t.method=su((e||"").toUpperCase(),iu)},data(e,t){t.data=e||""},url(e,t){t.method===iu[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(p(t,a)){let e=t[a];null==e?e="":S(e)&&(e=JSON.stringify(e)),s[Xu(a)]=Xu(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==iu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Uu).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Yu.indexOf(t.responseType)&&(t.responseType="text")}}};const Gu={url:{type:String,required:!0}},Ju=(Qu(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Qu(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),nd("navigateTo")),Zu={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Ed().length-1,e)}}};function Qu(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let ed;function td(){ed=""}function nd(e){return{formatArgs:{url:od(e)},beforeAll:td}}function od(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=Ed();return n.length&&(t=Kl(n[n.length-1]).route),fc(t,e)}(t)).split("?")[0],r=hc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!v(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(ed===t&&"appLaunch"!==n.openType)return`${ed} locked`;__uniConfig.ready&&(ed=t)}else if(r.meta.isTabBar){const e=Ed(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const rd={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},id=(Boolean,{formatArgs:{title:"",mask:!1}}),sd=(Boolean,{beforeInvoke(){hl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!p(t,"cancelText")){const{t:e}=cl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!p(t,"confirmText")){const{t:e}=cl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),ad=["success","loading","none","error"],ld=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=su(e,ad)},image(e,t){t.image=e?Wd(e):""},duration:1500,mask:!1}});function cd(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function ud(){const e=kd().keys();for(const t of e)Ld(t)}const dd=Au("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(wd.handledBeforeEntryPageRoutes)return ud(),pd({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);Cd.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,nd("reLaunch"));function pd({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=Mf().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:be(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++$d,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(va(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new xe(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function fd(){if(wd.handledBeforeEntryPageRoutes)return;wd.handledBeforeEntryPageRoutes=!0;const e=[...xd];xd.length=0,e.forEach((({args:e,resolve:t,reject:n})=>pd(e).then(t).catch(n)));const t=[...Sd];Sd.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(function(){const e=ic();if(!e)return;const t=kd(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Ld(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,lc(e,"onHide"))}(),pd(e,function(e){const t=kd().values();for(const n of t){const t=bd(n);if(cd(e,t))return n.$.__isActive=!0,t.id}}(e.url)).then(t).catch(n))));const n=[...Td];Td.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(function(){const e=nc();if(!e)return;const t=bd(e);Ld(Pd(t.path,t.id))}(),pd(e).then(t).catch(n))));const o=[...Cd];Cd.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(ud(),pd(e).then(t).catch(n))))}function hd(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const gd=hd("top:env(a)"),md=hd("top:constant(a)"),vd=(()=>gd?"env":md?"constant":"")();function yd(e){let t=0;var n,o;"custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),zl({"--window-top":(o=t,vd?`calc(${o}px + ${vd}(safe-area-inset-top))`:`${o}px`),"--window-bottom":(n=0,vd?`calc(${n}px + ${vd}(safe-area-inset-bottom))`:`${n}px`)})}const _d=new Map;function bd(e){return e.$page}const wd={handledBeforeEntryPageRoutes:!1},xd=[],Sd=[],Td=[],Cd=[];function kd(){return _d}function Ed(){return Od()}function Od(){const e=[],t=_d.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Ld(e,t=!0){const n=_d.get(e);n.$.__isUnload=!0,lc(n,"onUnload"),_d.delete(e),t&&function(e){const t=Bd.get(e);t&&(Bd.delete(e),Rd.pruneCacheEntry(t))}(e)}let $d=ru();function Md(e){const t=tu();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=Me(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:oe(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function Ad(e){const t=Md(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),_d.set(Pd(t.path,t.id),e),1===_d.size&&setTimeout((()=>{fd()}),0)}function Pd(e,t){return e+"$$"+t}const Bd=new Map,Rd={get:e=>Bd.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;Rd.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);o&&o>t&&(Rd.delete(n),Rd.pruneCacheEntry(e),wn((()=>{_d.forEach(((e,t)=>{e.$.isUnmounted&&_d.delete(t)}))})))}))}(e),Bd.set(e,t)},delete(e){Bd.get(e)&&Bd.delete(e)},forEach(e){Bd.forEach(e)}};function Id(e,t){!function(e){const t=jd(e),{body:n}=document;Vd&&n.removeAttribute(Vd),t&&n.setAttribute(t,""),Vd=t}(e),yd(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),Fd(e,t)}function Nd(e){const t=jd(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function jd(e){return e.type.__scopeId}let Vd;const Dd=!!(()=>{let e=!1;try{const t={};Object.defineProperty(t,"passive",{get(){e=!0}}),window.addEventListener("test-passive",(()=>{}),t)}catch(t){}return e})()&&{passive:!1};let Hd;function Fd(e,t){if(document.removeEventListener("touchmove",cc),Hd&&document.removeEventListener("scroll",Hd),t.disableScroll)return document.addEventListener("touchmove",cc,Dd);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=bd(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&og.publishHandler("onPageScroll",{scrollTop:o},e),n&&og.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>og.publishHandler("onReachBottom",{},s)),Hd=pc(i),requestAnimationFrame((()=>document.addEventListener("scroll",Hd)))}function qd(e){const{base:t}=__uniConfig.router;return 0===oe(e).indexOf(t)?oe(e):t+e}function Wd(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return qd(e.slice(1));e="https:"+e}if(ee.test(e)||te.test(e)||0===e.indexOf("blob:"))return e;const o=Od();return o.length?qd(fc(bd(o[o.length-1]).route,e).slice(1)):e}const zd=navigator.userAgent,Ud=/android/i.test(zd),Yd=/iphone|ipad|ipod/i.test(zd),Xd=zd.match(/Windows NT ([\d|\d.\d]*)/i),Kd=/Macintosh|Mac/i.test(zd),Gd=/Linux|X11/i.test(zd),Jd=Kd&&navigator.maxTouchPoints>0;function Zd(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Qd(e){return e&&90===Math.abs(window.orientation)}function ep(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function tp(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}const np=Nc(),op=Nc();const rp=Hc({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=tn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Vt({width:-1,height:-1});return Gn((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){To(o),Ro((()=>{t.initial&&wn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Qr("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Qr("div",{onScroll:r},[Qr("div",null,null)],40,["onScroll"]),Qr("div",{onScroll:r},[Qr("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function ip(){}const sp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function ap(e,t,n){function o(e){const t=xi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",ip,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",ip,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Gn((()=>t.value),(e=>e&&o(e)))}const lp={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},cp={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},up={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},dp=Hc({name:"Image",props:lp,setup(e,{emit:t}){const n=tn(null),o=function(e,t){const n=tn(""),o=xi((()=>{let e="auto",o="";const r=up[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Vt({rootEl:e,src:xi((()=>t.src?Wd(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Ro((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=Wc(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=cp[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){pp&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Gn((()=>t.mode),((e,t)=>{cp[t]&&r(),cp[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),wn((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Gn((()=>e.src),(e=>l(e))),Gn((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Ro((()=>l(e.src))),jo((()=>c()))}(o,e,n,i,r),()=>Qr("uni-image",{ref:n},[Qr("div",{style:o.modeStyle},null,4),cp[e.mode]?Qr(rp,{onResize:i},null,8,["onResize"]):Qr("span",null,null)],512)}});const pp="Google Inc."===navigator.vendor;const fp=ge(!0),hp=[];let gp=0,mp=!1;const vp=e=>hp.forEach((t=>t.userAction=e));function yp(){const e=Vt({userAction:!1});return Ro((()=>{!function(e={userAction:!1}){mp||(["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!gp&&vp(!0),gp++,setTimeout((()=>{!--gp&&vp(!1)}),0)}),fp)})),mp=!0);hp.push(e)}(e)})),jo((()=>{!function(e){const t=hp.indexOf(e);t>=0&&hp.splice(t,1)}(e)})),{state:e}}function _p(){const e=Vt({attrs:{}});return Ro((()=>{let t=di();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function bp(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}const wp=function(){var e,t,n;e=rc(),n=bp,t=yl(e,t="getSelectedTextRange"),vl[t]||(vl[t]=n)};function xp(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const Sp=["none","text","decimal","numeric","tel","search","email","url"],Tp=c({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Sp.indexOf(e)},cursorColor:{type:String,default:""}},sp),Cp=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function kp(e,t,n,o){let r=null;r=we((n=>{t.value=xp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),Gn((()=>e.modelValue),r),Gn((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Bo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Ep(e,t){yp();const n=xi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Gn((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Ro((()=>{n.value&&wn(o)}))}function Op(e,t,n,o){wp();const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=tn(null),r=Wc(t,n),i=xi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=xi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=xi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=xi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=xp(e.modelValue,e.type)||xp(e.value,e.type);const u=Vt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return Gn((()=>u.focus),(e=>n("update:focus",e))),Gn((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=kp(e,i,n,s);Ep(e,r),ap(0,r);const{state:l}=_p();!function(e,t){const n=gr(Xc,!1);if(!n)return;const o=di(),r={submit(){const n=o.proxy;return[n[e],v(t)?n[t]:t.value]},reset(){v(t)?o.proxy[t]="":t.value=""}};n.addField(r),jo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Gn([()=>t.selectionStart,()=>t.selectionEnd],s),Gn((()=>t.cursor),a),Gn((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),m(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Lp=c({},Tp,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),$p=re((()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}}));function Mp(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&$p()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Ap=Hc({name:"Input",props:Lp,emits:["confirm",...Cp],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=xi((()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=xi((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf($(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=tn(null!=t?t.toLocaleString():"");return Gn((()=>e.modelValue),(e=>{n.value=null!=e?e.toLocaleString():""})),Gn((()=>e.value),(e=>{n.value=null!=e?e.toLocaleString():""})),n}return tn("")}(e,i),l={fn:null};const c=tn(null),{fieldRef:u,state:d,scopedAttrsState:p,fixDisabledColor:f,trigger:h}=Op(e,c,t,((t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=Mp(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=Mp(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}}));Gn((()=>d.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const g=["number","digit"],m=xi((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&f?Qr("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):Qr("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return Qr("uni-input",{ref:c},[Qr("div",{class:"uni-input-wrapper"},[to(Qr("div",si(p.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Wi,!(d.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?Qr("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});function Pp(e){const t=[];return f(e)&&e.forEach((e=>{Yr(e)?e.type===Rr?t.push(...Pp(e.children)):t.push(e):f(e)&&t.push(...Pp(e))})),t}const Bp=function(e,t,n,o){e.addEventListener(t,(e=>{m(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Rp,Ip;function Np(e,t,n){jo((()=>{document.removeEventListener("mousemove",Rp),document.removeEventListener("mouseup",Ip)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Bp(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),Bp(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),Bp(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const d=Rp=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),Bp(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const p=Ip=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",p),Bp(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}const jp=Hc({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return f(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=tn(null),r=tn(null),i=Wc(o,n),s=function(e){const t=Vt([...e.value]),n=Vt({value:t,height:34});return Gn((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=tn(null);Ro((()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)}));let l=tn([]),c=tn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==Nr));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return hr("getPickerViewColumn",(function(e){return xi({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),hr("pickerViewProps",e),hr("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Pp(e);l.value=t,wn((()=>{c.value=t}))}return Qr("uni-picker-view",{ref:o},[Qr(rp,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),Qr("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class Vp{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Dp(e,t,n){return e>t-n&&e<t+n}function Hp(e,t){return Dp(e,0,t)}class Fp{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Hp(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(Hp(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Hp(t,.4)&&(t=0),Hp(o,.4)&&(o=0),o+=this._endPosition),this._solution&&Hp(o-e,.4)&&Hp(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Dp(this.x(),this._endPosition,.4)&&Hp(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class qp{constructor(e,t,n){this._extent=e,this._friction=t||new Vp(.01),this._spring=n||new Fp(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Wp{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new qp(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(m(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const zp=Hc({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=tn(null),r=tn(null),i=gr("getPickerViewColumn"),s=di(),a=i?i(s):tn(0),l=gr("pickerViewProps"),c=gr("pickerViewState"),u=tn(34),d=tn(null);Ro((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const p=xi((()=>(c.height-u.value)/2)),{state:f}=_p();let h;const g=Vt({current:a.value,length:0});let m;function v(){h&&!m&&(m=!0,wn((()=>{m=!1;let e=Math.min(g.current,g.length-1);e=Math.max(e,0),h.update(e*u.value,void 0,u.value)})))}Gn((()=>a.value),(e=>{e!==g.current&&(g.current=e,v())})),Gn((()=>g.current),(e=>a.value=e)),Gn([()=>u.value,()=>g.length,()=>c.height],v);let y=0;function _(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(g.current+(t<0?-1:1),g.length-1);g.current=e=Math.max(e,0),h.scrollTo(e*u.value)}else y=t;e.preventDefault()}function b({clientY:e}){const t=o.value;if(!h.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(g.current+t,g.length-1);g.current=r=Math.max(r,0),h.scrollTo(r*u.value)}}}const w=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Wp(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Vp(1e-4),spring:new Fp(2,90,20),onSnap:e=>{isNaN(e)||e===g.current||(g.current=e)}});h=n,Np(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),v()};return Ro(w),()=>{const e=t.default&&t.default();g.length=Pp(e).length;const n=`${p.value}px 0`;return Qr("uni-picker-view-column",{ref:o},[Qr("div",{onWheel:_,onClick:b,class:"uni-picker-view-group"},[Qr("div",si(f.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${p.value}px;${l.maskStyle}`}),null,16),Qr("div",si(f.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Qr(rp,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),Qr("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}}),Up=Hc({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=tn(null),o=xi((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=xi((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return Qr("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?Qr("div",{class:"uni-scroll-view-refresh"},[Qr("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?Qr("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Qr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Qr("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?Qr("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Qr("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?Qr("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),Yp=ge(!0),Xp=Hc({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=tn(null),i=tn(null),s=tn(null),a=tn(null),l=Wc(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=xi((()=>Number(e.scrollTop)||0)),n=xi((()=>Number(e.scrollLeft)||0));return{state:Vt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:p,realScrollY:f,_scrollLeftChanged:h,_scrollTopChanged:g}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,d=!1,p=()=>{};const f=xi((()=>e.scrollX)),h=xi((()=>e.scrollY)),g=xi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=xi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",p),i.removeEventListener("webkitTransitionEnd",p),p=()=>x(e,t),i.addEventListener("transitionend",p),i.addEventListener("webkitTransitionEnd",p),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),f.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function _(t){h.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function b(t){f.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(f.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=f.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",p),a.value.removeEventListener("webkitTransitionEnd",p)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:C.y-T.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:C.y-T.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:C.y-T.y}))}t.refreshState=n}}let T={x:0,y:0},C={x:0,y:e.refresherThreshold};return Ro((()=>{wn((()=>{_(n.value),b(o.value)})),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===T)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-T.x)>Math.abs(i-T.y))if(f.value){if(0===l.scrollLeft&&o>T.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<T.x)return void(a=!1);a=!0}else a=!1;else if(h.value)if(0===l.scrollTop&&i>T.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<T.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-T.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},p=function(e){1===e.touches.length&&(T={x:e.touches[0].pageX,y:e.touches[0].pageY})},g=function(n){C={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),T={x:0,y:0},C={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",p,Yp),s.value.addEventListener("touchmove",l,ge(!1)),s.value.addEventListener("scroll",i,ge(!1)),s.value.addEventListener("touchend",g,Yp),jo((()=>{s.value.removeEventListener("touchstart",p),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",g)}))})),To((()=>{h.value&&(s.value.scrollTop=t.lastScrollTop),f.value&&(s.value.scrollLeft=t.lastScrollLeft)})),Gn(n,(e=>{_(e)})),Gn(o,(e=>{b(e)})),Gn((()=>e.scrollIntoView),(e=>{w(e)})),Gn((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:f,realScrollY:h,_scrollTopChanged:_,_scrollLeftChanged:b}}(e,c,u,d,l,r,i,a,t),m=xi((()=>{let e="";return p.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",f.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=xi((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:p}=c;return Qr("uni-scroll-view",{ref:r},[Qr("div",{ref:s,class:"uni-scroll-view"},[Qr("div",{ref:i,style:m.value,class:v.value},[t?Qr(Up,{refreshState:p,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,Qr("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});const Kp=Hc({name:"Slider",props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},emits:["changing","change"],setup(e,{emit:t}){const n=tn(null),o=tn(null),r=tn(null),i=tn(Number(e.value));Gn((()=>e.value),(e=>{i.value=Number(e)}));const s=Wc(n,t),a=function(e,t){const n=()=>{return n=t.value,o=e.min,r=e.max,r=Number(r),o=Number(o),100*(n-o)/(r-o)+"%";var n,o,r},o=()=>"#e9e9e9"!==e.backgroundColor?e.backgroundColor:"#007aff"!==e.color?e.color:"#007aff",r=()=>"#007aff"!==e.activeColor?e.activeColor:"#e9e9e9"!==e.selectedColor?e.selectedColor:"#e9e9e9";return{setBgColor:xi((()=>({backgroundColor:o()}))),setBlockBg:xi((()=>({left:n()}))),setActiveColor:xi((()=>({backgroundColor:r(),width:n()}))),setBlockStyle:xi((()=>({width:e.blockSize+"px",height:e.blockSize+"px",marginLeft:-e.blockSize/2+"px",marginTop:-e.blockSize/2+"px",left:n(),backgroundColor:e.blockColor})))}}(e,i),{_onClick:l,_onTrack:c}=function(e,t,n,o,r){const i=n=>{e.disabled||(a(n),r("change",n,{value:t.value}))},s=t=>{const n=Number(e.max),o=Number(e.min),r=Number(e.step);return t<o?o:t>n?n:Gp.mul.call(Math.round((t-o)/r),r)+o},a=r=>{const i=Number(e.max),a=Number(e.min),l=o.value,c=getComputedStyle(l,null).marginLeft;let u=l.offsetWidth;u+=parseInt(c);const d=n.value,p=d.offsetWidth-(e.showValue?u:0),f=d.getBoundingClientRect().left,h=(r.x-f)*(i-a)/p+a;t.value=s(h)},l=n=>{if(!e.disabled)return"move"===n.detail.state?(a({x:n.detail.x}),r("changing",n,{value:t.value}),!1):"end"===n.detail.state&&r("change",n,{value:t.value})},c=gr(Xc,!1);if(c){const n={reset:()=>t.value=Number(e.min),submit:()=>{const n=["",null];return""!==e.name&&(n[0]=e.name,n[1]=t.value),n}};c.addField(n),jo((()=>{c.removeField(n)}))}return{_onClick:i,_onTrack:l}}(e,i,n,o,s);return Ro((()=>{Np(r.value,c)})),()=>{const{setBgColor:t,setBlockBg:s,setActiveColor:c,setBlockStyle:u}=a;return Qr("uni-slider",{ref:n,onClick:qc(l)},[Qr("div",{class:"uni-slider-wrapper"},[Qr("div",{class:"uni-slider-tap-area"},[Qr("div",{style:t.value,class:"uni-slider-handle-wrapper"},[Qr("div",{ref:r,style:s.value,class:"uni-slider-handle"},null,4),Qr("div",{style:u.value,class:"uni-slider-thumb"},null,4),Qr("div",{style:c.value,class:"uni-slider-track"},null,4)],4)]),to(Qr("span",{ref:o,class:"uni-slider-value"},[i.value],512),[[Wi,e.showValue]])])],8,["onClick"])}}});var Gp={mul:function(e){let t=0,n=this.toString(),o=e.toString();try{t+=n.split(".")[1].length}catch(r){}try{t+=o.split(".")[1].length}catch(r){}return Number(n.replace(".",""))*Number(o.replace(".",""))/Math.pow(10,t)}};function Jp(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,d=0,p=1,f=null,h=!1,g=0,m="";const v=xi((()=>n.value.length>t.displayMultipleItems)),y=xi((()=>e.circular&&v.value));function _(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),d=Math.max(o-(l+1),l-s,0),p=Math.max(o-(c+1),c-s,0),f=Math.min(u,d,p),h=[n,l,c][[u,d,p].indexOf(f)];t.updatePosition(h,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*p+"%")+", "+(e.vertical?100*-r*p+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),d=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function b(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){f=null}function x(){if(!f)return void(h=!1);const e=f,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){_(o),f=null,h=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}_(o+r*u*u/2),l=requestAnimationFrame(x)}function S(e,o,r){w();const i=t.duration,s=n.value.length;let a=d;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);f={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function T(){s();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=b(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function C(e){e?T():s()}return Gn([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),Gn([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),f&&(_(f.toPos),f=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);p=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();p=e.width/t.width,p>0&&p<1||(p=1)}const a=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(_(a+l-g),g=l):(_(l),e.autoplay&&T())):(u=!0,_(-t.displayMultipleItems-1))})),Gn((()=>t.interval),(()=>{c&&(s(),T())})),Gn((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const s=n.value;if(!r){const t=s.length;S(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),Gn((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),Gn((()=>e.autoplay&&!t.userTracking),C),C(e.autoplay&&!t.userTracking),Ro((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=b(d+o);e?_(g):(m="touch",t.current=r,S(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}Np(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),g=d,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&T())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),_(n)}const d=a-s||1,p=o.value;e.vertical?u(-r.dy/p.offsetHeight,-r.ddy/d):u(-r.dx/p.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),Vo((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const Zp=Hc({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=tn(null),r=Wc(o,n),i=tn(null),s=tn(null),a=function(e){return Vt({interval:xi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:xi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:xi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=xi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:Xl(e.previousMargin,!0),bottom:Xl(e.nextMargin,!0)}:{top:0,bottom:0,left:Xl(e.previousMargin,!0),right:Xl(e.nextMargin,!0)}),t})),c=xi((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],p=tn([]);function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(Xt(o))}p.value=e}hr("addSwiperContext",(function(e){d.push(e),f()}));hr("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())}));const{onSwiperDotClick:h,circularEnabled:g,swiperEnabled:m}=Jp(e,a,p,s,n,r);let v=()=>null;return v=Qp(o,e,a,h,p,g,m),()=>{const n=t.default&&t.default();return u=Pp(n),Qr("uni-swiper",{ref:o},[Qr("div",{ref:i,class:"uni-swiper-wrapper"},[Qr("div",{class:"uni-swiper-slides",style:l.value},[Qr("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Qr("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[p.value.map(((t,n,o)=>Qr("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),Qp=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,u=!1,d=tn(!1);function p(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Xn((()=>{a="auto"===t.navigation,d.value=!0!==t.navigation||a,_()})),Xn((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,u=!0,a&&(d.value=!0))}));const f={onMouseover:e=>p(e,"over"),onMouseout:e=>p(e,"out")};function h(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const g=()=>ec(Ql,t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let p=!1;if(p=t.vertical?!(r-a<u/3||l-r<u/3):!(o-i<c/3||s-o<c/3),p)return m=setTimeout((()=>{d.value=p}),300);d.value=p},y=()=>{d.value=!0};function _(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return Ro(_),function(){const e={"uni-swiper-navigation-hide":d.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Qr(Rr,null,[Qr("div",si({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},f),[g()],16,["onClick"]),Qr("div",si({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>h(e,"next",u)},f),[g()],16,["onClick"])]):null}},ef=Hc({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=tn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return Ro((()=>{const e=gr("addSwiperContext");e&&e(o)})),Vo((()=>{const e=gr("removeSwiperContext");e&&e(o)})),()=>Qr("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),tf=Hc({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=tn(null),o=tn(e.checked),r=function(e,t){const n=gr(Xc,!1),o=gr(Kc,!1),r={submit:()=>{const n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(r),Vo((()=>{n.removeField(r)})));return o}(e,o),i=Wc(n,t);Gn((()=>e.checked),(e=>{o.value=e}));const s=t=>{e.disabled||(o.value=!o.value,i("change",t,{value:o.value}))};return r&&(r.addHandler(s),jo((()=>{r.removeHandler(s)}))),Gc(e,{"label-click":s}),()=>{const{color:t,type:r}=e,i=Yc(e,"disabled"),a={};let l;return t&&o.value&&(a.backgroundColor=t,a.borderColor=t),l=o.value,Qr("uni-switch",si({id:e.id,ref:n},i,{onClick:s}),[Qr("div",{class:"uni-switch-wrapper"},[to(Qr("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:a},null,6),[[Wi,"switch"===r]]),to(Qr("div",{class:"uni-checkbox-input"},[l?ec(Jl,e.color,22):""],512),[[Wi,"checkbox"===r]])])],16,["id","onClick"])}}});const nf={ensp:" ",emsp:" ",nbsp:" "};function of(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&nf[t]&&" "===i&&(i=nf[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,nf.nbsp).replace(/&ensp;/g,nf.ensp).replace(/&emsp;/g,nf.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const rf=Hc({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=tn(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Nr){const n=of(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(ti(e)),t!==r&&o.push(Qr("br"))}))}else o.push(t)})),Qr("uni-text",{ref:n,selectable:!!e.selectable||null},[Qr("span",null,o)],8,["selectable"])}}}),sf=c({},Tp,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>lf.concat("return").includes(e)}});let af=!1;const lf=["done","go","next","search","send"];const cf=Hc({name:"Textarea",props:sf,emits:["confirm","linechange",...Cp],setup(e,{emit:t,expose:n}){const o=tn(null),r=tn(null),{fieldRef:i,state:s,scopedAttrsState:a,fixDisabledColor:l,trigger:c}=Op(e,o,t),u=xi((()=>s.value.split("\n"))),d=xi((()=>lf.includes(e.confirmType))),p=tn(0),f=tn(null);function h({height:e}){p.value=e}function g(e){"Enter"===e.key&&d.value&&e.preventDefault()}function m(t){if("Enter"===t.key&&d.value){!function(e){c("confirm",e,{value:s.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Gn((()=>p.value),(t=>{const n=o.value,i=f.value,s=r.value;let a=parseFloat(getComputedStyle(n).lineHeight);isNaN(a)&&(a=i.offsetHeight);var l=Math.round(t/a);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(s.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";af=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),s.value=e.value}}),()=>{let t=e.disabled&&l?Qr("textarea",{key:"disabled-textarea",ref:i,value:s.value,tabindex:"-1",readonly:!!e.disabled,maxlength:s.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":af},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Qr("textarea",{key:"textarea",ref:i,value:s.value,disabled:!!e.disabled,maxlength:s.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":af},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:g,onKeyup:m},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Qr("uni-textarea",{ref:o,"auto-height":e.autoHeight},[Qr("div",{ref:r,class:"uni-textarea-wrapper"},[to(Qr("div",si(a.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Wi,!s.value.length]]),Qr("div",{ref:f,class:"uni-textarea-line"},[" "],512),Qr("div",{class:"uni-textarea-compute"},[u.value.map((e=>Qr("div",null,[e.trim()?e:"."]))),Qr(rp,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?Qr("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],8,["auto-height"])}}}),uf=Hc({name:"View",props:c({},zc),setup(e,{slots:t}){const n=tn(null),{hovering:o,binding:r}=Uc(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?Qr("uni-view",si({class:o.value?i:"",ref:n},r),[zo(t,"default")],16):Qr("uni-view",{ref:n},[zo(t,"default")],512)}}});function df(e,t,n,o){m(t)&&Ao(e,t.bind(n),o)}function pf(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&("page"!==o||"component"!==t.renderer)&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!m(t))&&(Te.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];f(r)?r.forEach((e=>df(o,e,n,t))):df(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,lc(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&lc(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function ff(e,t,n){pf(e,t,n)}function hf(e,t,n){return e[t]=n}function gf(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function mf(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?lc(i.proxy,"onError",n):dn(n,0,o&&o.$.vnode,!1)}}function vf(e,t){return e?[...new Set([].concat(e,t))]:t}function yf(e){const t=e.config;var n;t.errorHandler=ke(e,mf),n=t.optionMergeStrategies,Te.forEach((e=>{n[e]=vf}));const o=t.globalProperties;o.$set=hf,o.$applyOptions=ff,o.$callMethod=gf,function(e){Ce.forEach((t=>t(e)))}(e)}function _f(e){const t=Ga({history:xf(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:wf});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(bf[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let bf=Object.create(null);const wf=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,bf[o]);if(t)return t}return{left:0,top:0};var o};function xf(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),ua(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Od(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=bd(t[r]);Ld(Pd(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const Sf={install(e){yf(e),Cc(e),Ic(e),e.config.warnHandler||(e.config.warnHandler=Tf),_f(e)}};function Tf(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const Cf={class:"uni-async-loading"},kf=Qr("i",{class:"uni-loading"},null,-1),Ef=Fc({name:"AsyncLoading",render:()=>(Hr(),Ur("div",Cf,[kf]))});function Of(){window.location.reload()}const Lf=Fc({name:"AsyncError",props:["error"],setup(){dl();const{t:e}=cl();return()=>Qr("div",{class:"uni-async-error",onClick:Of},[e("uni.async.error")],8,["onClick"])}});let $f;function Mf(){return $f}function Af(e){$f=e,Object.defineProperty($f.$.ctx,"$children",{get:()=>Od().map((e=>e.$vm))});const t=$f.$.appContext.app;t.component(Ef.name)||t.component(Ef.name,Ef),t.component(Lf.name)||t.component(Lf.name,Lf),function(e){e.$vm=e,e.$mpType="app";const t=tn(cl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}($f),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}($f),Bc(),gc()}function Pf(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=di();if(n(r.proxy),o(r),i)return i(e,t)},e}function Bf(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Pf(e.default,t):Pf(e,t)}function Rf(e){return Bf(e,{clone:!0,init:Ad,setup(e){e.$pageInstance=e;const t=ou(),n=ye(t.query);e.attrs.__pageQuery=n,bd(e.proxy).options=n,e.proxy.options=n;const o=tu();var r,i;return yd(o),e.onReachBottom=Vt([]),e.onPageScroll=Vt([]),Gn([e.onReachBottom,e.onPageScroll],(()=>{const t=nc();e.proxy===t&&Fd(e,o)}),{once:!0}),Bo((()=>{Id(e,o)})),Ro((()=>{Nd(e);const{onReady:n}=e;n&&B(n),Vf(t)})),ko((()=>{if(!e.__isVisible){Id(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&B(n),wn((()=>{Vf(t)}))}}),"ba",r),function(e,t){ko(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&B(t)}}})),i=o.id,og.subscribe(yl(i,"invokeViewApi"),_l),jo((()=>{!function(e){og.unsubscribe(yl(e,"invokeViewApi")),Object.keys(vl).forEach((t=>{0===t.indexOf(e+".")&&delete vl[t]}))}(o.id)})),n}})}function If(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=uh(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";rg.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Nf(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&rg.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function jf(){const{emit:e}=rg;"visible"===document.visibilityState?e("onAppEnterForeground",c({},op)):e("onAppEnterBackground")}function Vf(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&lc("onTabItemTap",{index:n,text:t,pagePath:o})}let Df,Hf=0;function Ff(e,t,n,o){var r,i=document.createElement("script"),s=t.callback||"callback",a="__uni_jsonp_callback_"+Hf++,l=t.timeout||3e4;function c(){clearTimeout(r),delete window[a],i.remove()}window[a]=e=>{m(n)&&n(e),c()},i.onerror=()=>{m(o)&&o(),c()},r=setTimeout((function(){m(o)&&o(),c()}),l),i.src=e+(e.indexOf("?")>=0?"&":"?")+s+"="+a,document.body.appendChild(i)}function qf(e){function t(){const e=this.div;this.getPanes().floatPane.appendChild(e)}function n(){const e=this.div.parentNode;e&&e.removeChild(this.div)}function o(){const t=this.option;this.Text=new e.Text({text:t.content,anchor:"bottom-center",offset:new e.Pixel(0,t.offsetY-16),style:{padding:(t.padding||8)+"px","line-height":(t.fontSize||14)+"px","border-radius":(t.borderRadius||0)+"px","border-color":`${t.bgColor||"#fff"} transparent transparent`,"background-color":t.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(t.fontSize||14)+"px",color:t.color||"#000"},position:t.position});(e.event||e.Event).addListener(this.Text,"click",(()=>{this.callback()})),this.Text.setMap(t.map)}function r(){}function i(){this.Text&&this.option.map.remove(this.Text)}function s(){this.Text&&this.option.map.remove(this.Text)}class a{constructor(e={},a){this.createAMapText=o,this.removeAMapText=i,this.createBMapText=r,this.removeBMapText=s,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=e||{};const l=this.visible=this.alwaysVisible="ALWAYS"===e.display;if(Jf())this.callback=a,this.visible&&this.createAMapText();else if(Zf())this.visible&&this.createBMapText();else{const t=e.map;this.position=e.position,this.index=1;const n=this.div=document.createElement("div"),o=n.style;o.position="absolute",o.whiteSpace="nowrap",o.transform="translateX(-50%) translateY(-100%)",o.zIndex="1",o.boxShadow=e.boxShadow||"none",o.display=l?"block":"none";const r=this.triangle=document.createElement("div");r.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(e),n.appendChild(r),t&&this.setMap(t)}}set onclick(e){this.div.onclick=e}get onclick(){return this.div.onclick}setOption(e){this.option=e,"ALWAYS"===e.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,Jf()?this.visible&&this.createAMapText():Zf()?this.visible&&this.createBMapText():(this.setPosition(e.position),this.setStyle(e))}setStyle(e){const t=this.div,n=t.style;t.innerText=e.content||"",n.lineHeight=(e.fontSize||14)+"px",n.fontSize=(e.fontSize||14)+"px",n.padding=(e.padding||8)+"px",n.color=e.color||"#000",n.borderRadius=(e.borderRadius||0)+"px",n.backgroundColor=e.bgColor||"#fff",n.marginTop="-"+((e.top||0)+5)+"px",this.triangle.style.borderColor=`${e.bgColor||"#fff"} transparent transparent`}setPosition(e){this.position=e,this.draw()}draw(){const e=this.getProjection();if(!this.position||!this.div||!e)return;const t=e.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=t.x+"px",n.top=t.y+"px"}changed(){this.div.style.display=this.visible?"block":"none"}}if(!Jf()&&!Zf()){const t=new(e.OverlayView||e.Overlay);a.prototype.setMap=t.setMap,a.prototype.getMap=t.getMap,a.prototype.getPanes=t.getPanes,a.prototype.getProjection=t.getProjection,a.prototype.map_changed=t.map_changed,a.prototype.set=t.set,a.prototype.get=t.get,a.prototype.setOptions=t.setValues,a.prototype.bindTo=t.bindTo,a.prototype.bindsTo=t.bindsTo,a.prototype.notify=t.notify,a.prototype.setValues=t.setValues,a.prototype.unbind=t.unbind,a.prototype.unbindAll=t.unbindAll,a.prototype.addListener=t.addListener}return a}const Wf={};function zf(e,t){const n=Xf();if(!n.key)return void console.error("Map key not configured.");const o=Wf[n.type]=Wf[n.type]||[];if(Df)t(Df);else if(window[n.type]&&window[n.type].maps)Df=Jf()||Zf()?window[n.type]:window[n.type].maps,Df.Callout=Df.Callout||qf(Df),t(Df);else if(o.length)o.push(t);else{o.push(t);const r=window,i="__map_callback__"+n.type;r[i]=function(){delete r[i],Df=Jf()||Zf()?window[n.type]:window[n.type].maps,Df.Callout=qf(Df),o.forEach((e=>e(Df))),o.length=0},Jf()&&function(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}(n);const s=document.createElement("script");let a=Uf(n.type);n.type===Yf.QQ&&e.push("geometry"),e.length&&(a+=`libraries=${e.join("%2C")}&`),n.type===Yf.BMAP?s.src=`${a}ak=${n.key}&callback=${i}`:s.src=`${a}key=${n.key}&callback=${i}`,s.onerror=function(){console.error("Map load failed.")},document.body.appendChild(s)}}const Uf=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"}[e]);var Yf=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(Yf||{});function Xf(){return __uniConfig.bMapKey?{type:"BMapGL",key:__uniConfig.bMapKey}:__uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:__uniConfig.googleMapKey?{type:"google",key:__uniConfig.googleMapKey}:__uniConfig.aMapKey?{type:"AMap",key:__uniConfig.aMapKey,securityJsCode:__uniConfig.aMapSecurityJsCode,serviceHost:__uniConfig.aMapServiceHost}:{type:"",key:""}}let Kf=!1,Gf=!1;const Jf=()=>Gf?Kf:(Gf=!0,Kf="AMap"===Xf().type),Zf=()=>"BMapGL"===Xf().type;const Qf=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let eh;function th(){if(eh=eh||Qf.__DC_STAT_UUID,!eh){eh=Date.now()+""+Math.floor(1e7*Math.random());try{Qf.__DC_STAT_UUID=eh}catch(e){}}return eh}function nh(){if(!0!==__uniConfig.darkmode)return v(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function oh(){let e,t="0",n="",o="phone";const r=navigator.language;if(Yd){e="iOS";const o=zd.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=zd.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Ud){e="Android";const o=zd.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=zd.match(/\((.+?)\)/),i=r?r[1].split(";"):zd.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Jd){if(n="iPad",e="iOS",o="pad",t=m(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=zd.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(Xd||Kd||Gd){n="PC",e="PC",o="pc",t="0";let r=zd.match(/\((.+?)\)/)[1];if(Xd){switch(e="Windows",Xd[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Kd){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Gd){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(zd)&&(a=t[n],l=zd.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLowerCase(),browserVersion:l,language:r,deviceType:o,ua:zd,osname:e,osversion:t,theme:nh()}}const rh=Mu(0,(()=>{const e=window.devicePixelRatio,t=Zd(),n=Qd(t),o=ep(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=tp(o);let s=window.innerHeight;const a=Dl.top,l={left:Dl.left,right:i-Dl.right,top:Dl.top,bottom:s-Dl.bottom,width:i-Dl.left-Dl.right,height:s-Dl.top-Dl.bottom},{top:c,bottom:u}=function(){const e=document.documentElement.style,t=Wl(),n=ql(e,"--window-bottom"),o=ql(e,"--window-left"),r=ql(e,"--window-right"),i=ql(e,"--top-window-height");return{top:t,bottom:n?n+Dl.bottom:0,left:o?o+Dl.left:0,right:r?r+Dl.right:0,topWindowHeight:i||0}}();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Dl.top,right:Dl.right,bottom:Dl.bottom,left:Dl.left},screenTop:r-s}}));let ih,sh=!0;function ah(){sh&&(ih=oh())}const lh=Mu(0,(()=>{ah();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:u}=ih;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:th(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLowerCase():void 0,osVersion:u})})),ch=Mu(0,(()=>{ah();const{theme:e,language:t,browserName:n,browserVersion:o}=ih;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Fu?Fu():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),uh=Mu(0,(()=>{sh=!0,ah(),sh=!1;const e=rh(),t=lh(),n=ch();sh=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=ih,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)}));const dh=Mu(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function ph(e){const t=localStorage&&localStorage.getItem(e);if(!v(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=v(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const fh=Mu(0,(e=>{try{return ph(e)}catch(t){return""}})),hh={esc:["Esc","Escape"],enter:["Enter"]},gh=Object.keys(hh);const mh=Qr("div",{class:"uni-mask"},null,-1);function vh(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),_s(go({setup:()=>()=>(Hr(),Ur(e,t,null,16))}))}function yh(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function _h(e,{onEsc:t,onEnter:n}){const o=tn(e.visible),{key:r,disable:i}=function(){const e=tn(""),t=tn(!1),n=n=>{if(t.value)return;const o=gh.find((e=>-1!==hh[e].indexOf(n.key)));o&&(e.value=o),wn((()=>e.value=""))};return Ro((()=>{document.addEventListener("keyup",n)})),jo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}();return Gn((()=>e.visible),(e=>o.value=e)),Gn((()=>o.value),(e=>i.value=!e)),Xn((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}const bh=$u("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,enableChunked:s,withCredentials:a,timeout:l=__uniConfig.networkTimeout.request},{resolve:c,reject:u})=>{let d=null;const f=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(v(t)||t instanceof ArrayBuffer)d=t;else if("json"===f)try{d=JSON.stringify(t)}catch(g){d=t.toString()}else if("urlencoded"===f){const e=[];for(const n in t)p(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));d=e.join("&")}else d=t.toString();let h;if(s){if(void 0===typeof window.fetch||void 0===typeof window.AbortController)throw new Error("fetch or AbortController is not supported in this environment");const t=new AbortController,s=t.signal;h=new xh(t);const p={method:o,headers:n,body:d,signal:s,credentials:a?"include":"same-origin"},f=setTimeout((function(){h.abort(),u("timeout",{errCode:5})}),l);p.signal.addEventListener("abort",(function(){clearTimeout(f),u("abort",{errCode:600003})})),window.fetch(e,p).then((e=>{const t=e.status,n=e.headers,o=e.body,s={};n.forEach(((e,t)=>{s[t]=e}));const a=wh(s);if(h._emitter.emit("headersReceived",{header:s,statusCode:t,cookies:a}),!o)return void c({data:"",statusCode:t,header:s,cookies:a});const l=o.getReader(),u=[],d=()=>{l.read().then((({done:e,value:n})=>{if(e){const e=function(e){const t=e.reduce(((e,t)=>e+t.byteLength),0),n=new Uint8Array(t);let o=0;for(const r of e)n.set(new Uint8Array(r),o),o+=r.byteLength;return n.buffer}(u);let n="text"===i?(new TextDecoder).decode(e):e;return"text"===i&&(n=Th(n,i,r)),void c({data:n,statusCode:t,header:s,cookies:a})}const o=n;u.push(o),h._emitter.emit("chunkReceived",{data:o}),d()}))};d()}),(e=>{u(e,{errCode:5})}))}else{const t=new XMLHttpRequest;h=new xh(t),t.open(o,e);for(const e in n)p(n,e)&&t.setRequestHeader(e,n[e]);const s=setTimeout((function(){t.onload=t.onabort=t.onerror=null,h.abort(),u("timeout",{errCode:5})}),l);t.responseType=i,t.onload=function(){clearTimeout(s);const e=t.status;let n="text"===i?t.responseText:t.response;"text"===i&&(n=Th(n,i,r)),c({data:n,statusCode:e,header:Sh(t.getAllResponseHeaders()),cookies:[]})},t.onabort=function(){clearTimeout(s),u("abort",{errCode:600003})},t.onerror=function(){clearTimeout(s),u(void 0,{errCode:5})},t.withCredentials=a,t.send(d)}return h}),0,Ku),wh=e=>{let t=e["Set-Cookie"]||e["set-cookie"],n=[];if(!t)return[];"["===t[0]&&"]"===t[t.length-1]&&(t=t.slice(1,-1));const o=t.split(";");for(let r=0;r<o.length;r++)-1!==o[r].indexOf("Expires=")||-1!==o[r].indexOf("expires=")?n.push(o[r].replace(",","")):n.push(o[r]);return n=n.join(";").split(","),n};class xh{constructor(e){this._requestOnChunkReceiveCallbackId=0,this._requestOnChunkReceiveCallbacks=new Map,this._requestOnHeadersReceiveCallbackId=0,this._requestOnHeadersReceiveCallbacks=new Map,this._emitter=new Oe,this._controller=e}abort(){this._controller&&(this._controller.abort(),delete this._controller)}onHeadersReceived(e){return this._emitter.on("headersReceived",e),this._requestOnHeadersReceiveCallbackId++,this._requestOnHeadersReceiveCallbacks.set(this._requestOnHeadersReceiveCallbackId,e),this._requestOnHeadersReceiveCallbackId}offHeadersReceived(e){if(null==e)return void this._emitter.off("headersReceived");if("function"==typeof e)return void this._requestOnHeadersReceiveCallbacks.forEach(((t,n)=>{t===e&&(this._requestOnHeadersReceiveCallbacks.delete(n),this._emitter.off("headersReceived",e))}));const t=this._requestOnHeadersReceiveCallbacks.get(e);t&&(this._requestOnHeadersReceiveCallbacks.delete(e),this._emitter.off("headersReceived",t))}onChunkReceived(e){return this._emitter.on("chunkReceived",e),this._requestOnChunkReceiveCallbackId++,this._requestOnChunkReceiveCallbacks.set(this._requestOnChunkReceiveCallbackId,e),this._requestOnChunkReceiveCallbackId}offChunkReceived(e){if(null==e)return void this._emitter.off("chunkReceived");if("function"==typeof e)return void this._requestOnChunkReceiveCallbacks.forEach(((t,n)=>{t===e&&(this._requestOnChunkReceiveCallbacks.delete(n),this._emitter.off("chunkReceived",e))}));const t=this._requestOnChunkReceiveCallbacks.get(e);t&&(this._requestOnChunkReceiveCallbacks.delete(e),this._emitter.off("chunkReceived",t))}}function Sh(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}function Th(e,t,n){let o=e;if("text"===t&&"json"===n)try{o=JSON.parse(o)}catch(r){}return o}const Ch=Au("getLocation",(({type:e,altitude:t,highAccuracyExpireTime:n,isHighAccuracy:o},{resolve:r,reject:i})=>{const s=Xf();new Promise(((e,r)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition((t=>e({coords:t.coords})),r,{enableHighAccuracy:o||t,timeout:n||1e5}):r(new Error("device nonsupport geolocation"))})).catch((e=>new Promise(((t,n)=>{s.type===Yf.QQ?Ff(`https://apis.map.qq.com/ws/location/v1/ip?output=jsonp&key=${s.key}`,{callback:"callback"},(e=>{if("result"in e&&e.result.location){const n=e.result.location;t({coords:{latitude:n.lat,longitude:n.lng},skip:!0})}else n(new Error(e.message||JSON.stringify(e)))}),(()=>n(new Error("network error")))):s.type===Yf.GOOGLE?bh({method:"POST",url:`https://www.googleapis.com/geolocation/v1/geolocate?key=${s.key}`,success(e){const o=e.data;"location"in o?t({coords:{latitude:o.location.lat,longitude:o.location.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.error&&o.error.message||JSON.stringify(e)))},fail(){n(new Error("network error"))}}):s.type===Yf.AMAP?zf([],(()=>{window.AMap.plugin("AMap.Geolocation",(()=>{new window.AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4}).getCurrentPosition(((e,o)=>{"complete"===e?t({coords:{latitude:o.position.lat,longitude:o.position.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.message))}))}))})):n(e)})))).then((({coords:t,skip:n})=>{(function(e,t,n){const o=Xf();return e&&"WGS84"===e.toUpperCase()||["google"].includes(o.type)||n?Promise.resolve(t):"qq"===o.type?new Promise((e=>{Ff(`https://apis.map.qq.com/ws/coord/v1/translate?type=1&locations=${t.latitude},${t.longitude}&key=${o.key}&output=jsonp`,{callback:"callback"},(n=>{if("locations"in n&&n.locations.length){const{lng:o,lat:r}=n.locations[0];e({longitude:o,latitude:r,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}),(()=>e(t)))})):"AMap"===o.type?new Promise((e=>{zf([],(()=>{window.AMap.convertFrom([t.longitude,t.latitude],"gps",((n,o)=>{if("ok"===o.info&&o.locations.length){const{lat:n,lng:r}=o.locations[0];e({longitude:r,latitude:n,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}))}))})):Promise.reject(new Error("translate coordinate system faild, map provider not configured or not supported"))})(e,t,n).then((e=>{r({latitude:e.latitude,longitude:e.longitude,accuracy:e.accuracy,speed:e.altitude||0,altitude:e.altitude||0,verticalAccuracy:e.altitudeAccuracy||0,horizontalAccuracy:e.accuracy||0})})).catch((e=>{i(e.message)}))})).catch((e=>{i(e.message||JSON.stringify(e))}))}),0,zu),kh=Au("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===lc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(Mf().$router.go(-e.delta),t()):n("onBackPress")}),0,Zu),Eh=Au("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(wd.handledBeforeEntryPageRoutes)return pd({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);xd.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Ju);function Oh(e){__uniConfig.darkmode&&rg.on("onThemeChange",e)}function Lh(e){rg.off("onThemeChange",e)}function $h(e){let t={};return __uniConfig.darkmode&&(t=Me(e,__uniConfig.themeConfig,nh())),__uniConfig.darkmode?t:e}const Mh={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},Ah=go({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=tn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=_h(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=tn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=Mh[e].cancelColor})(e,t)};return Xn((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===nh()&&n({theme:"dark"}),Oh(n))):Lh(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:p}=e;return n.value=o,Qr($i,{name:"uni-fade"},{default:()=>[to(Qr("uni-modal",{onTouchmove:Hl},[mh,Qr("div",{class:"uni-modal"},[t?Qr("div",{class:"uni-modal__hd"},[Qr("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?Qr("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:p,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Qr("div",{class:"uni-modal__bd",onTouchmovePassive:Fl,textContent:o},null,40,["onTouchmovePassive","textContent"]),Qr("div",{class:"uni-modal__ft"},[l&&Qr("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Qr("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Wi,s.value]])]})}}});let Ph;const Bh=re((()=>{rg.on("onHidePopup",(()=>Ph.visible=!1))}));let Rh;function Ih(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&Ph.editable&&(o.content=t),Rh&&Rh(o)}const Nh=Au("showModal",((e,{resolve:t})=>{Bh(),Rh=t,Ph?(c(Ph,e),Ph.visible=!0):(Ph=Vt(e),wn((()=>(vh(Ah,Ph,Ih).mount(yh("u-a-m")),wn((()=>Ph.visible=!0))))))}),0,sd),jh={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==ad.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Vh={light:"#fff",dark:"rgba(255,255,255,0.9)"},Dh=e=>Vh[e],Hh=go({name:"Toast",props:jh,setup(e){pl(),fl();const{Icon:t}=function(e){const t=tn(Dh(nh())),n=({theme:e})=>t.value=Dh(e);Xn((()=>{e.visible?Oh(n):Lh(n)}));return{Icon:xi((()=>{switch(e.icon){case"success":return Qr(ec(Jl,t.value,38),{class:"uni-toast__icon"});case"error":return Qr(ec(Zl,t.value,38),{class:"uni-toast__icon"});case"loading":return Qr("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=_h(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return Qr($i,{name:"uni-fade"},{default:()=>[to(Qr("uni-toast",{"data-duration":r},[o?Qr("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Hl},null,40,["onTouchmove"]):"",s||t.value?Qr("div",{class:"uni-toast"},[s?Qr("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,Qr("p",{class:"uni-toast__content"},[i])]):Qr("div",{class:"uni-sample-toast"},[Qr("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Wi,n.value]])]})}}});let Fh,qh,Wh="";const zh=Re();function Uh(e){Fh?c(Fh,e):(Fh=Vt(c(e,{visible:!1})),wn((()=>{zh.run((()=>{Gn([()=>Fh.visible,()=>Fh.duration],(([e,t])=>{if(e){if(qh&&clearTimeout(qh),"onShowLoading"===Wh)return;qh=setTimeout((()=>{Jh("onHideToast")}),t)}else qh&&clearTimeout(qh)}))})),rg.on("onHidePopup",(()=>Jh("onHidePopup"))),vh(Hh,Fh,(()=>{})).mount(yh("u-a-t"))}))),setTimeout((()=>{Fh.visible=!0}),10)}const Yh=Au("showToast",((e,{resolve:t,reject:n})=>{Uh(e),Wh="onShowToast",t()}),0,ld),Xh={icon:"loading",duration:1e8,image:""},Kh=Au("showLoading",((e,{resolve:t,reject:n})=>{c(e,Xh),Uh(e),Wh="onShowLoading",t()}),0,id),Gh=Au("hideLoading",((e,{resolve:t,reject:n})=>{Jh("onHideLoading"),t()}));function Jh(e){const{t:t}=cl();if(!Wh)return;let n="";if("onHideToast"===e&&"onShowToast"!==Wh?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Wh&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Wh="",setTimeout((()=>{Fh.visible=!1}),10)}function Zh(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,rg.emit("onNavigationBarChange",{titleText:t})}Xn(t),To(t)}function Qh(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:a}=n;i.titleText=a}o()}const eg=Au("setNavigationBarColor",((e,{resolve:t,reject:n})=>{Qh(oc(),"setNavigationBarColor",e,t,n)}),0,rd),tg=Au("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{Qh(oc(),"setNavigationBarTitle",e,t,n)})),ng=Fc({name:"Layout",setup(e,{emit:t}){const n=tn(null);zl({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=Ja();return{routeKey:xi((()=>Pd("/"+e.meta.route,ru()))),isTabBar:xi((()=>e.meta.isTabBar)),routeCache:Rd}}(),{layoutState:r,windowState:i}=function(){ou();{const e=Vt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Gn((()=>e.marginWidth),(e=>zl({"--window-margin":e+"px"}))),Gn((()=>e.leftWindowWidth+e.marginWidth),(e=>{zl({"--window-left":e+"px"})})),Gn((()=>e.rightWindowWidth+e.marginWidth),(e=>{zl({"--window-right":e+"px"})})),{layoutState:e,windowState:xi((()=>({})))}}}();!function(e,t){const n=ou();function o(){const o=document.body.clientWidth,r=Od();let i={};if(r.length>0){i=bd(r[r.length-1]).meta}else{const e=hc(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((p(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,wn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,wn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Gn([()=>n.path],o),Ro((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=tn(!1);return xi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(!1);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return Qr(Ka,null,{default:In((({Component:o})=>[(Hr(),Ur(xo,{matchBy:"key",cache:n},[(Hr(),Ur(qn(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o);return Qr("uni-app",{ref:n,class:s.value},[e,!1],2)}}});const og=c(bl,{publishHandler(e,t,n){rg.subscribeHandler(e,t,n)}}),rg=c(Oc,{publishHandler(e,t,n){og.subscribeHandler(e,t,n)}}),ig=Fc({name:"PageHead",setup(){const e=tn(null),t=tu(),n=function(e,t){const n=qt(e),o=n?Vt($h(e)):$h(e);return __uniConfig.darkmode&&n&&Gn(e,(e=>{const t=$h(e);for(const n in t)o[n]=t[n]})),t&&Oh(t),o}(t.navigationBar,(()=>{const e=$h(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=xi((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=xi((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const i=function(e,t){if(!t)return Qr("div",{class:"uni-page-head-btn",onClick:ag},[ec(Ql,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),s=n.type||"default",a="transparent"!==s&&"float"!==s&&Qr("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return Qr("uni-page-head",{"uni-page-head-type":s},[Qr("div",{ref:e,class:o.value,style:r.value},[Qr("div",{class:"uni-page-head-hd"},[i]),sg(n),Qr("div",{class:"uni-page-head-ft"},[])],6),a],8,["uni-page-head-type"])}}});function sg(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return Qr("div",{class:"uni-page-head-bd"},[Qr("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?Qr("i",{class:"uni-loading"},null):r?Qr("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function ag(){1===Ed().length?dd({url:"/"}):kh({from:"backbutton",success(){}})}const lg=Fc({name:"PageBody",setup(e,t){const n=tn(null),o=tn(null);return Gn((()=>false.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>Qr(Rr,null,[!1,Qr("uni-page-wrapper",si({ref:n},o.value),[Qr("uni-page-body",null,[zo(t.slots,"default")]),null],16)])}}),cg=Fc({name:"Page",setup(e,t){let n=nu(ru());const o=n.navigationBar,r={};return Zh(n),()=>Qr("uni-page",{"data-page":n.route,style:r},"custom"!==o.style?[Qr(ig),ug(t),null]:[ug(t),null])}});function ug(e){return Hr(),Ur(lg,{key:0},{default:In((()=>[zo(e.slots,"page")])),_:3})}const dg={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=Hu;const pg=Object.assign({}),fg=Object.assign;window.__uniConfig=fg({globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#F8F8F8",titleText:"uni-app",type:"default",titleColor:"#000000"},isNVue:!1},uniIdRouter:{},compilerVersion:"4.75"},{appId:"__UNI__77EE06C",appName:"uniapp-ts",appVersion:"1.0.0",appVersionCode:"100",async:dg,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(pg).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return fg(e[n]||(e[n]={}),pg[t].default),e}),{}),router:{mode:"hash",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const hg={delay:dg.delay,timeout:dg.timeout,suspensible:dg.suspensible};dg.loading&&(hg.loadingComponent={name:"SystemAsyncLoading",render:()=>Qr(Hn(dg.loading))}),dg.error&&(hg.errorComponent={name:"SystemAsyncError",props:["error"],render(){return Qr(Hn(dg.error),{error:this.error})}});const gg=()=>t((()=>import("./pages-index-index.CWcnUenF.js")),__vite__mapDeps([0,1,2])).then((e=>Rf(e.default||e))),mg=vo(fg({loader:gg},hg)),vg=()=>t((()=>import("./pages-ts-learn-index.TXpaEBNF.js")),__vite__mapDeps([3,1,4])).then((e=>Rf(e.default||e))),yg=vo(fg({loader:vg},hg)),_g=()=>t((()=>import("./pages-ts-learn-basic-types.tDZvBkRi.js")),__vite__mapDeps([5,1,6])).then((e=>Rf(e.default||e))),bg=vo(fg({loader:_g},hg)),wg=()=>t((()=>import("./pages-ts-learn-interfaces.B1Gz-s19.js")),__vite__mapDeps([7,1,8])).then((e=>Rf(e.default||e))),xg=vo(fg({loader:wg},hg)),Sg=()=>t((()=>import("./pages-ts-learn-type-aliases.neeDtytv.js")),__vite__mapDeps([9,1,10])).then((e=>Rf(e.default||e))),Tg=vo(fg({loader:Sg},hg)),Cg=()=>t((()=>import("./pages-ts-learn-functions.CkJ1mnVc.js")),__vite__mapDeps([11,1,12])).then((e=>Rf(e.default||e))),kg=vo(fg({loader:Cg},hg)),Eg=()=>t((()=>import("./pages-ts-learn-classes.6kEubYPL.js")),__vite__mapDeps([13,1,14])).then((e=>Rf(e.default||e))),Og=vo(fg({loader:Eg},hg)),Lg=()=>t((()=>import("./pages-ts-learn-modules.CPGBF_Gk.js")),__vite__mapDeps([15,1,16])).then((e=>Rf(e.default||e))),$g=vo(fg({loader:Lg},hg)),Mg=()=>t((()=>import("./pages-ts-learn-generics.GDySUqhN.js")),__vite__mapDeps([17,1,18])).then((e=>Rf(e.default||e))),Ag=vo(fg({loader:Mg},hg)),Pg=()=>t((()=>import("./pages-ts-learn-advanced-types.Cq59EIzT.js")),__vite__mapDeps([19,1,20])).then((e=>Rf(e.default||e))),Bg=vo(fg({loader:Pg},hg)),Rg=()=>t((()=>import("./pages-typescript-demo-typescript-demo.B8AdqGkL.js")),__vite__mapDeps([21,1,22])).then((e=>Rf(e.default||e))),Ig=vo(fg({loader:Rg},hg)),Ng=()=>t((()=>import("./pages-skeleton-demo-skeleton-demo.sFaOXvF9.js")),__vite__mapDeps([23,24,1,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42])).then((e=>Rf(e.default||e))),jg=vo(fg({loader:Ng},hg)),Vg=()=>t((()=>import("./pages-skeleton-simple-skeleton-simple.D9VVP8gy.js")),__vite__mapDeps([43,24,1,25,26,27,44])).then((e=>Rf(e.default||e))),Dg=vo(fg({loader:Vg},hg)),Hg=()=>t((()=>import("./pages-icon-demo-icon-demo.p2djDtHl.js")),__vite__mapDeps([45,26,1,27,46,39,47])).then((e=>Rf(e.default||e))),Fg=vo(fg({loader:Hg},hg)),qg=()=>t((()=>import("./pages-numberbox-demo-numberbox-demo.VF4TlLx8.js")),__vite__mapDeps([48,49,26,1,27,28,29,50])).then((e=>Rf(e.default||e))),Wg=vo(fg({loader:qg},hg)),zg=()=>t((()=>import("./pages-modal-demo-modal-demo.COyPpbkd.js")),__vite__mapDeps([51,26,1,27,30,31,52])).then((e=>Rf(e.default||e))),Ug=vo(fg({loader:zg},hg)),Yg=()=>t((()=>import("./pages-tabs-demo-tabs-demo.CIS-o_CS.js")),__vite__mapDeps([53,26,1,27,32,33,54])).then((e=>Rf(e.default||e))),Xg=vo(fg({loader:Yg},hg)),Kg=()=>t((()=>import("./pages-countdown-demo-countdown-demo.wz5ch5Vu.js")),__vite__mapDeps([55,49,26,1,27,34,35,56])).then((e=>Rf(e.default||e))),Gg=vo(fg({loader:Kg},hg)),Jg=()=>t((()=>import("./pages-bottom-popup-demo-bottom-popup-demo.CI2rcCcV.js")),__vite__mapDeps([57,49,26,1,27,36,37,58,59])).then((e=>Rf(e.default||e))),Zg=vo(fg({loader:Jg},hg)),Qg=()=>t((()=>import("./pages-cascade-selection-demo-cascade-selection-demo.CDmwY9tF.js")),__vite__mapDeps([60,26,1,27,36,37,58,61,46,39,40,62])).then((e=>Rf(e.default||e))),em=vo(fg({loader:Qg},hg)),tm=()=>t((()=>import("./pages-picker-address-demo-picker-address-demo.QQpE74kd.js")),__vite__mapDeps([63,26,1,27,36,37,61,46,39,40,58,64,41])).then((e=>Rf(e.default||e))),nm=vo(fg({loader:tm},hg)),om=()=>t((()=>import("./pages-verification-code-demo-verification-code-demo.aoapn5lJ.js")),__vite__mapDeps([65,26,1,27,66,42])).then((e=>Rf(e.default||e))),rm=vo(fg({loader:om},hg));function im(e,t){return Hr(),Ur(cg,null,{page:In((()=>[Qr(e,fg({},t,{ref:"page"}),null,512)])),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(mg,t)}},loader:gg,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"uni-app",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/index",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(yg,t)}},loader:vg,meta:{navigationBar:{titleText:"TypeScript 学习",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/basic-types",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(bg,t)}},loader:_g,meta:{navigationBar:{titleText:"TypeScript 基础类型",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/interfaces",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(xg,t)}},loader:wg,meta:{navigationBar:{titleText:"TypeScript 接口 (Interface)",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/type-aliases",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Tg,t)}},loader:Sg,meta:{navigationBar:{titleText:"TypeScript 类型别名 (Type)",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/functions",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(kg,t)}},loader:Cg,meta:{navigationBar:{titleText:"TypeScript 函数类型",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/classes",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Og,t)}},loader:Eg,meta:{navigationBar:{titleText:"TypeScript 类和继承",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/modules",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im($g,t)}},loader:Lg,meta:{navigationBar:{titleText:"TypeScript 模块和命名空间",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/generics",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Ag,t)}},loader:Mg,meta:{navigationBar:{titleText:"TypeScript 泛型 (Generics)",type:"default"},isNVue:!1}},{path:"/pages/ts-learn/advanced-types",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Bg,t)}},loader:Pg,meta:{navigationBar:{titleText:"TypeScript 高级类型",type:"default"},isNVue:!1}},{path:"/pages/typescript-demo/typescript-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Ig,t)}},loader:Rg,meta:{navigationBar:{titleText:"TypeScript 示例",type:"default"},isNVue:!1}},{path:"/pages/skeleton-demo/skeleton-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(jg,t)}},loader:Ng,meta:{navigationBar:{titleText:"骨架屏演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/skeleton-simple/skeleton-simple",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Dg,t)}},loader:Vg,meta:{navigationBar:{titleText:"骨架屏简单演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/icon-demo/icon-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Fg,t)}},loader:Hg,meta:{navigationBar:{titleText:"图标组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/numberbox-demo/numberbox-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Wg,t)}},loader:qg,meta:{navigationBar:{titleText:"数字框组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/modal-demo/modal-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Ug,t)}},loader:zg,meta:{navigationBar:{titleText:"弹窗组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/tabs-demo/tabs-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Xg,t)}},loader:Yg,meta:{navigationBar:{titleText:"标签页组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/countdown-demo/countdown-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Gg,t)}},loader:Kg,meta:{navigationBar:{titleText:"倒计时组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/bottom-popup-demo/bottom-popup-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(Zg,t)}},loader:Jg,meta:{navigationBar:{titleText:"底部弹窗组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/cascade-selection-demo/cascade-selection-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(em,t)}},loader:Qg,meta:{navigationBar:{titleText:"级联选择器组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/picker-address-demo/picker-address-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(nm,t)}},loader:tm,meta:{navigationBar:{titleText:"城市选择器组件演示",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/verification-code-demo/verification-code-demo",component:{setup(){const e=Mf(),t=e&&e.$route&&e.$route.query||{};return()=>im(rm,t)}},loader:om,meta:{navigationBar:{titleText:"验证码按钮组件演示",style:"custom",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const sm={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};Bf(sm,{init:Af,setup(e){const t=ou(),n=()=>{var n;n=e,Object.keys(qu).forEach((e=>{qu[e].forEach((t=>{Ao(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return c(np,{path:e,query:t}),c(op,np),c({},np)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:ye(t.query)});if(o&&B(o,s),r&&B(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};fd(),i&&B(i,e)}};return gr(ja).isReady().then(n),Ro((()=>{window.addEventListener("resize",we(If,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Nf),document.addEventListener("visibilitychange",jf),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{rg.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Hr(),Ur(ng));e.setup=(e,o)=>{const r=t&&t(e,o);return m(r)?n:r},e.render=n}}),_s(sm).use(Sf).mount("#app");export{dd as $,Ro as A,rn as B,tf as C,xi as D,zo as E,Rr as F,Kp as G,Gn as H,Ap as I,ms as J,_s as K,Si as L,wn as M,to as N,Wi as O,Vo as P,zp as Q,jp as R,Xp as S,cf as T,ef as U,Zp as V,si as W,uh as X,Hn as Y,Ed as Z,t as _,Qr as a,eg as a0,tg as a1,ti as b,Ur as c,dp as d,rf as e,Qc as f,dh as g,fh as h,uf as i,ce as j,zr as k,ue as l,kh as m,Eh as n,Hr as o,Kh as p,Gh as q,Wo as r,Yh as s,Y as t,Ch as u,Nh as v,In as w,ni as x,go as y,tn as z};
