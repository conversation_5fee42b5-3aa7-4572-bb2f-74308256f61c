/**
 * 全局组件注册文件
 */

import type { App } from 'vue'

// 导入自定义导航栏组件
import { CustomNavBar, NavBarPro } from './CustomNavBar'

// 导入骨架屏组件
import { Skeleton, SkeletonItem } from './Skeleton'

// 导入图标组件
import { Icon, IconFont } from './Icon'

// 导入数字框组件
import { NumberBox } from './NumberBox'

// 导入弹窗组件
import { Modal } from './Modal'

// 导入标签页组件
import { Tabs, TabPane } from './Tabs'

// 导入倒计时组件
import { Countdown } from './Countdown'

// 导入底部弹窗组件
import { BottomPopup } from './BottomPopup'

// 导入级联选择器组件
import { CascadeSelection } from './CascadeSelection'

// 导入城市地址选择器组件
import { PickerAddress } from './PickerAddress'

// 导入验证码按钮组件
import { VerificationCodeButton } from './VerificationCodeButton'

// 组件列表
const components = {
  CustomNavBar,
  NavBarPro,
  Skeleton,
  SkeletonItem,
  Icon,
  IconFont,
  NumberBox,
  Modal,
  Tabs,
  TabPane,
  Countdown,
  BottomPopup,
  CascadeSelection,
  PickerAddress,
  VerificationCodeButton
}

// 全局注册组件的函数
export function registerGlobalComponents(app: App) {
  Object.keys(components).forEach(key => {
    app.component(key, components[key as keyof typeof components])
  })
}

// 导出组件
export * from './CustomNavBar'
export * from './Skeleton'
export * from './Icon'
export * from './NumberBox'
export * from './Modal'
export * from './Tabs'
export * from './Countdown'
export * from './BottomPopup'
export * from './CascadeSelection'
export * from './PickerAddress'
export * from './VerificationCodeButton'

// 默认导出
export default {
  install: registerGlobalComponents
}
