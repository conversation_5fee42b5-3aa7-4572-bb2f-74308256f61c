<template>
  <view class="page" :style="{ paddingTop: navBarHeight + 'px' }">
    <!-- 自定义导航栏 -->
    <CustomNavBar 
      title="验证码按钮演示" 
      :show-back="true"
      background-color="#007aff"
      color="#ffffff"
    />
    
    <scroll-view scroll-y class="content" :style="{ height: `calc(100vh - ${navBarHeight}px)` }">
      <!-- 基础用法 -->
      <view class="demo-section">
        <text class="section-title">📱 基础用法</text>
        
        <view class="demo-card">
          <text class="demo-label">默认样式:</text>
          <view class="demo-row">
            <input 
              v-model="phone1" 
              placeholder="请输入手机号" 
              class="demo-input"
            />
            <VerificationCodeButton
              ref="basicButtonRef"
              @click="handleBasicClick"
            />
          </view>
          <text v-if="basicResult" class="demo-result">{{ basicResult }}</text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">自动开始倒计时:</text>
          <view class="demo-row">
            <input 
              v-model="phone2" 
              placeholder="请输入手机号" 
              class="demo-input"
            />
            <VerificationCodeButton
              :auto-start="true"
              @click="handleAutoStartClick"
            />
          </view>
          <text v-if="autoStartResult" class="demo-result">{{ autoStartResult }}</text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">自定义倒计时:</text>
          <view class="demo-row">
            <input 
              v-model="phone3" 
              placeholder="请输入手机号" 
              class="demo-input"
            />
            <VerificationCodeButton
              :countdown="30"
              countdown-format="重新发送({time}s)"
              @click="handleCustomCountdownClick"
            />
          </view>
          <text v-if="customCountdownResult" class="demo-result">{{ customCountdownResult }}</text>
        </view>
      </view>
      
      <!-- 样式主题 -->
      <view class="demo-section">
        <text class="section-title">🎨 样式主题</text>
        
        <view class="demo-card">
          <text class="demo-label">不同主题:</text>
          <view class="theme-grid">
            <VerificationCodeButton
              v-bind="defaultTheme"
              text="默认"
              @click="handleThemeClick('default')"
            />
            <VerificationCodeButton
              v-bind="successTheme"
              text="成功"
              @click="handleThemeClick('success')"
            />
            <VerificationCodeButton
              v-bind="warningTheme"
              text="警告"
              @click="handleThemeClick('warning')"
            />
            <VerificationCodeButton
              v-bind="dangerTheme"
              text="危险"
              @click="handleThemeClick('danger')"
            />
            <VerificationCodeButton
              v-bind="plainTheme"
              text="朴素"
              @click="handleThemeClick('plain')"
            />
            <VerificationCodeButton
              v-bind="darkTheme"
              text="暗色"
              @click="handleThemeClick('dark')"
            />
          </view>
          <text v-if="themeResult" class="demo-result">{{ themeResult }}</text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">不同尺寸:</text>
          <view class="size-list">
            <VerificationCodeButton
              v-bind="smallSize"
              text="小尺寸"
              @click="handleSizeClick('small')"
            />
            <VerificationCodeButton
              v-bind="defaultSize"
              text="默认尺寸"
              @click="handleSizeClick('default')"
            />
            <VerificationCodeButton
              v-bind="largeSize"
              text="大尺寸"
              @click="handleSizeClick('large')"
            />
            <VerificationCodeButton
              v-bind="xlargeSize"
              text="超大尺寸"
              @click="handleSizeClick('xlarge')"
            />
          </view>
          <text v-if="sizeResult" class="demo-result">{{ sizeResult }}</text>
        </view>
      </view>
      
      <!-- 异步操作 -->
      <view class="demo-section">
        <text class="section-title">⚡ 异步操作</text>
        
        <view class="demo-card">
          <text class="demo-label">模拟API调用:</text>
          <view class="demo-row">
            <input 
              v-model="phone4" 
              placeholder="请输入手机号" 
              class="demo-input"
            />
            <VerificationCodeButton
              ref="asyncButtonRef"
              :loading="isLoading"
              loading-text="发送中..."
              @click="handleAsyncClick"
            />
          </view>
          <text v-if="asyncResult" class="demo-result">{{ asyncResult }}</text>
        </view>
        
        <view class="demo-card">
          <text class="demo-label">错误处理:</text>
          <view class="demo-row">
            <input 
              v-model="phone5" 
              placeholder="请输入手机号" 
              class="demo-input"
            />
            <VerificationCodeButton
              ref="errorButtonRef"
              @click="handleErrorClick"
            />
          </view>
          <text v-if="errorResult" class="demo-result">{{ errorResult }}</text>
        </view>
      </view>
      
      <!-- 控制方法 -->
      <view class="demo-section">
        <text class="section-title">🎮 控制方法</text>
        
        <view class="demo-card">
          <text class="demo-label">手动控制:</text>
          <view class="control-area">
            <VerificationCodeButton
              ref="controlButtonRef"
              text="手动控制"
              @click="handleControlClick"
            />
            <view class="control-buttons">
              <button @click="startCountdown" class="control-btn">开始倒计时</button>
              <button @click="stopCountdown" class="control-btn">停止倒计时</button>
              <button @click="setLoading" class="control-btn">设置加载</button>
              <button @click="getState" class="control-btn">获取状态</button>
            </view>
          </view>
          <text v-if="controlResult" class="demo-result">{{ controlResult }}</text>
        </view>
      </view>
      
      <!-- 事件监听 -->
      <view class="demo-section">
        <text class="section-title">📡 事件监听</text>
        
        <view class="demo-card">
          <text class="demo-label">事件日志:</text>
          <view class="demo-row">
            <input 
              v-model="phone6" 
              placeholder="请输入手机号" 
              class="demo-input"
            />
            <VerificationCodeButton
              :auto-start="true"
              @click="handleEventClick"
              @countdown-start="handleCountdownStart"
              @countdown-tick="handleCountdownTick"
              @countdown-end="handleCountdownEnd"
            />
          </view>
          <scroll-view v-if="eventLogs.length > 0" scroll-y class="event-logs">
            <view v-for="(log, index) in eventLogs" :key="index" class="event-log">
              <text class="event-time">{{ log.time }}</text>
              <text class="event-name">{{ log.event }}</text>
              <text class="event-data">{{ log.data }}</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CustomNavBar, navBarUtils } from '@/components/CustomNavBar'
import { 
  VerificationCodeButton, 
  verificationCodeButtonUtils,
  type VerificationCodeButtonMethods 
} from '@/components/VerificationCodeButton'

// 响应式数据
const navBarHeight = ref(0)

// 表单数据
const phone1 = ref('')
const phone2 = ref('')
const phone3 = ref('')
const phone4 = ref('')
const phone5 = ref('')
const phone6 = ref('')

// 结果显示
const basicResult = ref('')
const autoStartResult = ref('')
const customCountdownResult = ref('')
const themeResult = ref('')
const sizeResult = ref('')
const asyncResult = ref('')
const errorResult = ref('')
const controlResult = ref('')

// 状态
const isLoading = ref(false)

// 组件引用
const basicButtonRef = ref<VerificationCodeButtonMethods>()
const asyncButtonRef = ref<VerificationCodeButtonMethods>()
const errorButtonRef = ref<VerificationCodeButtonMethods>()
const controlButtonRef = ref<VerificationCodeButtonMethods>()

// 事件日志
const eventLogs = ref<Array<{ time: string, event: string, data: string }>>([])

// 主题配置
const defaultTheme = verificationCodeButtonUtils.createPreset('default', 'small')
const successTheme = verificationCodeButtonUtils.createPreset('success', 'small')
const warningTheme = verificationCodeButtonUtils.createPreset('warning', 'small')
const dangerTheme = verificationCodeButtonUtils.createPreset('danger', 'small')
const plainTheme = verificationCodeButtonUtils.createPreset('plain', 'small')
const darkTheme = verificationCodeButtonUtils.createPreset('dark', 'small')

// 尺寸配置
const smallSize = verificationCodeButtonUtils.createPreset('default', 'small')
const defaultSize = verificationCodeButtonUtils.createPreset('default', 'default')
const largeSize = verificationCodeButtonUtils.createPreset('default', 'large')
const xlargeSize = verificationCodeButtonUtils.createPreset('default', 'xlarge')

// 生命周期
onMounted(() => {
  navBarHeight.value = navBarUtils.getTotalHeight()
})

// 基础用法方法
const handleBasicClick = () => {
  if (!verificationCodeButtonUtils.validatePhone(phone1.value)) {
    basicResult.value = '请输入正确的手机号'
    return
  }
  
  basicResult.value = `向 ${phone1.value} 发送验证码`
  basicButtonRef.value?.startCountdown()
}

const handleAutoStartClick = () => {
  if (!verificationCodeButtonUtils.validatePhone(phone2.value)) {
    autoStartResult.value = '请输入正确的手机号'
    return
  }
  
  autoStartResult.value = `向 ${phone2.value} 发送验证码（自动开始倒计时）`
}

const handleCustomCountdownClick = () => {
  if (!verificationCodeButtonUtils.validatePhone(phone3.value)) {
    customCountdownResult.value = '请输入正确的手机号'
    return
  }
  
  customCountdownResult.value = `向 ${phone3.value} 发送验证码（30秒倒计时）`
}

// 主题方法
const handleThemeClick = (theme: string) => {
  themeResult.value = `点击了 ${theme} 主题按钮`
}

const handleSizeClick = (size: string) => {
  sizeResult.value = `点击了 ${size} 尺寸按钮`
}

// 异步操作方法
const handleAsyncClick = async () => {
  if (!verificationCodeButtonUtils.validatePhone(phone4.value)) {
    asyncResult.value = '请输入正确的手机号'
    return
  }
  
  isLoading.value = true
  asyncResult.value = '正在发送验证码...'
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    asyncResult.value = `验证码已发送到 ${phone4.value}`
    asyncButtonRef.value?.startCountdown()
  } catch (error) {
    asyncResult.value = '发送失败，请重试'
  } finally {
    isLoading.value = false
  }
}

const handleErrorClick = async () => {
  if (!verificationCodeButtonUtils.validatePhone(phone5.value)) {
    errorResult.value = '请输入正确的手机号'
    return
  }
  
  errorButtonRef.value?.setLoading(true)
  errorResult.value = '正在发送验证码...'
  
  try {
    // 模拟API调用失败
    await new Promise((_, reject) => setTimeout(() => reject(new Error('网络错误')), 1500))
  } catch (error) {
    errorResult.value = '发送失败：网络错误'
  } finally {
    errorButtonRef.value?.setLoading(false)
  }
}

// 控制方法
const handleControlClick = () => {
  controlResult.value = '点击了手动控制按钮'
}

const startCountdown = () => {
  controlButtonRef.value?.startCountdown()
  controlResult.value = '手动开始倒计时'
}

const stopCountdown = () => {
  controlButtonRef.value?.stopCountdown()
  controlResult.value = '手动停止倒计时'
}

const setLoading = () => {
  const currentState = controlButtonRef.value?.getState()
  const newLoading = !currentState?.isLoading
  controlButtonRef.value?.setLoading(newLoading)
  controlResult.value = `设置加载状态: ${newLoading}`
}

const getState = () => {
  const state = controlButtonRef.value?.getState()
  controlResult.value = `当前状态: ${JSON.stringify(state)}`
}

// 事件监听方法
const addEventLog = (event: string, data: any) => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.push({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : String(data)
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 10) {
    eventLogs.value.shift()
  }
}

const handleEventClick = () => {
  if (!verificationCodeButtonUtils.validatePhone(phone6.value)) {
    addEventLog('click', '手机号格式错误')
    return
  }
  
  addEventLog('click', `向 ${phone6.value} 发送验证码`)
}

const handleCountdownStart = (countdown: number) => {
  addEventLog('countdownStart', `开始倒计时 ${countdown} 秒`)
}

const handleCountdownTick = (remaining: number) => {
  addEventLog('countdownTick', `剩余 ${remaining} 秒`)
}

const handleCountdownEnd = () => {
  addEventLog('countdownEnd', '倒计时结束')
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 16px;
}

.demo-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

.demo-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.demo-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.demo-input {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.demo-input:focus {
  border-color: #007aff;
}

.demo-result {
  font-size: 12px;
  color: #52c41a;
  margin-top: 8px;
  display: block;
  padding: 8px;
  background-color: #f6ffed;
  border-radius: 4px;
  border: 1px solid #d9f7be;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.size-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.control-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 6px 12px;
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.control-btn:active {
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}

.event-logs {
  max-height: 200px;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
}

.event-log {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
}

.event-log:last-child {
  border-bottom: none;
}

.event-time {
  color: #999;
  min-width: 60px;
}

.event-name {
  color: #007aff;
  font-weight: 500;
  min-width: 80px;
}

.event-data {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .content {
    padding: 12px;
  }

  .demo-card {
    padding: 12px;
  }

  .section-title {
    font-size: 16px;
  }

  .demo-row {
    gap: 8px;
  }

  .theme-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
  }

  .control-buttons {
    gap: 6px;
  }

  .control-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}
</style>
