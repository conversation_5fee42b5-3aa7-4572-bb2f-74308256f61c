import{y as a,z as e,A as t,o as l,c as s,w as i,a as c,B as u,b as d,t as o,k as r,r as n,F as p,j as m,f,i as _,d as g,e as v}from"./index-ChUEiI3E.js";import{_ as h,S as y,a as b}from"./index.B_fb1lzg.js";import{n as C,C as k}from"./index.DI3nc_37.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";const j=x(a({__name:"skeleton-simple",setup(a){const x=e(0),j=e(!0),T=e([{id:1,title:"Vue 3 Composition API 最佳实践",summary:"深入探讨 Vue 3 Composition API 的使用技巧和最佳实践，帮助开发者更好地组织代码逻辑。",author:{name:"张三",avatar:"/static/avatar1.png"},publishTime:"2024-01-15 10:30",readCount:1234,likeCount:89},{id:2,title:"TypeScript 进阶指南",summary:"从基础到进阶，全面掌握 TypeScript 的类型系统，提升代码质量和开发效率。",author:{name:"李四",avatar:"/static/avatar2.png"},publishTime:"2024-01-14 15:20",readCount:2156,likeCount:156},{id:3,title:"UniApp 跨平台开发实战",summary:"使用 UniApp 开发跨平台应用的实战经验分享，包括性能优化和平台适配技巧。",author:{name:"王五",avatar:"/static/avatar3.png"},publishTime:"2024-01-13 09:45",readCount:987,likeCount:67}]),w=e([{id:1,name:"智能手表",price:1299,description:"健康监测，运动追踪",image:"/static/product1.jpg"},{id:2,name:"无线耳机",price:599,description:"降噪技术，高音质",image:"/static/product2.jpg"},{id:3,name:"智能音箱",price:299,description:"语音控制，智能家居",image:"/static/product3.jpg"},{id:4,name:"平板电脑",price:2999,description:"轻薄便携，高清屏幕",image:"/static/product4.jpg"}]);t((()=>{x.value=C.getTotalHeight(),setTimeout((()=>{j.value=!1}),2e3)}));const F=()=>{j.value=!j.value},A=()=>{j.value=!0,setTimeout((()=>{j.value=!1}),1500)};return(a,e)=>{const t=f,C=_,S=g,I=v;return l(),s(C,{class:"page"},{default:i((()=>[c(u(k),{title:"骨架屏简单演示","show-back":!0}),c(C,{class:"content",style:m({paddingTop:x.value+"px"})},{default:i((()=>[c(C,{class:"controls"},{default:i((()=>[c(t,{onClick:F,class:"control-btn"},{default:i((()=>[d(o(j.value?"显示内容":"显示骨架屏"),1)])),_:1}),c(t,{onClick:A,class:"control-btn secondary"},{default:i((()=>[d(" 重新加载 ")])),_:1})])),_:1}),c(C,{class:"article-list"},{default:i((()=>[(l(!0),r(p,null,n(T.value,(a=>(l(),s(C,{key:a.id,class:"article-item"},{default:i((()=>[c(u(y),{loading:j.value,avatar:!0,title:!0,paragraph:!0,"paragraph-rows":2,animated:!0},{default:i((()=>[c(C,{class:"article-content"},{default:i((()=>[c(C,{class:"article-header"},{default:i((()=>[c(S,{class:"author-avatar",src:a.author.avatar,mode:"aspectFill"},null,8,["src"]),c(C,{class:"author-info"},{default:i((()=>[c(I,{class:"author-name"},{default:i((()=>[d(o(a.author.name),1)])),_:2},1024),c(I,{class:"publish-time"},{default:i((()=>[d(o(a.publishTime),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(I,{class:"article-title"},{default:i((()=>[d(o(a.title),1)])),_:2},1024),c(I,{class:"article-summary"},{default:i((()=>[d(o(a.summary),1)])),_:2},1024),c(C,{class:"article-footer"},{default:i((()=>[c(I,{class:"read-count"},{default:i((()=>[d("阅读 "+o(a.readCount),1)])),_:2},1024),c(I,{class:"like-count"},{default:i((()=>[d("点赞 "+o(a.likeCount),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["loading"])])),_:2},1024)))),128))])),_:1}),c(C,{class:"user-section"},{default:i((()=>[c(I,{class:"section-title"},{default:i((()=>[d("用户信息")])),_:1}),c(C,{class:"user-card"},{default:i((()=>[c(u(y),{loading:j.value,template:"profile",animated:!0},{default:i((()=>[c(C,{class:"user-content"},{default:i((()=>[c(S,{class:"user-avatar",src:"/static/user-large.png",mode:"aspectFill"}),c(C,{class:"user-info"},{default:i((()=>[c(I,{class:"user-name"},{default:i((()=>[d("李明")])),_:1}),c(I,{class:"user-title"},{default:i((()=>[d("高级前端工程师")])),_:1}),c(I,{class:"user-company"},{default:i((()=>[d("某知名互联网公司")])),_:1}),c(I,{class:"user-bio"},{default:i((()=>[d(" 专注于前端技术研究，热爱开源，喜欢分享技术心得。 擅长 Vue.js、React、TypeScript 等技术栈。 ")])),_:1})])),_:1})])),_:1})])),_:1},8,["loading"])])),_:1})])),_:1}),c(C,{class:"product-section"},{default:i((()=>[c(I,{class:"section-title"},{default:i((()=>[d("热门产品")])),_:1}),c(C,{class:"product-grid"},{default:i((()=>[(l(!0),r(p,null,n(w.value,(a=>(l(),s(C,{key:a.id,class:"product-card"},{default:i((()=>[c(u(y),{loading:j.value,template:"card",animated:!0},{default:i((()=>[c(C,{class:"product-content"},{default:i((()=>[c(S,{class:"product-image",src:a.image,mode:"aspectFill"},null,8,["src"]),c(C,{class:"product-info"},{default:i((()=>[c(I,{class:"product-name"},{default:i((()=>[d(o(a.name),1)])),_:2},1024),c(I,{class:"product-price"},{default:i((()=>[d("¥"+o(a.price),1)])),_:2},1024),c(I,{class:"product-desc"},{default:i((()=>[d(o(a.description),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["loading"])])),_:2},1024)))),128))])),_:1})])),_:1}),c(C,{class:"elements-section"},{default:i((()=>[c(I,{class:"section-title"},{default:i((()=>[d("单独元素")])),_:1}),c(C,{class:"elements-grid"},{default:i((()=>[c(C,{class:"element-item"},{default:i((()=>[c(I,{class:"element-label"},{default:i((()=>[d("头像")])),_:1}),c(u(b),{loading:j.value,type:"avatar",animated:!0},{default:i((()=>[c(S,{class:"demo-avatar",src:h,mode:"aspectFill"})])),_:1},8,["loading"])])),_:1}),c(C,{class:"element-item"},{default:i((()=>[c(I,{class:"element-label"},{default:i((()=>[d("按钮")])),_:1}),c(u(b),{loading:j.value,type:"button",width:"120px",animated:!0},{default:i((()=>[c(t,{class:"demo-button"},{default:i((()=>[d("立即购买")])),_:1})])),_:1},8,["loading"])])),_:1}),c(C,{class:"element-item"},{default:i((()=>[c(I,{class:"element-label"},{default:i((()=>[d("文本")])),_:1}),c(u(b),{loading:j.value,type:"text",width:"200px",animated:!0},{default:i((()=>[c(I,{class:"demo-text"},{default:i((()=>[d("这是一段示例文本")])),_:1})])),_:1},8,["loading"])])),_:1}),c(C,{class:"element-item"},{default:i((()=>[c(I,{class:"element-label"},{default:i((()=>[d("图片")])),_:1}),c(u(b),{loading:j.value,type:"image",width:"120px",height:"80px",animated:!0},{default:i((()=>[c(S,{class:"demo-image",src:"/static/demo.jpg",mode:"aspectFill"})])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}}}),[["__scopeId","data-v-3b901c4b"]]);export{j as default};
