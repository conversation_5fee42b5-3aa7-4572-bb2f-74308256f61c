import{m as e,n as t,h as s,g as a,c as l,w as c,i as n,o,a as r,b as i,f as d,e as u,S as f}from"./index-ChUEiI3E.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";const _=p({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/basic-types"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/type-aliases"})},markAsCompleted(){try{let e=s("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("interfaces")||(e.completedItems.push("interfaces"),e.lastUpdate=Date.now(),a("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("接口课程加载完成")}},[["render",function(e,t,s,a,p,_){const m=d,x=u,g=n,b=f;return o(),l(g,{class:"container"},{default:c((()=>[r(g,{class:"header"},{default:c((()=>[r(m,{onClick:_.goBack,class:"back-btn"},{default:c((()=>[i("← 返回")])),_:1},8,["onClick"]),r(x,{class:"title"},{default:c((()=>[i("接口 (Interface)")])),_:1}),r(g,{class:"progress-indicator"},{default:c((()=>[i("2/9")])),_:1})])),_:1}),r(b,{"scroll-y":"",class:"content"},{default:c((()=>[r(g,{class:"lesson-intro"},{default:c((()=>[r(x,{class:"intro-title"},{default:c((()=>[i("学习目标")])),_:1}),r(x,{class:"intro-text"},{default:c((()=>[i("掌握接口的定义、使用、继承和扩展，理解接口在 TypeScript 中的重要作用")])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("📋 基本接口定义")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('// 定义用户接口 interface User { id: number name: string email: string age: number } // 使用接口 const user: User = { id: 1, name: "张三", email: "<EMAIL>", age: 25 }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 接口定义了对象的结构和类型")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 使用 interface 关键字声明")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 对象必须包含接口中定义的所有属性")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("❓ 可选属性")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('interface Product { id: number name: string price: number description?: string // 可选属性 category?: string // 可选属性 } // 可以不提供可选属性 const product1: Product = { id: 1, name: "iPhone", price: 6999 } // 也可以提供可选属性 const product2: Product = { id: 2, name: "MacBook", price: 12999, description: "苹果笔记本电脑", category: "电脑" }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 使用 ? 标记可选属性")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 可选属性可以不在对象中出现")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 提高了接口的灵活性")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("🔒 只读属性")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('interface Config { readonly apiUrl: string readonly version: string timeout?: number } const config: Config = { apiUrl: "https://api.example.com", version: "1.0.0", timeout: 5000 } // 错误：不能修改只读属性 // config.apiUrl = "https://new-api.com" // Error!')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 使用 readonly 标记只读属性")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 只读属性只能在创建时赋值")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 适用于配置信息等不可变数据")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("🔧 函数类型接口")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('// 定义函数接口 interface Calculator { (a: number, b: number): number } // 实现函数接口 const add: Calculator = (x, y) => x + y const multiply: Calculator = (x, y) => x * y // 带有属性的函数接口 interface Counter { (start: number): string interval: number reset(): void } function getCounter(): Counter { let counter = function(start: number) { return `计数从 ${start} 开始` } as Counter counter.interval = 1000 counter.reset = function() { console.log("重置计数器") } return counter }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 接口可以描述函数的类型")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 函数接口定义参数和返回值类型")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 可以为函数添加属性和方法")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("📚 数组类型接口")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('// 索引签名接口 interface StringArray { [index: number]: string } const myArray: StringArray = ["张三", "李四", "王五"] const firstItem: string = myArray[0] // 字典类型接口 interface Dictionary { [key: string]: any } const userInfo: Dictionary = { name: "张三", age: 25, isActive: true }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 使用索引签名描述数组或对象的索引类型")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• [index: number] 用于数组索引")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• [key: string] 用于对象属性")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("🔗 接口继承")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('// 基础接口 interface Animal { name: string age: number } // 继承接口 interface Dog extends Animal { breed: string bark(): void } // 多重继承 interface Flyable { fly(): void } interface Bird extends Animal, Flyable { wingspan: number } // 实现继承的接口 const myDog: Dog = { name: "旺财", age: 3, breed: "金毛", bark() { console.log("汪汪汪!") } }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 使用 extends 关键字实现接口继承")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 子接口包含父接口的所有属性")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 支持多重继承")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("🔄 接口合并")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('// 同名接口会自动合并 interface User { name: string } interface User { age: number } interface User { email: string } // 合并后的 User 接口包含所有属性 const user: User = { name: "张三", age: 25, email: "<EMAIL>" }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 同名接口会自动合并")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 常用于扩展第三方库的类型定义")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 合并时不能有冲突的属性类型")])),_:1})])),_:1})])),_:1}),r(g,{class:"section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("🏗️ 类实现接口")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i('// 定义接口 interface Drivable { speed: number drive(): void stop(): void } // 类实现接口 class Car implements Drivable { speed: number = 0 drive(): void { this.speed = 60 console.log(`汽车以 ${this.speed} km/h 的速度行驶`) } stop(): void { this.speed = 0 console.log("汽车停止了") } }')])),_:1})])),_:1}),r(g,{class:"explanation"},{default:c((()=>[r(x,{class:"exp-text"},{default:c((()=>[i("• 使用 implements 关键字让类实现接口")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 类必须实现接口中的所有属性和方法")])),_:1}),r(x,{class:"exp-text"},{default:c((()=>[i("• 一个类可以实现多个接口")])),_:1})])),_:1})])),_:1}),r(g,{class:"practice-section"},{default:c((()=>[r(x,{class:"section-title"},{default:c((()=>[i("🎯 实践练习")])),_:1}),r(g,{class:"practice-item"},{default:c((()=>[r(x,{class:"practice-title"},{default:c((()=>[i("练习 1：定义学生接口")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i("// 定义一个学生接口，包含： // - id (必需) // - name (必需) // - age (必需) // - grade (可选) // - subjects (字符串数组) // - study() 方法")])),_:1})])),_:1})])),_:1}),r(g,{class:"practice-item"},{default:c((()=>[r(x,{class:"practice-title"},{default:c((()=>[i("练习 2：接口继承")])),_:1}),r(g,{class:"code-block"},{default:c((()=>[r(x,{class:"code"},{default:c((()=>[i("// 基于上面的学生接口，创建一个研究生接口 // 添加额外的属性： // - supervisor (导师姓名) // - researchTopic (研究方向) // - thesis() 方法")])),_:1})])),_:1})])),_:1})])),_:1}),r(g,{class:"navigation"},{default:c((()=>[r(m,{onClick:_.previousLesson,class:"nav-btn secondary"},{default:c((()=>[i("上一课：基础类型")])),_:1},8,["onClick"]),r(m,{onClick:_.nextLesson,class:"nav-btn primary"},{default:c((()=>[i("下一课：类型别名")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-e6463957"]]);export{_ as default};
