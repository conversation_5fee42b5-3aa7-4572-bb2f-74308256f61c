import{m as e,n as t,h as s,g as a,c as l,w as o,i as r,o as c,a as n,b as i,f as u,e as p,S as d}from"./index-ChUEiI3E.js";import{_ as f}from"./_plugin-vue_export-helper.BCo6x5W8.js";const m=f({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/classes"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/advanced-types"})},markAsCompleted(){try{let e=s("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("modules")||(e.completedItems.push("modules"),e.lastUpdate=Date.now(),a("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("模块和命名空间课程加载完成")}},[["render",function(e,t,s,a,f,m){const x=u,_=p,g=r,y=d;return c(),l(g,{class:"container"},{default:o((()=>[n(g,{class:"header"},{default:o((()=>[n(x,{onClick:m.goBack,class:"back-btn"},{default:o((()=>[i("← 返回")])),_:1},8,["onClick"]),n(_,{class:"title"},{default:o((()=>[i("模块和命名空间")])),_:1}),n(g,{class:"progress-indicator"},{default:o((()=>[i("6/9")])),_:1})])),_:1}),n(y,{"scroll-y":"",class:"content"},{default:o((()=>[n(g,{class:"lesson-intro"},{default:o((()=>[n(_,{class:"intro-title"},{default:o((()=>[i("学习目标")])),_:1}),n(_,{class:"intro-text"},{default:o((()=>[i("掌握 TypeScript 模块系统，理解导入导出、命名空间等模块化开发概念")])),_:1})])),_:1}),n(g,{class:"section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("📤 模块导出")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i('// utils.ts - 导出工具函数 export function formatDate(date: Date): string { return date.toLocaleDateString() } export function calculateAge(birthDate: Date): number { const today = new Date() return today.getFullYear() - birthDate.getFullYear() } // 导出常量 export const API_URL = "https://api.example.com" export const VERSION = "1.0.0" // 导出类型 export interface User { id: number name: string email: string } export type UserRole = "admin" | "user" | "guest" // 默认导出 export default class Logger { log(message: string): void { console.log(`[${new Date().toISOString()}] ${message}`) } }')])),_:1})])),_:1}),n(g,{class:"explanation"},{default:o((()=>[n(_,{class:"exp-text"},{default:o((()=>[i("• 使用 export 关键字导出模块成员")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 可以导出函数、变量、类、接口、类型等")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• export default 用于默认导出")])),_:1})])),_:1})])),_:1}),n(g,{class:"section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("📥 模块导入")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// main.ts - 导入模块 import Logger from './utils' // 导入默认导出 import { formatDate, calculateAge, API_URL } from './utils' // 命名导入 import { User, UserRole } from './utils' // 导入类型 // 使用导入的内容 const logger = new Logger() logger.log(\"应用启动\") const today = new Date() console.log(formatDate(today)) const user: User = { id: 1, name: \"张三\", email: \"<EMAIL>\" } // 重命名导入 import { formatDate as dateFormatter } from './utils' console.log(dateFormatter(today)) // 导入所有导出 import * as Utils from './utils' console.log(Utils.API_URL) console.log(Utils.formatDate(today)) // 仅导入类型（TypeScript 3.8+） import type { User as UserType } from './utils' const userData: UserType = { id: 1, name: \"李四\", email: \"<EMAIL>\" }")])),_:1})])),_:1}),n(g,{class:"explanation"},{default:o((()=>[n(_,{class:"exp-text"},{default:o((()=>[i("• import 语句用于导入模块")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 支持默认导入、命名导入、重命名导入")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• import type 仅导入类型信息")])),_:1})])),_:1})])),_:1}),n(g,{class:"section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("🔄 重新导出")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// index.ts - 模块入口文件 export { formatDate, calculateAge } from './utils' export { default as Logger } from './utils' export type { User, UserRole } from './utils' // 重新导出并重命名 export { API_URL as ApiEndpoint } from './utils' // 导出所有 export * from './constants' export * from './helpers' // 条件导出 ")])),_:1})])),_:1}),n(g,{class:"explanation"},{default:o((()=>[n(_,{class:"exp-text"},{default:o((()=>[i("• 重新导出可以创建模块的统一入口")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 支持选择性导出和重命名")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 便于管理大型项目的模块结构")])),_:1})])),_:1})])),_:1}),n(g,{class:"section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("🏷️ 命名空间")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// 命名空间定义 namespace Geometry { export interface Point { x: number y: number } export class Circle { constructor(public center: Point, public radius: number) {} area(): number { return Math.PI * this.radius * this.radius } } export class Rectangle { constructor( public topLeft: Point, public width: number, public height: number ) {} area(): number { return this.width * this.height } } export function distance(p1: Point, p2: Point): number { return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)) } } // 使用命名空间 const point1: Geometry.Point = { x: 0, y: 0 } const point2: Geometry.Point = { x: 3, y: 4 } const circle = new Geometry.Circle(point1, 5) const rectangle = new Geometry.Rectangle(point1, 10, 8) console.log(circle.area()) // 78.54 console.log(Geometry.distance(point1, point2)) // 5")])),_:1})])),_:1}),n(g,{class:"explanation"},{default:o((()=>[n(_,{class:"exp-text"},{default:o((()=>[i("• 命名空间用于组织相关的代码")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 避免全局命名冲突")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 现代开发中更推荐使用模块")])),_:1})])),_:1})])),_:1}),n(g,{class:"section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("🔍 模块解析")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// 相对导入 import { helper } from './utils/helper' // 相对路径 import { config } from '../config/app' // 上级目录 // 非相对导入 import { Component } from 'vue' // node_modules import { ApiClient } from '@/api/client' // 路径别名 // 动态导入 async function loadModule() { const { heavyFunction } = await import('./heavy-module') return heavyFunction() } // 条件导入 let platformUtils: any // 类型导入 import type { ComponentOptions } from 'vue' import type { RouteConfig } from 'vue-router'")])),_:1})])),_:1}),n(g,{class:"explanation"},{default:o((()=>[n(_,{class:"exp-text"},{default:o((()=>[i("• 相对导入用于项目内部模块")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 非相对导入用于第三方库")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 动态导入支持代码分割")])),_:1})])),_:1})])),_:1}),n(g,{class:"section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("🦄 UniApp 模块化实践")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// api/index.ts - API 模块 export class ApiClient { private baseURL: string = 'https://api.example.com' async request<T>(url: string, options?: UniApp.RequestOptions): Promise<T> { return new Promise((resolve, reject) => { uni.request({ url: this.baseURL + url, ...options, success: (res) => resolve(res.data as T), fail: reject }) }) } } export const apiClient = new ApiClient() // utils/storage.ts - 存储工具 export class Storage { static set(key: string, value: any): void { uni.setStorageSync(key, JSON.stringify(value)) } static get<T>(key: string): T | null { try { const value = uni.getStorageSync(key) return value ? JSON.parse(value) : null } catch { return null } } } // types/index.ts - 类型定义 export interface UserInfo { id: number name: string avatar: string } export interface ApiResponse<T> { code: number data: T message: string }")])),_:1})])),_:1}),n(g,{class:"explanation"},{default:o((()=>[n(_,{class:"exp-text"},{default:o((()=>[i("• 按功能模块组织代码")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 统一的 API 接口封装")])),_:1}),n(_,{class:"exp-text"},{default:o((()=>[i("• 类型定义集中管理")])),_:1})])),_:1})])),_:1}),n(g,{class:"practice-section"},{default:o((()=>[n(_,{class:"section-title"},{default:o((()=>[i("🎯 实践练习")])),_:1}),n(g,{class:"practice-item"},{default:o((()=>[n(_,{class:"practice-title"},{default:o((()=>[i("练习 1：创建工具模块")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// 创建一个 dateUtils 模块，包含： // - formatDate 函数 // - parseDate 函数 // - DateFormat 类型 // - 默认导出 DateHelper 类")])),_:1})])),_:1})])),_:1}),n(g,{class:"practice-item"},{default:o((()=>[n(_,{class:"practice-title"},{default:o((()=>[i("练习 2：模块重新导出")])),_:1}),n(g,{class:"code-block"},{default:o((()=>[n(_,{class:"code"},{default:o((()=>[i("// 创建一个 index.ts 文件，重新导出： // - dateUtils 的所有导出 // - stringUtils 的部分导出 // - 类型定义模块的所有类型")])),_:1})])),_:1})])),_:1})])),_:1}),n(g,{class:"navigation"},{default:o((()=>[n(x,{onClick:m.previousLesson,class:"nav-btn secondary"},{default:o((()=>[i("上一课：类和继承")])),_:1},8,["onClick"]),n(x,{onClick:m.nextLesson,class:"nav-btn primary"},{default:o((()=>[i("下一课：高级类型")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-e28b2655"]]);export{m as default};
