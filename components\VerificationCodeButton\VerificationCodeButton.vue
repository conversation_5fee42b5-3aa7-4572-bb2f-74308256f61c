<template>
  <button
    :class="[
      'verification-code-button',
      {
        'is-disabled': isDisabled,
        'is-counting': isCounting,
        'is-loading': isLoading
      },
      customClass
    ]"
    :style="buttonStyle"
    :disabled="isDisabled"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner" :style="spinnerStyle"></view>
      <text class="button-text" :style="textStyle">{{ loadingText }}</text>
    </view>
    
    <!-- 倒计时状态 -->
    <text v-else-if="isCounting" class="button-text" :style="textStyle">
      {{ countdownText }}
    </text>
    
    <!-- 正常状态 -->
    <text v-else class="button-text" :style="textStyle">
      {{ normalText }}
    </text>
  </button>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, watch } from 'vue'

// 类型定义
interface Props {
  // 基础配置
  disabled?: boolean
  loading?: boolean
  
  // 倒计时配置
  countdown?: number
  autoStart?: boolean
  
  // 文本配置
  text?: string
  loadingText?: string
  countdownFormat?: string
  
  // 样式配置
  width?: number | string
  height?: number | string
  backgroundColor?: string
  disabledBackgroundColor?: string
  textColor?: string
  disabledTextColor?: string
  fontSize?: number
  borderRadius?: number
  borderWidth?: number
  borderColor?: string
  borderStyle?: string
  
  // 其他配置
  customClass?: string
  customStyle?: Record<string, any>
}

// 事件接口
interface Emits {
  click: []
  countdownStart: [countdown: number]
  countdownEnd: []
  countdownTick: [remaining: number]
}

// Props 默认值
const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  loading: false,
  countdown: 60,
  autoStart: false,
  text: '获取验证码',
  loadingText: '发送中...',
  countdownFormat: '{time}s后重新获取',
  width: 'auto',
  height: 36,
  backgroundColor: '#007aff',
  disabledBackgroundColor: '#c8c9cc',
  textColor: '#ffffff',
  disabledTextColor: '#969799',
  fontSize: 14,
  borderRadius: 4,
  borderWidth: 0,
  borderColor: 'transparent',
  borderStyle: 'solid',
  customClass: '',
  customStyle: () => ({})
})

// 事件定义
const emit = defineEmits<Emits>()

// 响应式数据
const isCounting = ref(false)
const isLoading = ref(false)
const remainingTime = ref(0)
let countdownTimer: number | null = null

// 计算属性
const isDisabled = computed(() => {
  return props.disabled || isCounting.value || isLoading.value
})

const normalText = computed(() => {
  return props.text
})

const countdownText = computed(() => {
  return props.countdownFormat.replace('{time}', remainingTime.value.toString())
})

const buttonStyle = computed(() => {
  const baseStyle = {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    fontSize: `${props.fontSize}px`,
    borderRadius: `${props.borderRadius}px`,
    borderWidth: `${props.borderWidth}px`,
    borderColor: props.borderColor,
    borderStyle: props.borderStyle,
    ...props.customStyle
  }

  if (isDisabled.value) {
    baseStyle.backgroundColor = props.disabledBackgroundColor
  } else {
    baseStyle.backgroundColor = props.backgroundColor
  }

  return baseStyle
})

const textStyle = computed(() => {
  return {
    color: isDisabled.value ? props.disabledTextColor : props.textColor
  }
})

const spinnerStyle = computed(() => {
  return {
    borderTopColor: isDisabled.value ? props.disabledTextColor : props.textColor
  }
})

// 方法
const startCountdown = () => {
  if (isCounting.value) return

  isCounting.value = true
  remainingTime.value = props.countdown
  
  emit('countdownStart', props.countdown)
  
  countdownTimer = setInterval(() => {
    remainingTime.value--
    emit('countdownTick', remainingTime.value)
    
    if (remainingTime.value <= 0) {
      stopCountdown()
    }
  }, 1000)
}

const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  
  isCounting.value = false
  remainingTime.value = 0
  emit('countdownEnd')
}

const setLoading = (loading: boolean) => {
  isLoading.value = loading
}

const handleClick = () => {
  if (isDisabled.value) return
  
  emit('click')
  
  if (props.autoStart) {
    startCountdown()
  }
}

// 监听器
watch(() => props.loading, (newValue) => {
  isLoading.value = newValue
})

// 生命周期
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})

// 暴露方法给父组件
defineExpose({
  startCountdown,
  stopCountdown,
  setLoading,
  getState: () => ({
    isCounting: isCounting.value,
    isLoading: isLoading.value,
    remainingTime: remainingTime.value,
    isDisabled: isDisabled.value
  })
})
</script>

<style scoped>
.verification-code-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  user-select: none;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.verification-code-button:active:not(.is-disabled) {
  transform: scale(0.98);
}

.verification-code-button.is-disabled {
  cursor: not-allowed;
}

.verification-code-button.is-counting {
  cursor: not-allowed;
}

.verification-code-button.is-loading {
  cursor: not-allowed;
}

.button-text {
  line-height: 1;
  white-space: nowrap;
  font-weight: 500;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .verification-code-button {
    padding: 0 12px;
  }
  
  .button-text {
    font-size: 13px;
  }
  
  .loading-spinner {
    width: 12px;
    height: 12px;
  }
}
</style>
