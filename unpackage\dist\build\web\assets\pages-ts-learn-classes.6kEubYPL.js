import{m as e,n as t,h as a,g as s,c as l,w as n,i as c,o,a as r,b as i,f as u,e as d,S as m}from"./index-ChUEiI3E.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=p({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/generics"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/modules"})},markAsCompleted(){try{let e=a("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("classes")||(e.completedItems.push("classes"),e.lastUpdate=Date.now(),s("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("类和继承课程加载完成")}},[["render",function(e,t,a,s,p,g){const f=u,b=d,_=c,h=m;return o(),l(_,{class:"container"},{default:n((()=>[r(_,{class:"header"},{default:n((()=>[r(f,{onClick:g.goBack,class:"back-btn"},{default:n((()=>[i("← 返回")])),_:1},8,["onClick"]),r(b,{class:"title"},{default:n((()=>[i("类和继承")])),_:1}),r(_,{class:"progress-indicator"},{default:n((()=>[i("5/9")])),_:1})])),_:1}),r(h,{"scroll-y":"",class:"content"},{default:n((()=>[r(_,{class:"lesson-intro"},{default:n((()=>[r(b,{class:"intro-title"},{default:n((()=>[i("学习目标")])),_:1}),r(b,{class:"intro-text"},{default:n((()=>[i("掌握 TypeScript 中的类定义、继承、访问修饰符等面向对象编程概念")])),_:1})])),_:1}),r(_,{class:"section"},{default:n((()=>[r(b,{class:"section-title"},{default:n((()=>[i("🏗️ 基本类定义")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i('// 基本类定义 class Person { // 属性声明 name: string age: number // 构造函数 constructor(name: string, age: number) { this.name = name this.age = age } // 方法 greet(): string { return `你好，我是${this.name}，今年${this.age}岁` } // 获取器 get info(): string { return `${this.name} (${this.age}岁)` } // 设置器 set newAge(age: number) { if (age > 0) { this.age = age } } } // 使用类 const person = new Person("张三", 25) console.log(person.greet()) // "你好，我是张三，今年25岁" console.log(person.info) // "张三 (25岁)" person.newAge = 26')])),_:1})])),_:1}),r(_,{class:"explanation"},{default:n((()=>[r(b,{class:"exp-text"},{default:n((()=>[i("• 使用 class 关键字定义类")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 构造函数用于初始化实例")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 支持 getter 和 setter 方法")])),_:1})])),_:1})])),_:1}),r(_,{class:"section"},{default:n((()=>[r(b,{class:"section-title"},{default:n((()=>[i("🔐 访问修饰符")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i('class BankAccount { public accountNumber: string // 公共属性 private balance: number // 私有属性 protected bankName: string // 受保护属性 readonly createdAt: Date // 只读属性 constructor(accountNumber: string, initialBalance: number) { this.accountNumber = accountNumber this.balance = initialBalance this.bankName = "中国银行" this.createdAt = new Date() } // 公共方法 public getBalance(): number { return this.balance } // 私有方法 private validateAmount(amount: number): boolean { return amount > 0 && amount <= this.balance } // 受保护方法 protected logTransaction(type: string, amount: number): void { console.log(`${type}: ${amount}`) } public withdraw(amount: number): boolean { if (this.validateAmount(amount)) { this.balance -= amount this.logTransaction("取款", amount) return true } return false } } const account = new BankAccount("123456", 1000) console.log(account.accountNumber) // OK: public console.log(account.getBalance()) // OK: public method // console.log(account.balance) // Error: private // account.createdAt = new Date() // Error: readonly')])),_:1})])),_:1}),r(_,{class:"explanation"},{default:n((()=>[r(b,{class:"exp-text"},{default:n((()=>[i("• public: 公共成员，可以在任何地方访问")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• private: 私有成员，只能在类内部访问")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• protected: 受保护成员，类和子类可以访问")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• readonly: 只读成员，只能在声明或构造函数中赋值")])),_:1})])),_:1})])),_:1}),r(_,{class:"section"},{default:n((()=>[r(b,{class:"section-title"},{default:n((()=>[i("🔗 类的继承")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i('// 基类 class Animal { protected name: string constructor(name: string) { this.name = name } move(distance: number = 0): void { console.log(`${this.name} 移动了 ${distance} 米`) } makeSound(): void { console.log("动物发出声音") } } // 继承类 class Dog extends Animal { breed: string constructor(name: string, breed: string) { super(name) // 调用父类构造函数 this.breed = breed } // 重写父类方法 makeSound(): void { console.log(`${this.name} 汪汪叫`) } // 新增方法 wagTail(): void { console.log(`${this.name} 摇尾巴`) } } class Cat extends Animal { constructor(name: string) { super(name) } makeSound(): void { console.log(`${this.name} 喵喵叫`) } climb(): void { console.log(`${this.name} 爬树`) } } // 使用继承 const dog = new Dog("旺财", "金毛") const cat = new Cat("咪咪") dog.makeSound() // "旺财 汪汪叫" dog.wagTail() // "旺财 摇尾巴" cat.makeSound() // "咪咪 喵喵叫" cat.climb() // "咪咪 爬树"')])),_:1})])),_:1}),r(_,{class:"explanation"},{default:n((()=>[r(b,{class:"exp-text"},{default:n((()=>[i("• 使用 extends 关键字实现继承")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• super() 调用父类构造函数")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 子类可以重写父类方法")])),_:1})])),_:1})])),_:1}),r(_,{class:"section"},{default:n((()=>[r(b,{class:"section-title"},{default:n((()=>[i("🎭 抽象类")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i('// 抽象类 abstract class Shape { abstract name: string // 抽象方法 abstract calculateArea(): number abstract calculatePerimeter(): number // 具体方法 displayInfo(): void { console.log(`形状: ${this.name}`) console.log(`面积: ${this.calculateArea()}`) console.log(`周长: ${this.calculatePerimeter()}`) } } // 实现抽象类 class Rectangle extends Shape { name = "矩形" constructor(private width: number, private height: number) { super() } calculateArea(): number { return this.width * this.height } calculatePerimeter(): number { return 2 * (this.width + this.height) } } class Circle extends Shape { name = "圆形" constructor(private radius: number) { super() } calculateArea(): number { return Math.PI * this.radius * this.radius } calculatePerimeter(): number { return 2 * Math.PI * this.radius } } // 使用抽象类 const rectangle = new Rectangle(5, 3) const circle = new Circle(4) rectangle.displayInfo() circle.displayInfo() // const shape = new Shape() // Error: 不能实例化抽象类')])),_:1})])),_:1}),r(_,{class:"explanation"},{default:n((()=>[r(b,{class:"exp-text"},{default:n((()=>[i("• 抽象类不能被直接实例化")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 抽象方法必须在子类中实现")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 可以包含具体方法和抽象方法")])),_:1})])),_:1})])),_:1}),r(_,{class:"section"},{default:n((()=>[r(b,{class:"section-title"},{default:n((()=>[i("⚡ 静态成员")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i('class MathUtils { // 静态属性 static PI = 3.14159 static version = "1.0.0" // 静态方法 static add(a: number, b: number): number { return a + b } static multiply(a: number, b: number): number { return a * b } static circleArea(radius: number): number { return this.PI * radius * radius } // 实例方法 formatNumber(num: number): string { return num.toFixed(2) } } // 使用静态成员 console.log(MathUtils.PI) // 3.14159 console.log(MathUtils.add(5, 3)) // 8 console.log(MathUtils.circleArea(2)) // 12.56636 // 实例方法需要创建实例 const utils = new MathUtils() console.log(utils.formatNumber(3.14159)) // "3.14"')])),_:1})])),_:1}),r(_,{class:"explanation"},{default:n((()=>[r(b,{class:"exp-text"},{default:n((()=>[i("• 静态成员属于类本身，不属于实例")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 通过类名直接访问静态成员")])),_:1}),r(b,{class:"exp-text"},{default:n((()=>[i("• 静态方法中不能访问实例成员")])),_:1})])),_:1})])),_:1}),r(_,{class:"practice-section"},{default:n((()=>[r(b,{class:"section-title"},{default:n((()=>[i("🎯 实践练习")])),_:1}),r(_,{class:"practice-item"},{default:n((()=>[r(b,{class:"practice-title"},{default:n((()=>[i("练习 1：创建员工类")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i("// 创建一个 Employee 类，包含： // - 私有属性：id, salary // - 公共属性：name, department // - 构造函数 // - 公共方法：getInfo(), raiseSalary(amount) // - 私有方法：validateSalary(salary)")])),_:1})])),_:1})])),_:1}),r(_,{class:"practice-item"},{default:n((()=>[r(b,{class:"practice-title"},{default:n((()=>[i("练习 2：继承和多态")])),_:1}),r(_,{class:"code-block"},{default:n((()=>[r(b,{class:"code"},{default:n((()=>[i("// 基于 Employee 类创建 Manager 类： // - 新增属性：team (团队成员数组) // - 重写 getInfo() 方法 // - 新增方法：addTeamMember(), getTeamSize()")])),_:1})])),_:1})])),_:1})])),_:1}),r(_,{class:"navigation"},{default:n((()=>[r(f,{onClick:g.previousLesson,class:"nav-btn secondary"},{default:n((()=>[i("上一课：泛型")])),_:1},8,["onClick"]),r(f,{onClick:g.nextLesson,class:"nav-btn primary"},{default:n((()=>[i("下一课：模块")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-be2505e1"]]);export{g as default};
