<template>
	<view class="tui-drawer-wrapper" v-if="visible || isAnimating">
		<!-- 遮罩层 -->
		<view
			v-if="mask"
			class="tui-drawer-mask"
			:class="{ 'tui-drawer-mask_show': visible && !isAnimating }"
			:style="{
				zIndex: maskZIndex,
				backgroundColor: maskColor,
				transitionDuration: duration + 'ms'
			}"
			@tap="handleMaskClick"
			@touchmove.stop.prevent
		></view>

		<!-- 抽屉容器 -->
		<view
			class="tui-drawer-container"
			:class="[
				`tui-drawer-container_${mode}`,
				visible && !isAnimating ? `tui-drawer-${mode}__show` : ''
			]"
			:style="{
				zIndex: zIndex,
				backgroundColor: backgroundColor,
				borderRadius: borderRadius,
				width: isHorizontal ? width : '100%',
				height: isVertical ? height : 'auto',
				transitionDuration: duration + 'ms'
			}"
			@touchmove.stop.prevent
		>
			<!-- 关闭按钮 -->
			<view
				v-if="showClose"
				class="tui-drawer-close"
				:class="`tui-drawer-close_${closePosition}`"
				@tap="handleCloseClick"
			>
				<text class="tui-drawer-close-icon">✕</text>
			</view>

			<!-- 内容插槽 -->
			<slot></slot>
		</view>
	</view>
</template>

<script lang="ts">
/**
 * ThorUI 抽屉组件 - 增强版
 *
 * @description 支持上、下、左、右四个方向弹出的抽屉组件，具有丰富的配置选项和完整的TypeScript支持
 *
 * @features
 * - 支持四个方向弹出（left, right, top, bottom）
 * - 完整的TypeScript类型支持
 * - 可自定义宽度、高度、背景色、圆角等样式
 * - 支持遮罩层控制和点击关闭
 * - 可选的关闭按钮，支持多种位置
 * - 丰富的事件回调（open, opened, close, closed）
 * - 平滑的动画效果，可自定义动画时长
 * - 跨平台兼容（H5、小程序、APP）
 *
 * @usage
 * ```vue
 * <template>
 *   <tui-drawer
 *     :visible="drawerVisible"
 *     mode="right"
 *     width="80%"
 *     :show-close="true"
 *     @close="handleClose"
 *     @opened="handleOpened"
 *   >
 *     <view class="drawer-content">
 *       <!-- 抽屉内容 -->
 *     </view>
 *   </tui-drawer>
 * </template>
 *
 * <script>
 * export default {
 *   data() {
 *     return {
 *       drawerVisible: false
 *     }
 *   },
 *   methods: {
 *     handleClose() {
 *       this.drawerVisible = false
 *     },
 *     handleOpened() {
 *       console.log('抽屉已打开')
 *     }
 *   }
 * }
 * </script>
 * ```
 *
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-07-29
 * @license MIT
 */

interface DrawerData {
	isAnimating: boolean
	animationTimer: number | null
}

export default {
	name: 'tuiDrawer',
	emits: ['close', 'open', 'opened', 'closed'],
	props: {
		/** 是否显示抽屉 */
		visible: {
			type: Boolean,
			default: false
		},
		/** 是否需要遮罩层 */
		mask: {
			type: Boolean,
			default: true
		},
		/** 遮罩是否可点击关闭 */
		maskClosable: {
			type: Boolean,
			default: true
		},
		/** 抽屉弹出方向：left, right, bottom, top */
		mode: {
			type: String as () => 'left' | 'right' | 'top' | 'bottom',
			default: 'right',
			validator: (value: string) => ['left', 'right', 'top', 'bottom'].includes(value)
		},
		/** 抽屉宽度（left/right模式时有效） */
		width: {
			type: String,
			default: '70%'
		},
		/** 抽屉高度（top/bottom模式时有效） */
		height: {
			type: String,
			default: '50%'
		},
		/** 抽屉 z-index 值 */
		zIndex: {
			type: [Number, String],
			default: 990
		},
		/** 遮罩 z-index 值 */
		maskZIndex: {
			type: [Number, String],
			default: 980
		},
		/** 抽屉背景色 */
		backgroundColor: {
			type: String,
			default: '#fff'
		},
		/** 遮罩背景色 */
		maskColor: {
			type: String,
			default: 'rgba(0, 0, 0, 0.6)'
		},
		/** 圆角大小 */
		borderRadius: {
			type: String,
			default: '0'
		},
		/** 动画持续时间（毫秒） */
		duration: {
			type: Number,
			default: 300
		},
		/** 是否显示关闭按钮 */
		showClose: {
			type: Boolean,
			default: false
		},
		/** 关闭按钮位置 */
		closePosition: {
			type: String as () => 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right',
			default: 'top-right',
			validator: (value: string) => ['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(value)
		}
	},
	data(): DrawerData {
		return {
			isAnimating: false,
			animationTimer: null
		}
	},
	computed: {
		/** 是否为水平方向（左右） */
		isHorizontal(): boolean {
			return this.mode === 'left' || this.mode === 'right'
		},
		/** 是否为垂直方向（上下） */
		isVertical(): boolean {
			return this.mode === 'top' || this.mode === 'bottom'
		}
	},
	watch: {
		visible: {
			handler(newVal: boolean, oldVal: boolean) {
				if (newVal !== oldVal) {
					this.handleVisibilityChange(newVal)
				}
			},
			immediate: false
		}
	},
	methods: {
		/**
		 * 处理可见性变化
		 * @description 当visible属性变化时触发，控制抽屉的显示和隐藏动画
		 * @param {boolean} visible - 新的可见性状态
		 * @private
		 */
		handleVisibilityChange(visible: boolean): void {
			if (this.animationTimer) {
				clearTimeout(this.animationTimer)
				this.animationTimer = null
			}

			if (visible) {
				this.isAnimating = true
				this.$emit('open')

				// 延迟一帧确保DOM更新
				this.$nextTick(() => {
					this.animationTimer = setTimeout(() => {
						this.isAnimating = false
						this.$emit('opened')
						this.animationTimer = null
					}, this.duration) as unknown as number
				})
			} else {
				this.isAnimating = true
				this.$emit('close')

				this.animationTimer = setTimeout(() => {
					this.isAnimating = false
					this.$emit('closed')
					this.animationTimer = null
				}, this.duration) as unknown as number
			}
		},

		/**
		 * 处理遮罩点击事件
		 * @description 当用户点击遮罩层时触发，根据maskClosable属性决定是否关闭抽屉
		 * @public
		 */
		handleMaskClick(): void {
			if (!this.maskClosable || this.isAnimating) {
				return
			}
			this.closeDrawer()
		},

		/**
		 * 处理关闭按钮点击事件
		 * @description 当用户点击关闭按钮时触发
		 * @public
		 */
		handleCloseClick(): void {
			if (this.isAnimating) {
				return
			}
			this.closeDrawer()
		},

		/**
		 * 关闭抽屉
		 * @description 触发close事件，由父组件控制visible属性来关闭抽屉
		 * @public
		 */
		closeDrawer(): void {
			this.$emit('close')
		}
	},

	beforeUnmount() {
		if (this.animationTimer) {
			clearTimeout(this.animationTimer)
		}
	}
}
</script>

<style scoped>
/* 抽屉包装器 */
.tui-drawer-wrapper {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

/* 遮罩层 */
.tui-drawer-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
	pointer-events: auto;
}

.tui-drawer-mask_show {
	opacity: 1;
	visibility: visible;
}

/* 抽屉容器基础样式 */
.tui-drawer-container {
	position: fixed;
	box-sizing: border-box;
	overflow: hidden;
	transition: transform 0.3s ease-in-out;
	pointer-events: auto;
	/* 滚动优化 */
	-webkit-overflow-scrolling: touch;
	-ms-touch-action: pan-y cross-slide-y;
	-ms-scroll-chaining: none;
	-ms-scroll-limit: 0 50 0 50;
}

/* 左侧抽屉 */
.tui-drawer-container_left {
	left: 0;
	top: 0;
	bottom: 0;
	max-width: 100%;
	transform: translate3d(-100%, 0, 0);
	overflow-y: auto;
}

/* 右侧抽屉 */
.tui-drawer-container_right {
	right: 0;
	top: 0;
	bottom: 0;
	max-width: 100%;
	transform: translate3d(100%, 0, 0);
	overflow-y: auto;
}

/* 顶部抽屉 */
.tui-drawer-container_top {
	top: 0;
	left: 0;
	right: 0;
	max-height: 100%;
	transform: translate3d(0, -100%, 0);
	overflow-y: auto;
}

/* 底部抽屉 */
.tui-drawer-container_bottom {
	bottom: 0;
	left: 0;
	right: 0;
	max-height: 100%;
	transform: translate3d(0, 100%, 0);
	overflow-y: auto;
}

/* 显示状态 */
.tui-drawer-left__show,
.tui-drawer-right__show,
.tui-drawer-top__show,
.tui-drawer-bottom__show {
	transform: translate3d(0, 0, 0);
}

/* 关闭按钮 */
.tui-drawer-close {
	position: absolute;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.1);
	border-radius: 50%;
	cursor: pointer;
	z-index: 1;
	transition: background-color 0.2s ease;
}

.tui-drawer-close:hover {
	background-color: rgba(0, 0, 0, 0.2);
}

.tui-drawer-close:active {
	background-color: rgba(0, 0, 0, 0.3);
}

.tui-drawer-close-icon {
	font-size: 28rpx;
	color: #666;
	line-height: 1;
}

/* 关闭按钮位置 */
.tui-drawer-close_top-left {
	top: 20rpx;
	left: 20rpx;
}

.tui-drawer-close_top-right {
	top: 20rpx;
	right: 20rpx;
}

.tui-drawer-close_bottom-left {
	bottom: 20rpx;
	left: 20rpx;
}

.tui-drawer-close_bottom-right {
	bottom: 20rpx;
	right: 20rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.tui-drawer-container_left,
	.tui-drawer-container_right {
		max-width: 90%;
	}

	.tui-drawer-container_top,
	.tui-drawer-container_bottom {
		max-height: 90%;
	}
}

/* 小程序兼容性 */
/* #ifdef MP */
.tui-drawer-container {
	/* 小程序中使用 overflow: auto 可能有问题，改用 scroll */
	overflow: scroll;
}
/* #endif */

/* H5 兼容性 */
/* #ifdef H5 */
.tui-drawer-close {
	cursor: pointer;
}
/* #endif */

/* APP 兼容性 */
/* #ifdef APP-PLUS */
.tui-drawer-container {
	/* APP 中优化滚动性能 */
	-webkit-overflow-scrolling: touch;
}
/* #endif */
</style>
