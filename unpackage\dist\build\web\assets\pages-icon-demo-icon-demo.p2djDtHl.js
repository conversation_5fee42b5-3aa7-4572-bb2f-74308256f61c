import{y as e,D as l,o as a,c as s,w as t,b as o,t as c,l as n,j as i,e as u,z as d,A as r,a as f,B as m,k as p,r as _,F as b,x as g,G as y,i as v,C as h,f as z,S as k,s as x}from"./index-ChUEiI3E.js";import{n as C,C as I}from"./index.DI3nc_37.js";/* empty css                                                                 */import{I as w}from"./Icon.DLnR9vuk.js";import{_ as F}from"./_plugin-vue_export-helper.BCo6x5W8.js";const S=F(e({__name:"IconFont",props:{name:{default:""},fontFamily:{default:"iconfont"},prefix:{default:"icon-"},size:{default:24},color:{default:"#333333"},disabled:{type:Boolean,default:!1},spin:{type:Boolean,default:!1},pulse:{type:<PERSON><PERSON>an,default:!1},customClass:{default:""},customStyle:{default:()=>({})}},emits:["click"],setup(e,{emit:d}){const r=e,f=d,m=l((()=>[r.fontFamily,r.prefix+r.name,{"icon-disabled":r.disabled,"icon-spin":r.spin,"icon-pulse":r.pulse},r.customClass])),p=l((()=>({fontSize:"number"==typeof r.size?`${r.size}px`:r.size,color:r.color,fontFamily:r.fontFamily,cursor:r.disabled?"not-allowed":"pointer",opacity:r.disabled?.5:1,transition:"all 0.3s ease",userSelect:"none",display:"inline-block",lineHeight:1,...r.customStyle}))),_=l((()=>"")),b=e=>{r.disabled||f("click",e)};return(e,l)=>{const d=u;return a(),s(d,{class:n(["icon-font",m.value]),style:i(p.value),onClick:b},{default:t((()=>[o(c(_.value),1)])),_:1},8,["class","style"])}}}),[["__scopeId","data-v-22a4def6"]]),B={createIconFontConfig(e={}){const l={fontFamily:e.fontFamily||"iconfont",prefix:e.prefix||"icon-",cssUrl:e.cssUrl||""};return l.cssUrl&&this.loadIconFont(l.cssUrl),l},loadIconFont(e){if("undefined"!=typeof document){const l=document.createElement("link");l.rel="stylesheet",l.href=e,document.head.appendChild(l)}},getBuiltinIcons:()=>["home","user","search","heart","star","like","share","download","upload","delete","edit","add","minus","close","check","arrow-left","arrow-right","arrow-up","arrow-down","refresh","settings","menu","more","phone","message","mail","location","time","calendar","camera","image","video","music","file","folder","link","lock","unlock","eye","eye-off","warning","error","info","success"],validateIconName(e,l="builtin"){return!!e&&("builtin"!==l||this.getBuiltinIcons().includes(e))},getIconSizeInPx(e){if("number"==typeof e)return e;if("string"==typeof e){const l=e.match(/^(\d+(?:\.\d+)?)(.*)$/);if(l){const e=parseFloat(l[1]);switch(l[2]||"px"){case"px":default:return e;case"rpx":return e/2;case"rem":case"em":return 16*e}}}return 24},getRandomIcon(){const e=this.getBuiltinIcons();return e[Math.floor(Math.random()*e.length)]},createIconStyle(e={}){const l={};if(e.size){const a="number"==typeof e.size?`${e.size}px`:e.size;l.fontSize=a,l.width=a,l.height=a}return e.color&&(l.color=e.color),e.backgroundColor&&(l.backgroundColor=e.backgroundColor),e.borderRadius&&(l.borderRadius="number"==typeof e.borderRadius?`${e.borderRadius}px`:e.borderRadius),l},unicodeToChar:e=>e.startsWith("&#x")?String.fromCharCode(parseInt(e.slice(3,-1),16)):e.startsWith("&#")?String.fromCharCode(parseInt(e.slice(2,-1),10)):e.startsWith("\\u")?String.fromCharCode(parseInt(e.slice(2),16)):e,validateImageUrl:async e=>new Promise((l=>{if("undefined"!=typeof Image){const a=new Image;a.onload=()=>l(!0),a.onerror=()=>l(!1),a.src=e}else l(!0)})),getThemeColors(){const e=this.isDarkTheme();return{primary:e?"#ffffff":"#333333",secondary:e?"#cccccc":"#666666",disabled:e?"#666666":"#cccccc",background:e?"#2a2a2a":"#f8f9fa",border:e?"#444444":"#e9ecef"}},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches},j=F(e({__name:"icon-demo",setup(e){const F=d(0),j=d(24),R=d("#333333"),U=d(!0),M=d(!1),T=["#333333","#007aff","#4cd964","#ff3b30","#ff9500","#5ac8fa","#8e8e93","#ff2d92"],$=B.getBuiltinIcons(),W=l((()=>{const e=[];for(let l=0;l<100;l++)e.push(B.getRandomIcon());return e}));r((()=>{F.value=C.getTotalHeight()}));const D=e=>{j.value=e.detail.value},E=e=>{U.value=e.detail.value},A=()=>{console.log("图片加载成功")},H=()=>{console.log("图片加载失败")},N=()=>{M.value=!M.value};return(e,l)=>{const d=u,r=y,C=v,B=h,P=z,q=k;return a(),s(C,{class:"page"},{default:t((()=>[f(m(I),{title:"图标组件演示","show-back":!0}),f(q,{"scroll-y":"",class:"content",style:i({paddingTop:F.value+"px"})},{default:t((()=>[f(C,{class:"control-panel"},{default:t((()=>[f(d,{class:"panel-title"},{default:t((()=>[o("🎛️ 控制面板")])),_:1}),f(C,{class:"control-group"},{default:t((()=>[f(d,{class:"control-label"},{default:t((()=>[o("图标大小:")])),_:1}),f(r,{value:j.value,min:12,max:64,onChange:D,activeColor:"#007aff"},null,8,["value"]),f(d,{class:"control-value"},{default:t((()=>[o(c(j.value)+"px",1)])),_:1})])),_:1}),f(C,{class:"control-group"},{default:t((()=>[f(d,{class:"control-label"},{default:t((()=>[o("图标颜色:")])),_:1}),f(C,{class:"color-picker"},{default:t((()=>[(a(),p(b,null,_(T,(e=>f(C,{key:e,class:n(["color-item",{active:R.value===e}]),style:i({backgroundColor:e}),onClick:l=>R.value=e},null,8,["class","style","onClick"]))),64))])),_:1})])),_:1}),f(C,{class:"control-group"},{default:t((()=>[f(d,{class:"control-label"},{default:t((()=>[o("启用动画:")])),_:1}),f(B,{checked:U.value,onChange:E,color:"#007aff"},null,8,["checked"])])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("🏠 内置图标")])),_:1}),f(C,{class:"icon-grid"},{default:t((()=>[(a(!0),p(b,null,_(m($),(e=>(a(),s(C,{key:e,class:"icon-item",onClick:l=>(e=>{x({title:`点击了 ${e} 图标`,icon:"none"})})(e)},{default:t((()=>[f(m(w),{name:e,type:"builtin",size:j.value,color:R.value,spin:U.value&&"refresh"===e,pulse:U.value&&"heart"===e},null,8,["name","size","color","spin","pulse"]),f(d,{class:"icon-name"},{default:t((()=>[o(c(e),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("🔤 字体图标 (iconfont)")])),_:1}),f(C,{class:"font-icon-demo"},{default:t((()=>[f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("基础用法:")])),_:1}),f(m(S),{name:"home",size:j.value,color:R.value,"font-family":"iconfont",prefix:"icon-"},null,8,["size","color"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("自定义前缀:")])),_:1}),f(m(S),{name:"user",size:j.value,color:R.value,"font-family":"custom-font",prefix:"cf-"},null,8,["size","color"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("旋转动画:")])),_:1}),f(m(S),{name:"loading",size:j.value,color:R.value,spin:U.value},null,8,["size","color","spin"])])),_:1})])),_:1}),f(C,{class:"font-setup"},{default:t((()=>[f(d,{class:"setup-title"},{default:t((()=>[o("📝 使用说明")])),_:1}),f(d,{class:"setup-text"},{default:t((()=>[o(" 1. 在 iconfont.cn 创建项目并生成字体文件 2. 将 CSS 链接添加到项目中 3. 使用 IconFont 组件，设置正确的 name 和 prefix ")])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("🖼️ 图片图标")])),_:1}),f(C,{class:"image-icon-demo"},{default:t((()=>[f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("网络图片:")])),_:1}),f(m(w),{type:"image",src:"https://img.icons8.com/color/48/000000/vue-js.png",size:j.value,onLoad:A,onError:H},null,8,["size"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("本地图片:")])),_:1}),f(m(w),{type:"image",src:"/static/logo.png",size:j.value,"image-mode":"aspectFit"},null,8,["size"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("圆形图片:")])),_:1}),f(m(w),{type:"image",src:"https://img.icons8.com/color/48/000000/react-native.png",size:j.value,"border-radius":j.value/2},null,8,["size","border-radius"])])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("🔣 Unicode 图标")])),_:1}),f(C,{class:"unicode-demo"},{default:t((()=>[f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("Unicode 字符:")])),_:1}),f(m(w),{type:"unicode",unicode:"👍",size:j.value},null,8,["size"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("十六进制:")])),_:1}),f(m(w),{type:"unicode",unicode:"❤",size:j.value},null,8,["size"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("十进制:")])),_:1}),f(m(w),{type:"unicode",unicode:"★",size:j.value},null,8,["size"])])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("🏷️ 徽章图标")])),_:1}),f(C,{class:"badge-demo"},{default:t((()=>[f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("数字徽章:")])),_:1}),f(m(w),{name:"message",type:"builtin",size:j.value,color:R.value,badge:"5"},null,8,["size","color"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("99+ 徽章:")])),_:1}),f(m(w),{name:"mail",type:"builtin",size:j.value,color:R.value,badge:150},null,8,["size","color"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("红点徽章:")])),_:1}),f(m(w),{name:"user",type:"builtin",size:j.value,color:R.value,dot:!0},null,8,["size","color"])])),_:1}),f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("自定义徽章:")])),_:1}),f(m(w),{name:"heart",type:"builtin",size:j.value,color:R.value,badge:"NEW","badge-color":"#ff9500"},null,8,["size","color"])])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("🎨 组合使用")])),_:1}),f(C,{class:"combination-demo"},{default:t((()=>[f(C,{class:"demo-card"},{default:t((()=>[f(d,{class:"card-title"},{default:t((()=>[o("导航栏")])),_:1}),f(C,{class:"nav-bar"},{default:t((()=>[f(m(w),{name:"menu",type:"builtin",size:24,color:"#333"}),f(d,{class:"nav-title"},{default:t((()=>[o("首页")])),_:1}),f(C,{class:"nav-right"},{default:t((()=>[f(m(w),{name:"search",type:"builtin",size:24,color:"#333"}),f(m(w),{name:"message",type:"builtin",size:24,color:"#333",badge:"3"})])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-card"},{default:t((()=>[f(d,{class:"card-title"},{default:t((()=>[o("按钮组")])),_:1}),f(C,{class:"button-group"},{default:t((()=>[f(C,{class:"icon-button"},{default:t((()=>[f(m(w),{name:"heart",type:"builtin",size:20,color:"#ff3b30"}),f(d,{class:"button-text"},{default:t((()=>[o("喜欢")])),_:1})])),_:1}),f(C,{class:"icon-button"},{default:t((()=>[f(m(w),{name:"share",type:"builtin",size:20,color:"#007aff"}),f(d,{class:"button-text"},{default:t((()=>[o("分享")])),_:1})])),_:1}),f(C,{class:"icon-button"},{default:t((()=>[f(m(w),{name:"download",type:"builtin",size:20,color:"#4cd964"}),f(d,{class:"button-text"},{default:t((()=>[o("下载")])),_:1})])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-card"},{default:t((()=>[f(d,{class:"card-title"},{default:t((()=>[o("状态图标")])),_:1}),f(C,{class:"status-list"},{default:t((()=>[f(C,{class:"status-item"},{default:t((()=>[f(m(w),{name:"success",type:"builtin",size:16,color:"#4cd964"}),f(d,{class:"status-text"},{default:t((()=>[o("操作成功")])),_:1})])),_:1}),f(C,{class:"status-item"},{default:t((()=>[f(m(w),{name:"warning",type:"builtin",size:16,color:"#ff9500"}),f(d,{class:"status-text"},{default:t((()=>[o("警告信息")])),_:1})])),_:1}),f(C,{class:"status-item"},{default:t((()=>[f(m(w),{name:"error",type:"builtin",size:16,color:"#ff3b30"}),f(d,{class:"status-text"},{default:t((()=>[o("错误信息")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),f(C,{class:"demo-section"},{default:t((()=>[f(d,{class:"section-title"},{default:t((()=>[o("⚡ 性能测试")])),_:1}),f(C,{class:"performance-demo"},{default:t((()=>[f(C,{class:"demo-item"},{default:t((()=>[f(d,{class:"demo-label"},{default:t((()=>[o("大量图标渲染:")])),_:1}),f(P,{onClick:N,class:"test-btn"},{default:t((()=>[o(c(M.value?"隐藏":"显示")+" 100个图标 ",1)])),_:1})])),_:1}),M.value?(a(),s(C,{key:0,class:"mass-icons"},{default:t((()=>[(a(!0),p(b,null,_(W.value,((e,l)=>(a(),s(m(w),{key:l,name:e,type:"builtin",size:16,color:T[Math.floor(Math.random()*T.length)],spin:U.value&&"refresh"===e},null,8,["name","color","spin"])))),128))])),_:1})):g("",!0)])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}}}),[["__scopeId","data-v-0bcae82c"]]);export{j as default};
