import{y as l,z as e,A as a,o as s,c as t,w as c,a as o,B as u,b as d,j as i,k as n,r as f,F as _,s as m,e as v,f as r,i as b,S as p,Q as h,R as k,I as C,T as g,d as U,t as x}from"./index-ChUEiI3E.js";import{_ as w}from"./product.q7Ixu4ET.js";import{n as V,C as j}from"./index.DI3nc_37.js";import"./index._qKjp6eZ.js";import{B as y}from"./BottomPopup.gFUAg0m5.js";import{_ as Q}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=Q(l({__name:"bottom-popup-demo",setup(l){const Q=e(0),B=e(!1),q=e(!1),F=e(!1),T=e(!1),$=e(!1),I=e(!1),z=e(!1),A=e(!1),H=e(!1),L=e(!1),O=e(!1),P=e(!1),R=e(!1),S=e(!1),D=e(!1),E=e(!1),G=e(!1),J=e(!1),K=e(!1),M=e(!1),N=e(!1),W=e(["北京","上海","广州","深圳","杭州","南京","成都","重庆"]),X=e([0]),Y=e({name:"",phone:"",email:"",remark:""});a((()=>{Q.value=V.getTotalHeight()}));const Z=()=>{B.value=!0},ll=()=>{q.value=!0},el=()=>{F.value=!0},al=()=>{T.value=!0},sl=()=>{$.value=!0},tl=()=>{I.value=!0},cl=()=>{z.value=!0},ol=()=>{A.value=!0},ul=()=>{H.value=!0},dl=()=>{L.value=!0},il=()=>{O.value=!0},nl=()=>{P.value=!0},fl=()=>{R.value=!0},_l=()=>{S.value=!0},ml=()=>{D.value=!0},vl=()=>{E.value=!0},rl=()=>{G.value=!0},bl=()=>{J.value=!0},pl=()=>{K.value=!0},hl=()=>{M.value=!0},kl=()=>{N.value=!0},Cl=()=>{console.log("弹窗打开")},gl=()=>{console.log("弹窗关闭")},Ul=()=>{m({title:"已取消",icon:"none"})},xl=()=>{m({title:"已确认",icon:"success"})},wl=l=>{E.value=!1,m({title:`选择了: ${l}`,icon:"none"})},Vl=l=>{X.value=l.detail.value},jl=()=>{const l=W.value[X.value[0]];m({title:`选择了: ${l}`,icon:"success"})},yl=l=>{J.value=!1,m({title:`分享到: ${l}`,icon:"none"})},Ql=()=>{Y.value.name?(m({title:"提交成功",icon:"success"}),Y.value={name:"",phone:"",email:"",remark:""}):m({title:"请输入姓名",icon:"none"})},Bl=()=>{m({title:"购买成功",icon:"success"})};return(l,e)=>{const a=v,m=r,V=b,ql=p,Fl=h,Tl=k,$l=C,Il=g,zl=U;return s(),t(V,{class:"page"},{default:c((()=>[o(u(j),{title:"底部弹窗组件演示","show-back":!0}),o(ql,{"scroll-y":"",class:"content",style:i({paddingTop:Q.value+"px"})},{default:c((()=>[o(V,{class:"demo-section"},{default:c((()=>[o(a,{class:"section-title"},{default:c((()=>[d("📝 基础用法")])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("基础弹窗:")])),_:1}),o(m,{onClick:Z,class:"demo-btn"},{default:c((()=>[d("显示基础弹窗")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("带标题弹窗:")])),_:1}),o(m,{onClick:ll,class:"demo-btn"},{default:c((()=>[d("显示带标题弹窗")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("带关闭按钮:")])),_:1}),o(m,{onClick:el,class:"demo-btn"},{default:c((()=>[d("显示带关闭按钮")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("带底部按钮:")])),_:1}),o(m,{onClick:al,class:"demo-btn"},{default:c((()=>[d("显示带底部按钮")])),_:1})])),_:1})])),_:1}),o(V,{class:"demo-section"},{default:c((()=>[o(a,{class:"section-title"},{default:c((()=>[d("📏 尺寸控制")])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("小尺寸 (30%):")])),_:1}),o(m,{onClick:sl,class:"demo-btn"},{default:c((()=>[d("显示小弹窗")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("中等尺寸 (50%):")])),_:1}),o(m,{onClick:tl,class:"demo-btn"},{default:c((()=>[d("显示中等弹窗")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("大尺寸 (80%):")])),_:1}),o(m,{onClick:cl,class:"demo-btn"},{default:c((()=>[d("显示大弹窗")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("自适应高度:")])),_:1}),o(m,{onClick:ol,class:"demo-btn"},{default:c((()=>[d("显示自适应弹窗")])),_:1})])),_:1})])),_:1}),o(V,{class:"demo-section"},{default:c((()=>[o(a,{class:"section-title"},{default:c((()=>[d("🎨 样式定制")])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("自定义颜色:")])),_:1}),o(m,{onClick:ul,class:"demo-btn"},{default:c((()=>[d("显示自定义颜色")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("自定义圆角:")])),_:1}),o(m,{onClick:dl,class:"demo-btn"},{default:c((()=>[d("显示自定义圆角")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("暗色主题:")])),_:1}),o(m,{onClick:il,class:"demo-btn"},{default:c((()=>[d("显示暗色主题")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("无拖拽指示器:")])),_:1}),o(m,{onClick:nl,class:"demo-btn"},{default:c((()=>[d("显示无指示器")])),_:1})])),_:1})])),_:1}),o(V,{class:"demo-section"},{default:c((()=>[o(a,{class:"section-title"},{default:c((()=>[d("🎛️ 交互控制")])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("禁止遮罩关闭:")])),_:1}),o(m,{onClick:fl,class:"demo-btn"},{default:c((()=>[d("显示禁止遮罩关闭")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("自定义动画时长:")])),_:1}),o(m,{onClick:_l,class:"demo-btn"},{default:c((()=>[d("显示慢动画")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("自定义遮罩:")])),_:1}),o(m,{onClick:ml,class:"demo-btn"},{default:c((()=>[d("显示自定义遮罩")])),_:1})])),_:1})])),_:1}),o(V,{class:"demo-section"},{default:c((()=>[o(a,{class:"section-title"},{default:c((()=>[d("🛠️ 应用场景")])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("操作菜单:")])),_:1}),o(m,{onClick:vl,class:"demo-btn"},{default:c((()=>[d("显示操作菜单")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("选择器:")])),_:1}),o(m,{onClick:rl,class:"demo-btn"},{default:c((()=>[d("显示选择器")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("分享面板:")])),_:1}),o(m,{onClick:bl,class:"demo-btn"},{default:c((()=>[d("显示分享面板")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("表单输入:")])),_:1}),o(m,{onClick:pl,class:"demo-btn"},{default:c((()=>[d("显示表单")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("商品详情:")])),_:1}),o(m,{onClick:hl,class:"demo-btn"},{default:c((()=>[d("显示商品详情")])),_:1})])),_:1}),o(V,{class:"demo-card"},{default:c((()=>[o(a,{class:"demo-label"},{default:c((()=>[d("用户信息:")])),_:1}),o(m,{onClick:kl,class:"demo-btn"},{default:c((()=>[d("显示用户信息")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]),o(u(y),{visible:B.value,"onUpdate:visible":e[0]||(e[0]=l=>B.value=l),content:"这是一个基础的底部弹窗",onOpen:Cl,onClose:gl},null,8,["visible"]),o(u(y),{visible:q.value,"onUpdate:visible":e[1]||(e[1]=l=>q.value=l),title:"弹窗标题",content:"这是一个带标题的底部弹窗，可以显示更多信息。"},null,8,["visible"]),o(u(y),{visible:F.value,"onUpdate:visible":e[2]||(e[2]=l=>F.value=l),title:"带关闭按钮",content:"这个弹窗右上角有关闭按钮","show-close":!0},null,8,["visible"]),o(u(y),{visible:T.value,"onUpdate:visible":e[3]||(e[3]=l=>T.value=l),title:"确认操作",content:"确定要执行此操作吗？","show-footer":!0,onCancel:Ul,onConfirm:xl},null,8,["visible"]),o(u(y),{visible:$.value,"onUpdate:visible":e[4]||(e[4]=l=>$.value=l),title:"小弹窗",content:"这是一个小尺寸的弹窗",height:"30%"},null,8,["visible"]),o(u(y),{visible:I.value,"onUpdate:visible":e[5]||(e[5]=l=>I.value=l),title:"中等弹窗",content:"这是一个中等尺寸的弹窗，高度为屏幕的50%",height:"50%"},null,8,["visible"]),o(u(y),{visible:z.value,"onUpdate:visible":e[6]||(e[6]=l=>z.value=l),title:"大弹窗",height:"80%","show-close":!0},{default:c((()=>[o(V,{class:"large-content"},{default:c((()=>[o(a,{class:"content-title"},{default:c((()=>[d("大尺寸弹窗内容")])),_:1}),o(a,{class:"content-text"},{default:c((()=>[d("这是一个大尺寸的弹窗，可以容纳更多内容。")])),_:1}),o(V,{class:"content-list"},{default:c((()=>[(s(),n(_,null,f(20,(l=>o(V,{key:l,class:"list-item"},{default:c((()=>[o(a,null,{default:c((()=>[d("列表项 "+x(l),1)])),_:2},1024)])),_:2},1024))),64))])),_:1})])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:A.value,"onUpdate:visible":e[7]||(e[7]=l=>A.value=l),title:"自适应高度",height:"auto","max-height":"60%"},{default:c((()=>[o(V,{class:"auto-content"},{default:c((()=>[o(a,{class:"content-title"},{default:c((()=>[d("自适应高度内容")])),_:1}),o(a,{class:"content-text"},{default:c((()=>[d("这个弹窗会根据内容自动调整高度。")])),_:1}),o(V,{class:"feature-list"},{default:c((()=>[o(V,{class:"feature-item"},{default:c((()=>[d("✅ 自动高度调整")])),_:1}),o(V,{class:"feature-item"},{default:c((()=>[d("✅ 最大高度限制")])),_:1}),o(V,{class:"feature-item"},{default:c((()=>[d("✅ 内容滚动支持")])),_:1})])),_:1})])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:H.value,"onUpdate:visible":e[8]||(e[8]=l=>H.value=l),title:"自定义颜色",content:"这是一个自定义颜色的弹窗","background-color":"#007aff","title-color":"#ffffff","content-color":"#ffffff","handle-color":"#ffffff"},null,8,["visible"]),o(u(y),{visible:L.value,"onUpdate:visible":e[9]||(e[9]=l=>L.value=l),title:"自定义圆角",content:"这个弹窗有自定义的圆角样式","border-radius":"0"},null,8,["visible"]),o(u(y),{visible:O.value,"onUpdate:visible":e[10]||(e[10]=l=>O.value=l),title:"暗色主题",content:"这是一个暗色主题的弹窗","background-color":"#2a2a2a","title-color":"#ffffff","content-color":"#cccccc","handle-color":"#555555"},null,8,["visible"]),o(u(y),{visible:P.value,"onUpdate:visible":e[11]||(e[11]=l=>P.value=l),title:"无拖拽指示器",content:"这个弹窗没有拖拽指示器","show-handle":!1,"show-close":!0},null,8,["visible"]),o(u(y),{visible:R.value,"onUpdate:visible":e[12]||(e[12]=l=>R.value=l),title:"禁止遮罩关闭",content:"点击遮罩无法关闭此弹窗，只能通过按钮关闭","mask-closable":!1,"show-footer":!0},null,8,["visible"]),o(u(y),{visible:S.value,"onUpdate:visible":e[13]||(e[13]=l=>S.value=l),title:"慢动画",content:"这个弹窗有较慢的动画效果","animation-duration":800},null,8,["visible"]),o(u(y),{visible:D.value,"onUpdate:visible":e[14]||(e[14]=l=>D.value=l),title:"自定义遮罩",content:"这个弹窗有自定义的遮罩样式","mask-color":"#ff0000","mask-opacity":.3},null,8,["visible"]),o(u(y),{visible:E.value,"onUpdate:visible":e[19]||(e[19]=l=>E.value=l),height:"auto","max-height":"50%","content-padding":"0"},{default:c((()=>[o(V,{class:"action-sheet"},{default:c((()=>[o(V,{class:"action-item",onClick:e[15]||(e[15]=l=>wl("拍照"))},{default:c((()=>[o(a,{class:"action-icon"},{default:c((()=>[d("📷")])),_:1}),o(a,{class:"action-text"},{default:c((()=>[d("拍照")])),_:1})])),_:1}),o(V,{class:"action-item",onClick:e[16]||(e[16]=l=>wl("从相册选择"))},{default:c((()=>[o(a,{class:"action-icon"},{default:c((()=>[d("🖼️")])),_:1}),o(a,{class:"action-text"},{default:c((()=>[d("从相册选择")])),_:1})])),_:1}),o(V,{class:"action-item",onClick:e[17]||(e[17]=l=>wl("文件"))},{default:c((()=>[o(a,{class:"action-icon"},{default:c((()=>[d("📁")])),_:1}),o(a,{class:"action-text"},{default:c((()=>[d("选择文件")])),_:1})])),_:1}),o(V,{class:"action-divider"}),o(V,{class:"action-item cancel",onClick:e[18]||(e[18]=l=>E.value=!1)},{default:c((()=>[o(a,{class:"action-text"},{default:c((()=>[d("取消")])),_:1})])),_:1})])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:G.value,"onUpdate:visible":e[20]||(e[20]=l=>G.value=l),title:"选择城市",height:"50%","show-close":!0,"show-footer":!0,onConfirm:jl},{default:c((()=>[o(V,{class:"picker-content"},{default:c((()=>[o(Tl,{class:"picker-view",value:X.value,onChange:Vl},{default:c((()=>[o(Fl,null,{default:c((()=>[(s(!0),n(_,null,f(W.value,((l,e)=>(s(),t(V,{key:e,class:"picker-item"},{default:c((()=>[d(x(l),1)])),_:2},1024)))),128))])),_:1})])),_:1},8,["value"])])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:J.value,"onUpdate:visible":e[27]||(e[27]=l=>J.value=l),title:"分享到",height:"auto","max-height":"50%","show-close":!0,"content-padding":"20px 20px 40px"},{default:c((()=>[o(V,{class:"share-panel"},{default:c((()=>[o(V,{class:"share-apps"},{default:c((()=>[o(V,{class:"share-app",onClick:e[21]||(e[21]=l=>yl("微信"))},{default:c((()=>[o(V,{class:"app-icon wechat"},{default:c((()=>[d("💬")])),_:1}),o(a,{class:"app-name"},{default:c((()=>[d("微信")])),_:1})])),_:1}),o(V,{class:"share-app",onClick:e[22]||(e[22]=l=>yl("朋友圈"))},{default:c((()=>[o(V,{class:"app-icon moments"},{default:c((()=>[d("🌟")])),_:1}),o(a,{class:"app-name"},{default:c((()=>[d("朋友圈")])),_:1})])),_:1}),o(V,{class:"share-app",onClick:e[23]||(e[23]=l=>yl("QQ"))},{default:c((()=>[o(V,{class:"app-icon qq"},{default:c((()=>[d("🐧")])),_:1}),o(a,{class:"app-name"},{default:c((()=>[d("QQ")])),_:1})])),_:1}),o(V,{class:"share-app",onClick:e[24]||(e[24]=l=>yl("微博"))},{default:c((()=>[o(V,{class:"app-icon weibo"},{default:c((()=>[d("📱")])),_:1}),o(a,{class:"app-name"},{default:c((()=>[d("微博")])),_:1})])),_:1})])),_:1}),o(V,{class:"share-actions"},{default:c((()=>[o(V,{class:"share-action",onClick:e[25]||(e[25]=l=>yl("复制链接"))},{default:c((()=>[o(V,{class:"action-icon"},{default:c((()=>[d("🔗")])),_:1}),o(a,{class:"action-name"},{default:c((()=>[d("复制链接")])),_:1})])),_:1}),o(V,{class:"share-action",onClick:e[26]||(e[26]=l=>yl("保存图片"))},{default:c((()=>[o(V,{class:"action-icon"},{default:c((()=>[d("💾")])),_:1}),o(a,{class:"action-name"},{default:c((()=>[d("保存图片")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:K.value,"onUpdate:visible":e[32]||(e[32]=l=>K.value=l),title:"编辑信息",height:"auto","max-height":"80%","show-close":!0,"show-footer":!0,onConfirm:Ql},{default:c((()=>[o(V,{class:"form-content"},{default:c((()=>[o(V,{class:"form-item"},{default:c((()=>[o(a,{class:"form-label"},{default:c((()=>[d("姓名:")])),_:1}),o($l,{modelValue:Y.value.name,"onUpdate:modelValue":e[28]||(e[28]=l=>Y.value.name=l),class:"form-input",placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),o(V,{class:"form-item"},{default:c((()=>[o(a,{class:"form-label"},{default:c((()=>[d("手机号:")])),_:1}),o($l,{modelValue:Y.value.phone,"onUpdate:modelValue":e[29]||(e[29]=l=>Y.value.phone=l),class:"form-input",placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),o(V,{class:"form-item"},{default:c((()=>[o(a,{class:"form-label"},{default:c((()=>[d("邮箱:")])),_:1}),o($l,{modelValue:Y.value.email,"onUpdate:modelValue":e[30]||(e[30]=l=>Y.value.email=l),class:"form-input",placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),o(V,{class:"form-item"},{default:c((()=>[o(a,{class:"form-label"},{default:c((()=>[d("备注:")])),_:1}),o(Il,{modelValue:Y.value.remark,"onUpdate:modelValue":e[31]||(e[31]=l=>Y.value.remark=l),class:"form-textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:M.value,"onUpdate:visible":e[33]||(e[33]=l=>M.value=l),title:"商品详情",height:"80%","show-close":!0,"show-footer":!0,"confirm-text":"立即购买",onConfirm:Bl},{default:c((()=>[o(V,{class:"product-detail"},{default:c((()=>[o(V,{class:"product-images"},{default:c((()=>[o(zl,{class:"product-image",src:w,mode:"aspectFill"})])),_:1}),o(V,{class:"product-info"},{default:c((()=>[o(a,{class:"product-name"},{default:c((()=>[d("精选商品名称")])),_:1}),o(V,{class:"product-price"},{default:c((()=>[o(a,{class:"current-price"},{default:c((()=>[d("¥299")])),_:1}),o(a,{class:"original-price"},{default:c((()=>[d("¥599")])),_:1})])),_:1}),o(V,{class:"product-tags"},{default:c((()=>[o(a,{class:"tag"},{default:c((()=>[d("限时特价")])),_:1}),o(a,{class:"tag"},{default:c((()=>[d("包邮")])),_:1})])),_:1})])),_:1}),o(V,{class:"product-description"},{default:c((()=>[o(a,{class:"desc-title"},{default:c((()=>[d("商品描述")])),_:1}),o(a,{class:"desc-content"},{default:c((()=>[d("这是一款精心挑选的优质商品，具有优良的品质和实用的功能。适合日常使用，性价比极高。")])),_:1})])),_:1}),o(V,{class:"product-specs"},{default:c((()=>[o(a,{class:"specs-title"},{default:c((()=>[d("规格参数")])),_:1}),o(V,{class:"spec-item"},{default:c((()=>[o(a,{class:"spec-label"},{default:c((()=>[d("颜色:")])),_:1}),o(a,{class:"spec-value"},{default:c((()=>[d("经典黑")])),_:1})])),_:1}),o(V,{class:"spec-item"},{default:c((()=>[o(a,{class:"spec-label"},{default:c((()=>[d("尺寸:")])),_:1}),o(a,{class:"spec-value"},{default:c((()=>[d("标准版")])),_:1})])),_:1}),o(V,{class:"spec-item"},{default:c((()=>[o(a,{class:"spec-label"},{default:c((()=>[d("材质:")])),_:1}),o(a,{class:"spec-value"},{default:c((()=>[d("优质材料")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["visible"]),o(u(y),{visible:N.value,"onUpdate:visible":e[34]||(e[34]=l=>N.value=l),title:"用户信息",height:"70%","show-close":!0},{default:c((()=>[o(V,{class:"user-profile"},{default:c((()=>[o(V,{class:"user-header"},{default:c((()=>[o(zl,{class:"user-avatar",src:"/static/avatar.jpg",mode:"aspectFill"}),o(V,{class:"user-basic"},{default:c((()=>[o(a,{class:"user-name"},{default:c((()=>[d("张三")])),_:1}),o(a,{class:"user-title"},{default:c((()=>[d("高级用户")])),_:1})])),_:1}),o(V,{class:"user-level"},{default:c((()=>[o(a,{class:"level-text"},{default:c((()=>[d("Lv.5")])),_:1})])),_:1})])),_:1}),o(V,{class:"user-stats"},{default:c((()=>[o(V,{class:"stat-item"},{default:c((()=>[o(a,{class:"stat-number"},{default:c((()=>[d("128")])),_:1}),o(a,{class:"stat-label"},{default:c((()=>[d("关注")])),_:1})])),_:1}),o(V,{class:"stat-item"},{default:c((()=>[o(a,{class:"stat-number"},{default:c((()=>[d("256")])),_:1}),o(a,{class:"stat-label"},{default:c((()=>[d("粉丝")])),_:1})])),_:1}),o(V,{class:"stat-item"},{default:c((()=>[o(a,{class:"stat-number"},{default:c((()=>[d("89")])),_:1}),o(a,{class:"stat-label"},{default:c((()=>[d("获赞")])),_:1})])),_:1})])),_:1}),o(V,{class:"user-actions"},{default:c((()=>[o(m,{class:"action-btn primary"},{default:c((()=>[d("关注")])),_:1}),o(m,{class:"action-btn"},{default:c((()=>[d("私信")])),_:1})])),_:1}),o(V,{class:"user-info"},{default:c((()=>[o(V,{class:"info-item"},{default:c((()=>[o(a,{class:"info-label"},{default:c((()=>[d("个人简介:")])),_:1}),o(a,{class:"info-value"},{default:c((()=>[d("这是一个简短的个人介绍")])),_:1})])),_:1}),o(V,{class:"info-item"},{default:c((()=>[o(a,{class:"info-label"},{default:c((()=>[d("注册时间:")])),_:1}),o(a,{class:"info-value"},{default:c((()=>[d("2023-01-01")])),_:1})])),_:1}),o(V,{class:"info-item"},{default:c((()=>[o(a,{class:"info-label"},{default:c((()=>[d("最后活跃:")])),_:1}),o(a,{class:"info-value"},{default:c((()=>[d("2小时前")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["visible"])])),_:1})}}}),[["__scopeId","data-v-5d45b240"]]);export{B as default};
