import{m as e,n as t,h as s,g as a,c as n,w as l,i as r,o as i,a as c,b as o,f as d,e as p,S as u}from"./index-ChUEiI3E.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";const T=y({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/modules"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/practical-cases"})},markAsCompleted(){try{let e=s("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("advanced-types")||(e.completedItems.push("advanced-types"),e.lastUpdate=Date.now(),a("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("高级类型课程加载完成")}},[["render",function(e,t,s,a,y,T){const f=d,m=p,x=r,_=u;return i(),n(x,{class:"container"},{default:l((()=>[c(x,{class:"header"},{default:l((()=>[c(f,{onClick:T.goBack,class:"back-btn"},{default:l((()=>[o("← 返回")])),_:1},8,["onClick"]),c(m,{class:"title"},{default:l((()=>[o("高级类型")])),_:1}),c(x,{class:"progress-indicator"},{default:l((()=>[o("7/9")])),_:1})])),_:1}),c(_,{"scroll-y":"",class:"content"},{default:l((()=>[c(x,{class:"lesson-intro"},{default:l((()=>[c(m,{class:"intro-title"},{default:l((()=>[o("学习目标")])),_:1}),c(m,{class:"intro-text"},{default:l((()=>[o("掌握 TypeScript 的高级类型特性，包括条件类型、映射类型、工具类型等")])),_:1})])),_:1}),c(x,{class:"section"},{default:l((()=>[c(m,{class:"section-title"},{default:l((()=>[o("❓ 条件类型 (Conditional Types)")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o("// 基本条件类型语法：T extends U ? X : Y type IsString<T> = T extends string ? true : false type Test1 = IsString<string> // true type Test2 = IsString<number> // false // 实用的条件类型 type NonNullable<T> = T extends null | undefined ? never : T type Example1 = NonNullable<string | null> // string type Example2 = NonNullable<number | undefined> // number // 分布式条件类型 type ToArray<T> = T extends any ? T[] : never type StrArrOrNumArr = ToArray<string | number> // string[] | number[] // 推断类型 (infer) type ReturnType<T> = T extends (...args: any[]) => infer R ? R : never type FuncReturn = ReturnType<() => string> // string type ArrayElement<T> = T extends (infer U)[] ? U : never type StringElement = ArrayElement<string[]> // string")])),_:1})])),_:1}),c(x,{class:"explanation"},{default:l((()=>[c(m,{class:"exp-text"},{default:l((()=>[o("• 条件类型根据类型关系选择不同的类型")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• infer 关键字用于推断类型")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 支持分布式条件类型")])),_:1})])),_:1})])),_:1}),c(x,{class:"section"},{default:l((()=>[c(m,{class:"section-title"},{default:l((()=>[o("🗺️ 映射类型 (Mapped Types)")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o("// 基本映射类型 type Readonly<T> = { readonly [P in keyof T]: T[P] } type Partial<T> = { [P in keyof T]?: T[P] } // 使用示例 interface User { id: number name: string email: string } type ReadonlyUser = Readonly<User> type PartialUser = Partial<User> // 高级映射类型 type Getters<T> = { [K in keyof T as `get${Capitalize<string & K>}`]: () => T[K] } type UserGetters = Getters<User> // 结果：{ // getId: () => number // getName: () => string // getEmail: () => string // } // 过滤属性 type PickByType<T, U> = { [K in keyof T as T[K] extends U ? K : never]: T[K] } type StringProps = PickByType<User, string> // { name: string, email: string }")])),_:1})])),_:1}),c(x,{class:"explanation"},{default:l((()=>[c(m,{class:"exp-text"},{default:l((()=>[o("• 映射类型基于现有类型创建新类型")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 可以修改属性的修饰符和名称")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 支持条件过滤和转换")])),_:1})])),_:1})])),_:1}),c(x,{class:"section"},{default:l((()=>[c(m,{class:"section-title"},{default:l((()=>[o("📝 模板字面量类型")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o('// 模板字面量类型 type EventName<T extends string> = `on${Capitalize<T>}` type ClickEvent = EventName<"click"> // "onClick" // 组合模板类型 type HttpMethod = "GET" | "POST" | "PUT" | "DELETE" type ApiEndpoint = `/${string}` type ApiCall = `${HttpMethod} ${ApiEndpoint}` type Examples = ApiCall // "GET /${string}" | "POST /${string}" | "PUT /${string}" | "DELETE /${string}" // 实用的模板类型 type CSSProperty = | "color" | "background-color" | "font-size" | "margin" | "padding" type CSSInJS = { [K in CSSProperty as `${K}Property`]: string } // 路径参数提取 type ExtractParams<T extends string> = T extends `${string}:${infer Param}/${infer Rest}` ? Param | ExtractParams<Rest> : T extends `${string}:${infer Param}` ? Param : never type Params = ExtractParams<"/users/:id/posts/:postId"> // "id" | "postId"')])),_:1})])),_:1}),c(x,{class:"explanation"},{default:l((()=>[c(m,{class:"exp-text"},{default:l((()=>[o("• 模板字面量类型用于字符串操作")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 支持字符串模式匹配和提取")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 常用于 API 路由和事件处理")])),_:1})])),_:1})])),_:1}),c(x,{class:"section"},{default:l((()=>[c(m,{class:"section-title"},{default:l((()=>[o("🛠️ 内置工具类型")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o('interface User { id: number name: string email: string age: number isActive: boolean } // Partial<T> - 所有属性可选 type PartialUser = Partial<User> // Required<T> - 所有属性必需 type RequiredUser = Required<PartialUser> // Pick<T, K> - 选择属性 type UserBasic = Pick<User, "id" | "name"> // Omit<T, K> - 排除属性 type UserWithoutId = Omit<User, "id"> // Record<K, T> - 创建记录类型 type UserRoles = Record<"admin" | "user" | "guest", boolean> // Exclude<T, U> - 排除联合类型 type NonStringTypes = Exclude<string | number | boolean, string> // number | boolean // Extract<T, U> - 提取联合类型 type StringTypes = Extract<string | number | boolean, string> // string // ReturnType<T> - 函数返回类型 type FuncReturn = ReturnType<() => User> // User // Parameters<T> - 函数参数类型 type FuncParams = Parameters<(id: number, name: string) => void> // [number, string]')])),_:1})])),_:1}),c(x,{class:"explanation"},{default:l((()=>[c(m,{class:"exp-text"},{default:l((()=>[o("• TypeScript 提供丰富的内置工具类型")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 简化常见的类型操作")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 提高代码的可读性和维护性")])),_:1})])),_:1})])),_:1}),c(x,{class:"section"},{default:l((()=>[c(m,{class:"section-title"},{default:l((()=>[o("🔧 自定义工具类型")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o("// 深度只读 type DeepReadonly<T> = { readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P] } // 深度可选 type DeepPartial<T> = { [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P] } // 非空类型 type NonNullable<T> = T extends null | undefined ? never : T // 函数属性 type FunctionPropertyNames<T> = { [K in keyof T]: T[K] extends Function ? K : never }[keyof T] type FunctionProperties<T> = Pick<T, FunctionPropertyNames<T>> // 非函数属性 type NonFunctionPropertyNames<T> = { [K in keyof T]: T[K] extends Function ? never : K }[keyof T] type NonFunctionProperties<T> = Pick<T, NonFunctionPropertyNames<T>> // 可选属性名 type OptionalPropertyNames<T> = { [K in keyof T]-?: {} extends Pick<T, K> ? K : never }[keyof T] // 必需属性名 type RequiredPropertyNames<T> = { [K in keyof T]-?: {} extends Pick<T, K> ? never : K }[keyof T]")])),_:1})])),_:1}),c(x,{class:"explanation"},{default:l((()=>[c(m,{class:"exp-text"},{default:l((()=>[o("• 可以创建自定义的工具类型")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 组合基础类型操作实现复杂功能")])),_:1}),c(m,{class:"exp-text"},{default:l((()=>[o("• 提高类型系统的表达能力")])),_:1})])),_:1})])),_:1}),c(x,{class:"practice-section"},{default:l((()=>[c(m,{class:"section-title"},{default:l((()=>[o("🎯 实践练习")])),_:1}),c(x,{class:"practice-item"},{default:l((()=>[c(m,{class:"practice-title"},{default:l((()=>[o("练习 1：创建条件类型")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o("// 创建一个条件类型 IsArray<T> // 如果 T 是数组类型返回 true，否则返回 false")])),_:1})])),_:1})])),_:1}),c(x,{class:"practice-item"},{default:l((()=>[c(m,{class:"practice-title"},{default:l((()=>[o("练习 2：映射类型应用")])),_:1}),c(x,{class:"code-block"},{default:l((()=>[c(m,{class:"code"},{default:l((()=>[o("// 创建一个映射类型 Setters<T> // 为对象的每个属性生成对应的 setter 方法")])),_:1})])),_:1})])),_:1})])),_:1}),c(x,{class:"navigation"},{default:l((()=>[c(f,{onClick:T.previousLesson,class:"nav-btn secondary"},{default:l((()=>[o("上一课：模块")])),_:1},8,["onClick"]),c(f,{onClick:T.nextLesson,class:"nav-btn primary"},{default:l((()=>[o("下一课：实战案例")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-d20b8d71"]]);export{T as default};
