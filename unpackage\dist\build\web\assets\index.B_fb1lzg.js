import{y as e,D as t,o as a,c as l,w as s,l as o,j as i,x as r,k as n,F as d,r as u,E as h,a as c,i as p}from"./index-ChUEiI3E.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const f="/static/avatar.png",k=g(e({__name:"Skeleton",props:{loading:{type:Boolean,default:!0},animated:{type:Boolean,default:!0},avatar:{type:Boolean,default:!1},avatarSize:{default:"default"},avatarShape:{default:"circle"},title:{type:Boolean,default:!1},titleWidth:{default:"40%"},paragraph:{type:Boolean,default:!1},paragraphRows:{default:3},paragraphWidths:{default:()=>["100%","95%","80%"]},template:{},listRows:{default:3},tableRows:{default:3},backgroundColor:{default:"#f2f2f2"},highlightColor:{default:"#e6e6e6"},borderRadius:{default:4},height:{},animationDuration:{default:1.5},animationDelay:{default:0}},setup(e){const g=e,f=t((()=>[`skeleton-avatar-${g.avatarSize}`,`skeleton-avatar-${g.avatarShape}`])),k=t((()=>({backgroundColor:g.backgroundColor,borderRadius:"circle"===g.avatarShape?"50%":`${g.borderRadius}px`,animationDuration:`${g.animationDuration}s`,animationDelay:`${g.animationDelay}s`}))),m=t((()=>({width:"number"==typeof g.titleWidth?`${g.titleWidth}px`:g.titleWidth,backgroundColor:g.backgroundColor,borderRadius:`${g.borderRadius}px`,animationDuration:`${g.animationDuration}s`,animationDelay:`${g.animationDelay}s`}))),y=t((()=>{const e=[];for(let t=0;t<g.paragraphRows;t++)e.push(t);return e})),x=e=>{const t=g.paragraphWidths[e]||g.paragraphWidths[g.paragraphWidths.length-1]||"100%";return{width:"number"==typeof t?`${t}px`:t,backgroundColor:g.backgroundColor,borderRadius:`${g.borderRadius}px`,animationDuration:`${g.animationDuration}s`,animationDelay:`${g.animationDelay+.1*e}s`}};return(e,t)=>{const g=p;return a(),l(g,{class:o(["skeleton",{"skeleton-loading":e.loading}])},{default:s((()=>[e.avatar?(a(),l(g,{key:0,class:o(["skeleton-avatar",f.value]),style:i(k.value)},null,8,["class","style"])):r("",!0),e.title?(a(),l(g,{key:1,class:"skeleton-title",style:i(m.value)},null,8,["style"])):r("",!0),e.paragraph?(a(),l(g,{key:2,class:"skeleton-paragraph"},{default:s((()=>[(a(!0),n(d,null,u(y.value,((e,t)=>(a(),l(g,{key:t,class:"skeleton-line",style:i(x(t))},null,8,["style"])))),128))])),_:1})):r("",!0),e.loading?r("",!0):h(e.$slots,"default",{key:3},void 0,!0),e.template&&e.loading?(a(),n(d,{key:4},["article"===e.template?(a(),l(g,{key:0,class:"skeleton-article"},{default:s((()=>[c(g,{class:"skeleton-title",style:{width:"80%","margin-bottom":"16px"}}),c(g,{class:"skeleton-meta"},{default:s((()=>[c(g,{class:"skeleton-avatar skeleton-avatar-small"}),c(g,{class:"skeleton-text",style:{width:"100px","margin-left":"12px"}}),c(g,{class:"skeleton-text",style:{width:"80px","margin-left":"12px"}})])),_:1}),c(g,{class:"skeleton-paragraph",style:{"margin-top":"20px"}},{default:s((()=>[c(g,{class:"skeleton-line",style:{width:"100%"}}),c(g,{class:"skeleton-line",style:{width:"95%"}}),c(g,{class:"skeleton-line",style:{width:"88%"}}),c(g,{class:"skeleton-line",style:{width:"92%"}})])),_:1})])),_:1})):r("",!0),"card"===e.template?(a(),l(g,{key:1,class:"skeleton-card"},{default:s((()=>[c(g,{class:"skeleton-image",style:{height:"200px","margin-bottom":"16px"}}),c(g,{class:"skeleton-title",style:{width:"70%","margin-bottom":"12px"}}),c(g,{class:"skeleton-paragraph"},{default:s((()=>[c(g,{class:"skeleton-line",style:{width:"100%"}}),c(g,{class:"skeleton-line",style:{width:"85%"}})])),_:1})])),_:1})):r("",!0),"list"===e.template?(a(),l(g,{key:2,class:"skeleton-list"},{default:s((()=>[(a(!0),n(d,null,u(e.listRows,(e=>(a(),l(g,{key:e,class:"skeleton-list-item"},{default:s((()=>[c(g,{class:"skeleton-avatar skeleton-avatar-small"}),c(g,{class:"skeleton-content"},{default:s((()=>[c(g,{class:"skeleton-text",style:{width:"60%","margin-bottom":"8px"}}),c(g,{class:"skeleton-text",style:{width:"40%"}})])),_:1})])),_:2},1024)))),128))])),_:1})):r("",!0),"profile"===e.template?(a(),l(g,{key:3,class:"skeleton-profile"},{default:s((()=>[c(g,{class:"skeleton-avatar skeleton-avatar-large"}),c(g,{class:"skeleton-info"},{default:s((()=>[c(g,{class:"skeleton-text",style:{width:"120px","margin-bottom":"8px"}}),c(g,{class:"skeleton-text",style:{width:"200px","margin-bottom":"8px"}}),c(g,{class:"skeleton-text",style:{width:"150px"}})])),_:1})])),_:1})):r("",!0),"table"===e.template?(a(),l(g,{key:4,class:"skeleton-table"},{default:s((()=>[c(g,{class:"skeleton-table-header"},{default:s((()=>[c(g,{class:"skeleton-text",style:{width:"80px"}}),c(g,{class:"skeleton-text",style:{width:"100px"}}),c(g,{class:"skeleton-text",style:{width:"120px"}}),c(g,{class:"skeleton-text",style:{width:"90px"}})])),_:1}),(a(!0),n(d,null,u(e.tableRows,(e=>(a(),l(g,{key:e,class:"skeleton-table-row"},{default:s((()=>[c(g,{class:"skeleton-text",style:{width:"70px"}}),c(g,{class:"skeleton-text",style:{width:"90px"}}),c(g,{class:"skeleton-text",style:{width:"110px"}}),c(g,{class:"skeleton-text",style:{width:"80px"}})])),_:2},1024)))),128))])),_:1})):r("",!0)],64)):r("",!0)])),_:3},8,["class"])}}}),[["__scopeId","data-v-51d7758d"]]),m=g(e({__name:"SkeletonItem",props:{loading:{type:Boolean,default:!0},type:{default:"text"},width:{default:"100%"},height:{default:"auto"},backgroundColor:{default:"#f2f2f2"},borderRadius:{default:4},animated:{type:Boolean,default:!0},animationDuration:{default:1.5},animationDelay:{default:0},variant:{default:"text"},lines:{default:1}},setup(e){const n=e,d=t((()=>["skeleton-item-base",`skeleton-${n.type}`,`skeleton-variant-${n.variant}`,{"skeleton-animated":n.animated&&n.loading}])),u=t((()=>{const e={};if(n.loading)switch(e.background=`linear-gradient(90deg, ${n.backgroundColor} 25%, #e6e6e6 50%, ${n.backgroundColor} 75%)`,e.backgroundSize="200% 100%","auto"!==n.width&&(e.width="number"==typeof n.width?`${n.width}px`:n.width),"auto"!==n.height&&(e.height="number"==typeof n.height?`${n.height}px`:n.height),"circular"===n.variant||"avatar"===n.type||"circle"===n.type?e.borderRadius="50%":e.borderRadius="number"==typeof n.borderRadius?`${n.borderRadius}px`:n.borderRadius,n.animated&&(e.animationDuration=`${n.animationDuration}s`,e.animationDelay=`${n.animationDelay}s`),n.type){case"avatar":case"circle":"100%"===n.width&&(e.width="40px"),"auto"===n.height&&(e.height="40px");break;case"text":"auto"===n.height&&(e.height="16px");break;case"button":"auto"===n.height&&(e.height="36px"),"100%"===n.width&&(e.width="80px");break;case"input":"auto"===n.height&&(e.height="40px");break;case"image":"auto"===n.height&&(e.height="200px");break;case"rect":"auto"===n.height&&(e.height="100px")}return e}));return(e,t)=>{const n=p;return a(),l(n,{class:o(["skeleton-item",d.value]),style:i(u.value)},{default:s((()=>[e.loading?r("",!0):h(e.$slots,"default",{key:0},void 0,!0)])),_:3},8,["class","style"])}}}),[["__scopeId","data-v-78e793a0"]]),y={createConfig:(e={})=>({loading:!0,animated:!0,avatar:!1,avatarSize:"default",avatarShape:"circle",title:!1,titleWidth:"40%",paragraph:!1,paragraphRows:3,paragraphWidths:["100%","95%","80%"],listRows:3,tableRows:3,backgroundColor:"#f2f2f2",highlightColor:"#e6e6e6",borderRadius:4,animationDuration:1.5,animationDelay:0,...e}),createArticleConfig(){return this.createConfig({template:"article"})},createCardConfig(){return this.createConfig({template:"card"})},createListConfig(e=3){return this.createConfig({template:"list",listRows:e})},createProfileConfig(){return this.createConfig({template:"profile"})},createTableConfig(e=3){return this.createConfig({template:"table",tableRows:e})},createCustomConfig(e){return this.createConfig({avatar:e.avatar,title:e.title,paragraph:e.paragraph,paragraphRows:e.rows||3})},getParagraphWidths:(e="medium")=>({short:["80%","60%"],medium:["100%","95%","80%"],long:["100%","98%","95%","88%","92%"]}[e]),generateRandomWidths(e,t=60,a=100){const l=[];for(let s=0;s<e;s++){const e=Math.floor(Math.random()*(a-t+1))+t;l.push(`${e}%`)}return l},createStaggeredAnimation(e,t=0,a=.1){const l=[];for(let s=0;s<e;s++)l.push({loading:!0,animated:!0,animationDelay:t+s*a});return l},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches,getThemeColors(){const e=this.isDarkTheme();return{backgroundColor:e?"#2a2a2a":"#f2f2f2",highlightColor:e?"#3a3a3a":"#e6e6e6"}}};y.createArticleConfig(),y.createCardConfig(),y.createListConfig(),y.createProfileConfig(),y.createTableConfig(),y.createCustomConfig({title:!0,paragraph:!0,rows:2}),y.createCustomConfig({avatar:!0,title:!0,paragraph:!0,rows:1}),y.createConfig();export{k as S,f as _,m as a};
