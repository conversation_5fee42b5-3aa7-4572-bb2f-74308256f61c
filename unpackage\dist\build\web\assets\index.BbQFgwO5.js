import"./index-ChUEiI3E.js";const e={createConfig:(e={})=>({visible:!1,title:"",content:"",type:"default",width:"auto",height:"auto",maxWidth:"80%",maxHeight:"80%",top:"auto",backgroundColor:"#ffffff",borderRadius:12,maskClosable:!0,maskColor:"#000000",maskOpacity:.5,titleColor:"#333333",titleSize:18,titleAlign:"center",contentColor:"#666666",contentSize:16,contentAlign:"center",showClose:!1,closeText:"✕",closeColor:"#999999",closeSize:16,showFooter:!0,showCancel:!0,showConfirm:!0,cancelText:"取消",confirmText:"确定",cancelColor:"#666666",confirmColor:"#ffffff",cancelBgColor:"#f8f9fa",confirmBgColor:"#007aff",animation:"scale",animationDuration:300,zIndex:9999,safeAreaInsetBottom:!1,customClass:"",customStyle:{},...e}),createAlertConfig(e,t){return this.createConfig({type:"alert",title:t||"提示",content:e,showCancel:!1,confirmText:"知道了"})},createConfirmConfig(e,t){return this.createConfig({type:"confirm",title:t||"确认",content:e,showCancel:!0,showConfirm:!0})},createSuccessConfig(e,t){return this.createConfig({type:"alert",title:t||"成功",content:e,showCancel:!1,confirmText:"确定",confirmBgColor:"#4cd964"})},createErrorConfig(e,t){return this.createConfig({type:"alert",title:t||"错误",content:e,showCancel:!1,confirmText:"确定",confirmBgColor:"#ff3b30"})},createWarningConfig(e,t){return this.createConfig({type:"alert",title:t||"警告",content:e,showCancel:!1,confirmText:"确定",confirmBgColor:"#ff9500"})},createLoadingConfig(e){return this.createConfig({type:"custom",content:e||"加载中...",showFooter:!1,showClose:!1,maskClosable:!1,width:200,height:120})},getThemeColors(){const e=this.isDarkTheme();return{backgroundColor:e?"#2a2a2a":"#ffffff",titleColor:e?"#ffffff":"#333333",contentColor:e?"#cccccc":"#666666",maskColor:"#000000",cancelBgColor:e?"#3a3a3a":"#f8f9fa",cancelColor:e?"#cccccc":"#666666"}},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches,debounce(e,t){let o=null;return(...n)=>{o&&clearTimeout(o),o=setTimeout((()=>e(...n)),t)}},throttle(e,t){let o=!1;return(...n)=>{o||(e(...n),o=!0,setTimeout((()=>o=!1),t))}}};e.createConfig(),e.createAlertConfig("这是一个警告信息"),e.createConfirmConfig("确定要执行此操作吗？"),e.createSuccessConfig("操作成功！"),e.createErrorConfig("操作失败，请重试"),e.createWarningConfig("请注意相关风险"),e.createLoadingConfig();
