import{y as e,H as t,D as o,P as l,o as a,c as n,w as s,a as i,j as u,l as c,J as p,x as r,b as d,t as f,E as m,i as y,e as h,f as b}from"./index-ChUEiI3E.js";import"./index._qKjp6eZ.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const x=g(e({__name:"BottomPopup",props:{visible:{type:Boolean,default:!1},title:{default:""},content:{default:""},height:{default:"auto"},maxHeight:{default:"80%"},minHeight:{default:"200px"},borderRadius:{default:"20px 20px 0 0"},backgroundColor:{default:"#ffffff"},maskClosable:{type:Boolean,default:!0},maskColor:{default:"#000000"},maskOpacity:{default:.5},titleColor:{default:"#333333"},titleSize:{default:18},titleAlign:{default:"center"},contentColor:{default:"#666666"},contentSize:{default:16},contentPadding:{default:"20px"},showClose:{type:Boolean,default:!1},closeText:{default:"✕"},closeColor:{default:"#999999"},closeSize:{default:16},showFooter:{type:Boolean,default:!1},showCancel:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!0},cancelText:{default:"取消"},confirmText:{default:"确定"},cancelColor:{default:"#666666"},confirmColor:{default:"#ffffff"},cancelBgColor:{default:"#f8f9fa"},confirmBgColor:{default:"#007aff"},showHandle:{type:Boolean,default:!0},handleColor:{default:"#e5e5e5"},animationDuration:{default:300},safeAreaInsetBottom:{type:Boolean,default:!0},zIndex:{default:9999},customClass:{default:""},customStyle:{default:()=>({})}},emits:["update:visible","open","close","cancel","confirm","maskClick"],setup(e,{emit:g}){const x=e,C=g;t((()=>x.visible),(e=>{e?(C("open"),"undefined"!=typeof document&&(document.body.style.overflow="hidden")):(C("close"),"undefined"!=typeof document&&(document.body.style.overflow=""))}));const k=o((()=>["bottom-popup-overlay-base",{"popup-visible":x.visible},x.customClass])),v=o((()=>({zIndex:x.zIndex,animationDuration:`${x.animationDuration}ms`,...x.customStyle}))),w=o((()=>({backgroundColor:x.maskColor,opacity:x.maskOpacity}))),z=o((()=>["popup-container-base"])),S=o((()=>{const e={backgroundColor:x.backgroundColor,borderRadius:x.borderRadius,animationDuration:`${x.animationDuration}ms`};return"auto"!==x.height&&(e.height="number"==typeof x.height?`${x.height}px`:x.height),x.maxHeight&&(e.maxHeight="number"==typeof x.maxHeight?`${x.maxHeight}px`:x.maxHeight),x.minHeight&&(e.minHeight="number"==typeof x.minHeight?`${x.minHeight}px`:x.minHeight),e})),_=o((()=>({padding:"15px 0 10px"}))),B=o((()=>({width:"40px",height:"4px",backgroundColor:x.handleColor,borderRadius:"2px",margin:"0 auto"}))),H=o((()=>({position:"relative",padding:"20px 20px 10px",borderBottom:x.title?"1px solid #f0f0f0":"none"}))),$=o((()=>({color:x.titleColor,fontSize:"number"==typeof x.titleSize?`${x.titleSize}px`:x.titleSize,textAlign:x.titleAlign,fontWeight:"bold",lineHeight:"1.4",margin:"0",paddingRight:x.showClose?"40px":"0"}))),T=o((()=>({position:"absolute",top:"15px",right:"15px",width:"30px",height:"30px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",borderRadius:"50%",transition:"background-color 0.3s"}))),I=o((()=>({padding:x.contentPadding,flex:"1",overflow:"auto"}))),D=o((()=>({color:x.contentColor,fontSize:"number"==typeof x.contentSize?`${x.contentSize}px`:x.contentSize,lineHeight:"1.6",wordBreak:"break-word"}))),R=o((()=>({padding:"10px 20px 20px",borderTop:"1px solid #f0f0f0"}))),j=o((()=>["btn-base","btn-cancel"])),A=o((()=>["btn-base","btn-confirm"])),P=o((()=>({color:x.cancelColor,backgroundColor:x.cancelBgColor,fontSize:"number"==typeof x.contentSize?`${x.contentSize}px`:x.contentSize}))),F=o((()=>({color:x.confirmColor,backgroundColor:x.confirmBgColor,fontSize:"number"==typeof x.contentSize?`${x.contentSize}px`:x.contentSize}))),O=o((()=>({height:"env(safe-area-inset-bottom)",backgroundColor:x.backgroundColor}))),E=()=>{C("maskClick"),x.maskClosable&&J()},J=()=>{C("update:visible",!1),C("close")},W=()=>{C("cancel"),J()},q=()=>{C("confirm"),J()};return l((()=>{"undefined"!=typeof document&&(document.body.style.overflow="")})),(e,t)=>{const o=y,l=h,g=b;return e.visible?(a(),n(o,{key:0,class:c(["bottom-popup-overlay",k.value]),style:u(v.value),onClick:E,onTouchmove:t[2]||(t[2]=p((()=>{}),["prevent"]))},{default:s((()=>[i(o,{class:"popup-mask",style:u(w.value)},null,8,["style"]),i(o,{class:c(["popup-container",z.value]),style:u(S.value),onClick:t[0]||(t[0]=p((()=>{}),["stop"])),onTouchmove:t[1]||(t[1]=p((()=>{}),["stop"]))},{default:s((()=>[e.showHandle?(a(),n(o,{key:0,class:"popup-handle",style:u(_.value)},{default:s((()=>[i(o,{class:"handle-bar",style:u(B.value)},null,8,["style"])])),_:1},8,["style"])):r("",!0),e.title||e.showClose?(a(),n(o,{key:1,class:"popup-header",style:u(H.value)},{default:s((()=>[e.title?(a(),n(l,{key:0,class:"popup-title",style:u($.value)},{default:s((()=>[d(f(e.title),1)])),_:1},8,["style"])):r("",!0),e.showClose?(a(),n(o,{key:1,class:"popup-close",style:u(T.value),onClick:J},{default:s((()=>[i(l,{class:"close-icon",style:u({color:e.closeColor})},{default:s((()=>[d(f(e.closeText),1)])),_:1},8,["style"])])),_:1},8,["style"])):r("",!0)])),_:1},8,["style"])):r("",!0),i(o,{class:"popup-content",style:u(I.value)},{default:s((()=>[m(e.$slots,"default",{},(()=>[e.content?(a(),n(l,{key:0,class:"popup-text",style:u(D.value)},{default:s((()=>[d(f(e.content),1)])),_:1},8,["style"])):r("",!0)]),!0)])),_:3},8,["style"]),e.$slots.footer||e.showFooter?(a(),n(o,{key:2,class:"popup-footer",style:u(R.value)},{default:s((()=>[m(e.$slots,"footer",{},(()=>[e.showFooter?(a(),n(o,{key:0,class:"popup-buttons"},{default:s((()=>[e.showCancel?(a(),n(g,{key:0,class:c(["popup-btn cancel-btn",j.value]),style:u(P.value),onClick:W},{default:s((()=>[d(f(e.cancelText),1)])),_:1},8,["class","style"])):r("",!0),e.showConfirm?(a(),n(g,{key:1,class:c(["popup-btn confirm-btn",A.value]),style:u(F.value),onClick:q},{default:s((()=>[d(f(e.confirmText),1)])),_:1},8,["class","style"])):r("",!0)])),_:1})):r("",!0)]),!0)])),_:3},8,["style"])):r("",!0),e.safeAreaInsetBottom?(a(),n(o,{key:3,class:"popup-safe-area",style:u(O.value)},null,8,["style"])):r("",!0)])),_:3},8,["class","style"])])),_:3},8,["class","style"])):r("",!0)}}}),[["__scopeId","data-v-68093794"]]);export{x as B};
