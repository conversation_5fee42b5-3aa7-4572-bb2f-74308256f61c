import{y as e,z as l,D as a,H as t,A as o,o as s,c as u,w as d,j as c,a as n,B as i,x as r,b as f,t as v,k as h,F as p,r as b,l as C,M as y,i as _,e as g,S as m,U as k,V as x,f as F}from"./index-ChUEiI3E.js";import{I}from"./Icon.DLnR9vuk.js";/* empty css                                                                 *//* empty css                                                                         */import{_ as w}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=w(e({__name:"CascadeSelection",props:{data:{default:()=>[]},value:{default:()=>[]},defaultValue:{default:()=>[]},labelField:{default:"label"},valueField:{default:"value"},childrenField:{default:"children"},iconField:{default:"icon"},subLabelField:{default:"subLabel"},title:{default:""},placeholder:{default:"请选择"},emptyText:{default:"暂无数据"},showClose:{type:Boolean,default:!1},showFooter:{type:Boolean,default:!0},showActiveIndicator:{type:Boolean,default:!0},height:{default:400},width:{default:"100%"},titleSize:{default:16},itemHeight:{default:44},itemSize:{default:14},levelWidth:{default:"auto"},backgroundColor:{default:"#ffffff"},headerBackgroundColor:{default:"#ffffff"},footerBackgroundColor:{default:"#ffffff"},tabBackgroundColor:{default:"#f8f9fa"},activeColor:{default:"#007aff"},textColor:{default:"#333333"},subTextColor:{default:"#999999"},borderColor:{default:"#e5e5e5"},checkIconColor:{default:"#007aff"},closeIconColor:{default:"#666666"},confirmText:{default:"确定"},cancelText:{default:"取消"},confirmButtonColor:{default:"#007aff"},cancelButtonColor:{default:"#666666"},checkIconSize:{default:16},closeIconSize:{default:18},customClass:{default:""},customStyle:{default:()=>({})}},emits:["update:value","select","change","confirm","cancel","close"],setup(e,{emit:w}){const B=e,V=w,z=l(0),S=l([]),$=l(""),T=l([]),j=a((()=>({height:"number"==typeof B.height?`${B.height}px`:B.height,width:"number"==typeof B.width?`${B.width}px`:B.width,backgroundColor:B.backgroundColor,...B.customStyle}))),M=a((()=>({backgroundColor:B.headerBackgroundColor,borderBottomColor:B.borderColor}))),A=a((()=>({fontSize:`${B.titleSize}px`,color:B.textColor}))),L=a((()=>{const e=B.title||B.showClose?44:0,l=B.showFooter?60:0;return{height:`${("number"==typeof B.height?B.height:400)-e-l}px`}})),H=a((()=>({backgroundColor:B.tabBackgroundColor,borderBottomColor:B.borderColor}))),W=a((()=>{const e=S.value.length>0?44:0;return{height:`${parseInt(L.value.height)-e}px`}})),D=a((()=>({height:"100%"}))),U=a((()=>({backgroundColor:B.footerBackgroundColor,borderTopColor:B.borderColor}))),q=a((()=>({backgroundColor:B.activeColor}))),E=a((()=>({color:B.cancelButtonColor,borderColor:B.cancelButtonColor}))),G=a((()=>({backgroundColor:B.confirmButtonColor,borderColor:B.confirmButtonColor}))),J=a((()=>T.value.length>0)),K=e=>{const l=e===z.value;return{color:l?B.activeColor:B.textColor,fontWeight:l?"bold":"normal"}},N=()=>{if(S.value=[],T.value=[],!B.data||0===B.data.length)return;const e={label:B.placeholder,value:void 0,options:B.data,selectedIndex:-1,scrollIntoView:""};S.value.push(e),z.value=0,B.defaultValue&&B.defaultValue.length>0?O():B.value&&B.value.length>0&&P(B.value)},O=()=>{if(!B.defaultValue||0===B.defaultValue.length)return;let e=B.data,l=0;for(const a of B.defaultValue){if(!e)break;const t=e.findIndex((e=>e[B.valueField]===a));if(-1===t)break;const o=e[t];if(S.value[l]&&(S.value[l].selectedIndex=t,S.value[l].label=o[B.labelField],S.value[l].value=o[B.valueField]),T.value.push(o),!(o[B.childrenField]&&o[B.childrenField].length>0))break;{l++;const a={label:B.placeholder,value:void 0,options:o[B.childrenField],selectedIndex:-1,scrollIntoView:""};S.value[l]?S.value[l]=a:S.value.push(a),e=o[B.childrenField]}}z.value=Math.min(l,S.value.length-1)},P=e=>{if(!e||0===e.length)return;let l=B.data,a=0;T.value=[];for(const t of e){if(!l)break;const e=l.findIndex((e=>e[B.valueField]===t));if(-1===e)break;const o=l[e];if(S.value[a]&&(S.value[a].selectedIndex=e,S.value[a].label=o[B.labelField],S.value[a].value=o[B.valueField]),T.value.push(o),!(o[B.childrenField]&&o[B.childrenField].length>0))break;{a++;const e={label:B.placeholder,value:void 0,options:o[B.childrenField],selectedIndex:-1,scrollIntoView:""};S.value[a]?S.value[a]=e:S.value.push(e),l=o[B.childrenField]}}z.value=Math.min(a,S.value.length-1)},Q=e=>{z.value=e.detail.current,R()},R=()=>{z.value>1?$.value=`tab_${Math.max(0,z.value-1)}`:$.value="tab_0";const e=S.value[z.value];e&&e.selectedIndex>=0&&y((()=>{const l=Math.max(0,e.selectedIndex-2);e.scrollIntoView=`option_${z.value}_${l}`}))},X=()=>{V("close")},Y=()=>{V("cancel")},Z=()=>{const e=T.value.map((e=>e[B.valueField]));V("confirm",e,T.value)};return t((()=>B.data),(()=>{N()}),{deep:!0,immediate:!0}),t((()=>B.value),(e=>{e&&e.length>0&&P(e)}),{deep:!0}),t((()=>B.defaultValue),(()=>{B.defaultValue&&B.defaultValue.length>0&&O()}),{deep:!0}),t(z,(()=>{R()})),o((()=>{N()})),(e,l)=>{const a=_,t=g,o=m,w=k,N=x,O=F;return s(),u(a,{class:"cascade-selection",style:c(j.value)},{default:d((()=>[e.title||e.showClose?(s(),u(a,{key:0,class:"cascade-header",style:c(M.value)},{default:d((()=>[n(a,{class:"header-left"},{default:d((()=>[e.showClose?(s(),u(a,{key:0,class:"close-btn",onClick:X},{default:d((()=>[n(i(I),{name:"close",size:e.closeIconSize,color:e.closeIconColor},null,8,["size","color"])])),_:1})):r("",!0)])),_:1}),n(a,{class:"header-title",style:c(A.value)},{default:d((()=>[f(v(e.title),1)])),_:1},8,["style"]),n(a,{class:"header-right"})])),_:1},8,["style"])):r("",!0),n(a,{class:"cascade-content",style:c(L.value)},{default:d((()=>[S.value.length>0?(s(),u(o,{key:0,"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":$.value,class:"cascade-tabs",style:c(H.value)},{default:d((()=>[n(a,{class:"tabs-container"},{default:d((()=>[(s(!0),h(p,null,b(S.value,((l,o)=>(s(),u(a,{key:o,id:`tab_${o}`,class:C(["tab-item",{"tab-active":o===z.value}]),style:c(K(o)),onClick:e=>(e=>{e>=0&&e<S.value.length&&(z.value=e,R())})(o)},{default:d((()=>[n(t,{class:"tab-text"},{default:d((()=>[f(v(l.label||e.placeholder),1)])),_:2},1024),o===z.value&&e.showActiveIndicator?(s(),u(a,{key:0,class:"tab-indicator",style:c(q.value)},null,8,["style"])):r("",!0)])),_:2},1032,["id","class","style","onClick"])))),128))])),_:1})])),_:1},8,["scroll-into-view","style"])):r("",!0),n(N,{class:"cascade-swiper",current:z.value,duration:300,onChange:Q,style:c(W.value)},{default:d((()=>[(s(!0),h(p,null,b(S.value,((l,_)=>(s(),u(w,{key:_,class:"swiper-item"},{default:d((()=>[n(o,{"scroll-y":"",class:"options-scroll",style:c(D.value),"scroll-into-view":l.scrollIntoView},{default:d((()=>[l.options&&0!==l.options.length?(s(),u(a,{key:1,class:"options-list"},{default:d((()=>[(s(!0),h(p,null,b(l.options,((o,h)=>{return s(),u(a,{key:h,id:`option_${_}_${h}`,class:C(["option-item",{"option-selected":l.selectedIndex===h}]),style:c((p=l.selectedIndex===h,{height:`${B.itemHeight}px`,backgroundColor:p?`${B.activeColor}10`:"transparent",color:p?B.activeColor:B.textColor})),onClick:e=>((e,l,a)=>{if(a.disabled)return;const t=S.value[e];if(!t)return;if(t.selectedIndex=l,t.label=a[B.labelField],t.value=a[B.valueField],T.value=T.value.slice(0,e),T.value.push(a),S.value=S.value.slice(0,e+1),a[B.childrenField]&&a[B.childrenField].length>0){const l={label:B.placeholder,value:void 0,options:a[B.childrenField],selectedIndex:-1,scrollIntoView:""};S.value.push(l),y((()=>{z.value=e+1,R()}))}V("select",a,e,l);const o=T.value.map((e=>e[B.valueField]));V("update:value",o),V("change",o,T.value),a[B.childrenField]&&0!==a[B.childrenField].length||B.showFooter||Z()})(_,h,o)},{default:d((()=>[o[e.iconField]?(s(),u(a,{key:0,class:"option-icon"},{default:d((()=>[n(t,{class:"icon-text"},{default:d((()=>[f(v(o[e.iconField]),1)])),_:2},1024)])),_:2},1024)):r("",!0),n(a,{class:"option-content"},{default:d((()=>[n(t,{class:"option-label"},{default:d((()=>[f(v(o[e.labelField]),1)])),_:2},1024),o[e.subLabelField]?(s(),u(t,{key:0,class:"option-sublabel"},{default:d((()=>[f(v(o[e.subLabelField]),1)])),_:2},1024)):r("",!0)])),_:2},1024),l.selectedIndex===h?(s(),u(a,{key:1,class:"option-check"},{default:d((()=>[n(i(I),{name:"check",size:e.checkIconSize,color:e.checkIconColor},null,8,["size","color"])])),_:1})):r("",!0)])),_:2},1032,["id","class","style","onClick"]);var p})),128))])),_:2},1024)):(s(),u(a,{key:0,class:"empty-state"},{default:d((()=>[n(t,{class:"empty-text"},{default:d((()=>[f(v(e.emptyText),1)])),_:1})])),_:1}))])),_:2},1032,["style","scroll-into-view"])])),_:2},1024)))),128))])),_:1},8,["current","style"])])),_:1},8,["style"]),e.showFooter?(s(),u(a,{key:1,class:"cascade-footer",style:c(U.value)},{default:d((()=>[n(a,{class:"footer-buttons"},{default:d((()=>[n(O,{class:"btn btn-cancel",style:c(E.value),onClick:Y},{default:d((()=>[f(v(e.cancelText),1)])),_:1},8,["style"]),n(O,{class:"btn btn-confirm",style:c(G.value),disabled:!J.value,onClick:Z},{default:d((()=>[f(v(e.confirmText),1)])),_:1},8,["style","disabled"])])),_:1})])),_:1},8,["style"])):r("",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-d592f9ed"]]);export{B as C};
