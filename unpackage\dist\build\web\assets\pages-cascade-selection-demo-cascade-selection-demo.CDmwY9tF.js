import{y as e,z as l,A as a,o,c as u,w as n,a as i,B as t,b as v,t as s,x as d,j as c,s as r,e as b,f,i as h,S as m}from"./index-ChUEiI3E.js";import{n as _,C as p}from"./index.DI3nc_37.js";import"./index._qKjp6eZ.js";/* empty css                                                                 *//* empty css                                                                         */import{B as g}from"./BottomPopup.gFUAg0m5.js";import{C as j}from"./CascadeSelection.D80cIVcX.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./Icon.DLnR9vuk.js";const y={regions:[{label:"北京市",value:"beijing",children:[{label:"东城区",value:"dongcheng",children:[{label:"王府井街道",value:"wangfujing"},{label:"东华门街道",value:"donghuamen"},{label:"安定门街道",value:"andingmen"}]},{label:"西城区",value:"xicheng",children:[{label:"西长安街街道",value:"xichanganjie"},{label:"新街口街道",value:"xinjiekou"},{label:"月坛街道",value:"yuetan"}]},{label:"朝阳区",value:"chaoyang",children:[{label:"建国门外街道",value:"jianguomenwai"},{label:"朝外街道",value:"chaowai"},{label:"呼家楼街道",value:"hujialou"}]}]},{label:"上海市",value:"shanghai",children:[{label:"黄浦区",value:"huangpu",children:[{label:"南京东路街道",value:"nanjingdonglu"},{label:"外滩街道",value:"waitan"},{label:"半淞园路街道",value:"bansonglu"}]},{label:"徐汇区",value:"xuhui",children:[{label:"湖南路街道",value:"hunanlu"},{label:"天平路街道",value:"tianpinglu"},{label:"枫林路街道",value:"fenglinlu"}]}]},{label:"广东省",value:"guangdong",children:[{label:"广州市",value:"guangzhou",children:[{label:"越秀区",value:"yuexiu"},{label:"荔湾区",value:"liwan"},{label:"海珠区",value:"haizhu"}]},{label:"深圳市",value:"shenzhen",children:[{label:"福田区",value:"futian"},{label:"罗湖区",value:"luohu"},{label:"南山区",value:"nanshan"}]}]}],categories:[{label:"电子产品",value:"electronics",icon:"📱",children:[{label:"手机",value:"phone",icon:"📱",children:[{label:"iPhone",value:"iphone",icon:"🍎"},{label:"Android",value:"android",icon:"🤖"},{label:"华为",value:"huawei",icon:"📱"}]},{label:"电脑",value:"computer",icon:"💻",children:[{label:"笔记本",value:"laptop",icon:"💻"},{label:"台式机",value:"desktop",icon:"🖥️"},{label:"平板",value:"tablet",icon:"📱"}]}]},{label:"服装",value:"clothing",icon:"👕",children:[{label:"男装",value:"mens",icon:"👔",children:[{label:"衬衫",value:"shirt",icon:"👔"},{label:"T恤",value:"tshirt",icon:"👕"},{label:"裤子",value:"pants",icon:"👖"}]},{label:"女装",value:"womens",icon:"👗",children:[{label:"连衣裙",value:"dress",icon:"👗"},{label:"上衣",value:"top",icon:"👚"},{label:"裙子",value:"skirt",icon:"👗"}]}]}],departments:[{name:"技术部",id:"tech",children:[{name:"前端组",id:"frontend",children:[{name:"Vue团队",id:"vue-team"},{name:"React团队",id:"react-team"},{name:"Angular团队",id:"angular-team"}]},{name:"后端组",id:"backend",children:[{name:"Java团队",id:"java-team"},{name:"Python团队",id:"python-team"},{name:"Node.js团队",id:"nodejs-team"}]}]},{name:"产品部",id:"product",children:[{name:"产品设计",id:"design",children:[{name:"UI设计师",id:"ui-designer"},{name:"UX设计师",id:"ux-designer"}]},{name:"产品运营",id:"operation",children:[{name:"用户运营",id:"user-operation"},{name:"内容运营",id:"content-operation"}]}]}]},w=C(e({__name:"cascade-selection-demo",setup(e){const C=l(0),w=l(!1),k=l([]),U=l([]),$=l(!1),A=l([]),x=l([]),S=l(!1),B=l([]),z=l([]),I=l(!1),P=l([]),T=l([]),J=l(!1),D=l([]),F=l([]),N=l(!1),R=l([]),V=l([]),q=l(!1),E=l([]),H=l([]),M=l(!1),X=l([]),G=l([]),K=l(!1),L=l([]),O=l([]),Q=l(!1),W=l([]),Y=l([]),Z=l(!1),ee=l([]),le=l([]),ae=l(!1),oe=l([]),ue=l([]),ne=l(y.regions),ie=l(y.categories),te=l(y.departments),ve=l([{title:"一级分类A",code:"a",items:[{title:"二级分类A1",code:"a1",items:[{title:"三级分类A1a",code:"a1a"},{title:"三级分类A1b",code:"a1b"}]},{title:"二级分类A2",code:"a2",items:[{title:"三级分类A2a",code:"a2a"},{title:"三级分类A2b",code:"a2b"}]}]},{title:"一级分类B",code:"b",items:[{title:"二级分类B1",code:"b1",items:[{title:"三级分类B1a",code:"b1a"},{title:"三级分类B1b",code:"b1b"}]}]}]),se=l([{label:"2024年",value:"2024",children:Array.from({length:12},((e,l)=>({label:`${l+1}月`,value:`${l+1}`,children:Array.from({length:31},((e,l)=>({label:`${l+1}日`,value:`${l+1}`,children:Array.from({length:24},((e,l)=>({label:`${l}时`,value:`${l}`})))})))})))},{label:"2025年",value:"2025",children:Array.from({length:12},((e,l)=>({label:`${l+1}月`,value:`${l+1}`,children:Array.from({length:31},((e,l)=>({label:`${l+1}日`,value:`${l+1}`,children:Array.from({length:24},((e,l)=>({label:`${l}时`,value:`${l}`})))})))})))}]),de=l([{label:"系统管理",value:"system",icon:"⚙️",children:[{label:"用户管理",value:"user",icon:"👤",children:[{label:"用户列表",value:"user-list",icon:"📋"},{label:"添加用户",value:"user-add",icon:"➕"}]},{label:"角色管理",value:"role",icon:"👥",children:[{label:"角色列表",value:"role-list",icon:"📋"},{label:"权限设置",value:"permission",icon:"🔐"}]}]},{label:"内容管理",value:"content",icon:"📝",children:[{label:"文章管理",value:"article",icon:"📄",children:[{label:"文章列表",value:"article-list",icon:"📋"},{label:"发布文章",value:"article-add",icon:"✍️"}]},{label:"分类管理",value:"category",icon:"📂",children:[{label:"分类列表",value:"category-list",icon:"📋"},{label:"添加分类",value:"category-add",icon:"➕"}]}]}]),ce=l([{label:"前端技术",value:"frontend",icon:"🌐",children:[{label:"JavaScript",value:"javascript",icon:"🟨",children:[{label:"ES6+",value:"es6",icon:"⚡"},{label:"TypeScript",value:"typescript",icon:"🔷"},{label:"Node.js",value:"nodejs",icon:"🟢"}]},{label:"框架",value:"framework",icon:"🏗️",children:[{label:"Vue.js",value:"vue",icon:"💚"},{label:"React",value:"react",icon:"⚛️"},{label:"Angular",value:"angular",icon:"🔴"}]}]},{label:"后端技术",value:"backend",icon:"⚙️",children:[{label:"Java",value:"java",icon:"☕",children:[{label:"Spring",value:"spring",icon:"🍃"},{label:"MyBatis",value:"mybatis",icon:"🗃️"}]},{label:"Python",value:"python",icon:"🐍",children:[{label:"Django",value:"django",icon:"🎸"},{label:"Flask",value:"flask",icon:"🌶️"}]}]}]);a((()=>{C.value=_.getTotalHeight()}));const re=()=>{w.value=!0},be=()=>{$.value=!0},fe=()=>{S.value=!0},he=()=>{I.value=!0},me=()=>{J.value=!0},_e=()=>{N.value=!0},pe=()=>{q.value=!0},ge=()=>{M.value=!0},je=()=>{K.value=!0},Ce=()=>{Q.value=!0},ye=()=>{Z.value=!0},we=()=>{ae.value=!0},ke=(e,l)=>{U.value=l.map((e=>e.label)),w.value=!1,r({title:`已选择: ${U.value.join(" > ")}`,icon:"none"})},Ue=(e,l)=>{x.value=l.map((e=>e.label)),$.value=!1,r({title:`已选择: ${x.value.join(" > ")}`,icon:"none"})},$e=(e,l)=>{z.value=l.map((e=>e.name)),S.value=!1,r({title:`已选择: ${z.value.join(" > ")}`,icon:"none"})},Ae=(e,l)=>{T.value=l.map((e=>e.label)),I.value=!1,r({title:`已选择: ${T.value.join(" > ")}`,icon:"none"})},xe=(e,l)=>{F.value=l.map((e=>e.label)),J.value=!1,r({title:`已选择: ${F.value.join(" > ")}`,icon:"none"})},Se=(e,l)=>{V.value=l.map((e=>e.name)),N.value=!1,r({title:`已选择: ${V.value.join(" > ")}`,icon:"none"})},Be=(e,l,a)=>{if(!e.children||0===e.children.length){const o=[];let u=ne.value,n=a;for(let e=0;e<=l;e++)e===l?o.push(u[n].label):(o.push(u[0].label),u=u[0].children||[]);H.value=[e.label],q.value=!1,r({title:`已选择: ${e.label}`,icon:"none"})}},ze=(e,l)=>{G.value=l.map((e=>e.title)),M.value=!1,r({title:`已选择: ${G.value.join(" > ")}`,icon:"none"})},Ie=(e,l)=>{O.value=l.map((e=>e.label)),K.value=!1,r({title:`已选择: ${O.value.join(" > ")}`,icon:"none"})},Pe=(e,l)=>{Y.value=l.map((e=>e.label)),Q.value=!1,r({title:`已选择: ${Y.value.join(" ")}`,icon:"none"})},Te=(e,l,a)=>{e.children&&0!==e.children.length||(le.value=[e.label],Z.value=!1,r({title:`已选择: ${e.label}`,icon:"none"}))},Je=(e,l)=>{ue.value=l.map((e=>e.label)),ae.value=!1,r({title:`已选择: ${ue.value.join(" > ")}`,icon:"none"})};return(e,l)=>{const a=b,r=f,_=h,y=m;return o(),u(_,{class:"page"},{default:n((()=>[i(t(p),{title:"级联选择器组件演示","show-back":!0}),i(y,{"scroll-y":"",class:"content",style:c({paddingTop:C.value+"px"})},{default:n((()=>[i(_,{class:"demo-section"},{default:n((()=>[i(a,{class:"section-title"},{default:n((()=>[v("📝 基础用法")])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("地区选择器:")])),_:1}),i(r,{onClick:re,class:"demo-btn"},{default:n((()=>[v("选择地区")])),_:1}),U.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(U.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("分类选择器:")])),_:1}),i(r,{onClick:be,class:"demo-btn"},{default:n((()=>[v("选择分类")])),_:1}),x.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(x.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("部门选择器:")])),_:1}),i(r,{onClick:fe,class:"demo-btn"},{default:n((()=>[v("选择部门")])),_:1}),z.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(z.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1})])),_:1}),i(_,{class:"demo-section"},{default:n((()=>[i(a,{class:"section-title"},{default:n((()=>[v("🎨 样式定制")])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("自定义颜色:")])),_:1}),i(r,{onClick:he,class:"demo-btn"},{default:n((()=>[v("自定义颜色选择器")])),_:1}),T.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(T.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("暗色主题:")])),_:1}),i(r,{onClick:me,class:"demo-btn"},{default:n((()=>[v("暗色主题选择器")])),_:1}),F.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(F.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("自定义尺寸:")])),_:1}),i(r,{onClick:_e,class:"demo-btn"},{default:n((()=>[v("自定义尺寸选择器")])),_:1}),V.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(V.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1})])),_:1}),i(_,{class:"demo-section"},{default:n((()=>[i(a,{class:"section-title"},{default:n((()=>[v("⚙️ 功能配置")])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("无底部按钮:")])),_:1}),i(r,{onClick:pe,class:"demo-btn"},{default:n((()=>[v("无底部按钮选择器")])),_:1}),H.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(H.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("自定义字段:")])),_:1}),i(r,{onClick:ge,class:"demo-btn"},{default:n((()=>[v("自定义字段选择器")])),_:1}),G.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(G.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("默认值:")])),_:1}),i(r,{onClick:je,class:"demo-btn"},{default:n((()=>[v("带默认值选择器")])),_:1}),O.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(O.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1})])),_:1}),i(_,{class:"demo-section"},{default:n((()=>[i(a,{class:"section-title"},{default:n((()=>[v("🛠️ 应用场景")])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("时间选择:")])),_:1}),i(r,{onClick:Ce,class:"demo-btn"},{default:n((()=>[v("时间选择器")])),_:1}),Y.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(Y.value.join(" ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("菜单选择:")])),_:1}),i(r,{onClick:ye,class:"demo-btn"},{default:n((()=>[v("菜单选择器")])),_:1}),le.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(le.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1}),i(_,{class:"demo-card"},{default:n((()=>[i(a,{class:"demo-label"},{default:n((()=>[v("技能选择:")])),_:1}),i(r,{onClick:we,class:"demo-btn"},{default:n((()=>[v("技能选择器")])),_:1}),ue.value?(o(),u(a,{key:0,class:"demo-result"},{default:n((()=>[v(" 已选择: "+s(ue.value.join(" > ")),1)])),_:1})):d("",!0)])),_:1})])),_:1})])),_:1},8,["style"]),i(t(g),{visible:w.value,"onUpdate:visible":l[3]||(l[3]=e=>w.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ne.value,value:k.value,"onUpdate:value":l[0]||(l[0]=e=>k.value=e),title:"选择地区","show-close":!0,"show-footer":!0,onConfirm:ke,onClose:l[1]||(l[1]=e=>w.value=!1),onCancel:l[2]||(l[2]=e=>w.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:$.value,"onUpdate:visible":l[7]||(l[7]=e=>$.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ie.value,value:A.value,"onUpdate:value":l[4]||(l[4]=e=>A.value=e),title:"选择分类","show-close":!0,"show-footer":!0,"icon-field":"icon",onConfirm:Ue,onClose:l[5]||(l[5]=e=>$.value=!1),onCancel:l[6]||(l[6]=e=>$.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:S.value,"onUpdate:visible":l[11]||(l[11]=e=>S.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:te.value,value:B.value,"onUpdate:value":l[8]||(l[8]=e=>B.value=e),title:"选择部门","show-close":!0,"show-footer":!0,"label-field":"name","value-field":"id",onConfirm:$e,onClose:l[9]||(l[9]=e=>S.value=!1),onCancel:l[10]||(l[10]=e=>S.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:I.value,"onUpdate:visible":l[15]||(l[15]=e=>I.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ne.value,value:P.value,"onUpdate:value":l[12]||(l[12]=e=>P.value=e),title:"自定义颜色选择器","show-close":!0,"show-footer":!0,"background-color":"#007aff","title-color":"#ffffff","item-color":"#ffffff","selected-color":"#ffffff","selected-bg-color":"rgba(255, 255, 255, 0.2)","level-border-color":"rgba(255, 255, 255, 0.1)",onConfirm:Ae,onClose:l[13]||(l[13]=e=>I.value=!1),onCancel:l[14]||(l[14]=e=>I.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:J.value,"onUpdate:visible":l[19]||(l[19]=e=>J.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ie.value,value:D.value,"onUpdate:value":l[16]||(l[16]=e=>D.value=e),title:"暗色主题选择器","show-close":!0,"show-footer":!0,"background-color":"#2a2a2a","title-color":"#ffffff","item-color":"#cccccc","selected-color":"#409eff","selected-bg-color":"#1f2937","level-border-color":"#444444","icon-field":"icon",onConfirm:xe,onClose:l[17]||(l[17]=e=>J.value=!1),onCancel:l[18]||(l[18]=e=>J.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:N.value,"onUpdate:visible":l[23]||(l[23]=e=>N.value=e),height:"70%"},{default:n((()=>[i(t(j),{data:te.value,value:R.value,"onUpdate:value":l[20]||(l[20]=e=>R.value=e),title:"自定义尺寸选择器","show-close":!0,"show-footer":!0,height:500,"title-size":20,"item-height":60,"item-size":18,"label-field":"name","value-field":"id",onConfirm:Se,onClose:l[21]||(l[21]=e=>N.value=!1),onCancel:l[22]||(l[22]=e=>N.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:q.value,"onUpdate:visible":l[26]||(l[26]=e=>q.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ne.value,value:E.value,"onUpdate:value":l[24]||(l[24]=e=>E.value=e),title:"无底部按钮选择器","show-close":!0,"show-footer":!1,onSelect:Be,onClose:l[25]||(l[25]=e=>q.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:M.value,"onUpdate:visible":l[30]||(l[30]=e=>M.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ve.value,value:X.value,"onUpdate:value":l[27]||(l[27]=e=>X.value=e),title:"自定义字段选择器","show-close":!0,"show-footer":!0,"label-field":"title","value-field":"code","children-field":"items",onConfirm:ze,onClose:l[28]||(l[28]=e=>M.value=!1),onCancel:l[29]||(l[29]=e=>M.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:K.value,"onUpdate:visible":l[34]||(l[34]=e=>K.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ne.value,value:L.value,"onUpdate:value":l[31]||(l[31]=e=>L.value=e),"default-value":["beijing","dongcheng","wangfujing"],title:"带默认值选择器","show-close":!0,"show-footer":!0,onConfirm:Ie,onClose:l[32]||(l[32]=e=>K.value=!1),onCancel:l[33]||(l[33]=e=>K.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:Q.value,"onUpdate:visible":l[38]||(l[38]=e=>Q.value=e),height:"50%"},{default:n((()=>[i(t(j),{data:se.value,value:W.value,"onUpdate:value":l[35]||(l[35]=e=>W.value=e),title:"选择时间","show-close":!0,"show-footer":!0,"level-width":"25%",onConfirm:Pe,onClose:l[36]||(l[36]=e=>Q.value=!1),onCancel:l[37]||(l[37]=e=>Q.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:Z.value,"onUpdate:visible":l[41]||(l[41]=e=>Z.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:de.value,value:ee.value,"onUpdate:value":l[39]||(l[39]=e=>ee.value=e),title:"选择菜单","show-close":!0,"show-footer":!1,"icon-field":"icon",onSelect:Te,onClose:l[40]||(l[40]=e=>Z.value=!1)},null,8,["data","value"])])),_:1},8,["visible"]),i(t(g),{visible:ae.value,"onUpdate:visible":l[45]||(l[45]=e=>ae.value=e),height:"60%"},{default:n((()=>[i(t(j),{data:ce.value,value:oe.value,"onUpdate:value":l[42]||(l[42]=e=>oe.value=e),title:"选择技能","show-close":!0,"show-footer":!0,"icon-field":"icon",onConfirm:Je,onClose:l[43]||(l[43]=e=>ae.value=!1),onCancel:l[44]||(l[44]=e=>ae.value=!1)},null,8,["data","value"])])),_:1},8,["visible"])])),_:1})}}}),[["__scopeId","data-v-0ac957dc"]]);export{w as default};
