import{y as e,z as t,D as a,o,c as l,w as s,l as i,j as d,b as n,t as r,x as u,e as c,d as f,i as p}from"./index-ChUEiI3E.js";/* empty css                                                                 */import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const y=g(e({__name:"Icon",props:{name:{default:""},type:{default:"builtin"},src:{default:""},size:{default:24},color:{default:"#333333"},fontFamily:{default:"iconfont"},fontClass:{default:""},prefix:{default:"icon-"},imageMode:{default:"aspectFit"},svgContent:{default:""},unicode:{default:""},width:{default:"auto"},height:{default:"auto"},borderRadius:{default:0},backgroundColor:{default:"transparent"},badge:{default:""},dot:{type:Boolean,default:!1},badgeColor:{default:"#ff3b30"},badgeTextColor:{default:"#ffffff"},badgeSize:{default:16},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},spin:{type:Boolean,default:!1},pulse:{type:Boolean,default:!1},customStyle:{default:""},customClass:{default:""}},emits:["click","load","error"],setup(e,{emit:g}){const y=e,b=g,m=t(!1),h=t(!1),v={home:"🏠",user:"👤",search:"🔍",heart:"❤️",star:"⭐",like:"👍",share:"📤",download:"⬇️",upload:"⬆️",delete:"🗑️",edit:"✏️",add:"➕",minus:"➖",close:"✖️",check:"✅","arrow-left":"←","arrow-right":"→","arrow-up":"↑","arrow-down":"↓",refresh:"🔄",settings:"⚙️",menu:"☰",more:"⋯",phone:"📞",message:"💬",mail:"📧",location:"📍",time:"🕐",calendar:"📅",camera:"📷",image:"🖼️",video:"🎥",music:"🎵",file:"📄",folder:"📁",link:"🔗",lock:"🔒",unlock:"🔓",eye:"👁️","eye-off":"🙈",warning:"⚠️",error:"❌",info:"ℹ️",success:"✅"},x=a((()=>["custom-icon-base",{"icon-disabled":y.disabled,"icon-loading":y.loading,"icon-spin":y.spin,"icon-pulse":y.pulse},y.customClass])),z=a((()=>{const e={position:"relative",display:"inline-flex",alignItems:"center",justifyContent:"center",backgroundColor:y.backgroundColor,borderRadius:"number"==typeof y.borderRadius?`${y.borderRadius}px`:y.borderRadius};if("auto"!==y.width&&(e.width="number"==typeof y.width?`${y.width}px`:y.width),"auto"!==y.height&&(e.height="number"==typeof y.height?`${y.height}px`:y.height),"auto"===y.width&&"auto"===y.height){const t="number"==typeof y.size?`${y.size}px`:y.size;e.width=t,e.height=t}return y.disabled&&(e.opacity=.5,e.cursor="not-allowed"),e})),_=a((()=>[y.fontFamily,y.fontClass,y.prefix+y.name])),w=a((()=>({fontSize:"number"==typeof y.size?`${y.size}px`:y.size,color:y.color,fontFamily:y.fontFamily}))),k=a((()=>({width:"100%",height:"100%"}))),C=a((()=>({width:"100%",height:"100%",fill:y.color}))),S=a((()=>({fontSize:"number"==typeof y.size?`${y.size}px`:y.size,color:y.color}))),$=a((()=>({fontSize:"number"==typeof y.size?`${y.size}px`:y.size,lineHeight:1}))),F=a((()=>["badge-base",{"badge-dot":y.dot,"badge-text":!y.dot&&y.badge}])),I=a((()=>{const e={backgroundColor:y.badgeColor,color:y.badgeTextColor};if(y.dot){const t=Math.max(8,.5*Number(y.badgeSize));e.width=`${t}px`,e.height=`${t}px`}else e.fontSize=.75*Number(y.badgeSize)+"px",e.minWidth=`${y.badgeSize}px`,e.height=`${y.badgeSize}px`;return e})),j=a((()=>("font"===y.type&&y.name,""))),B=a((()=>y.unicode?String.fromCharCode(parseInt(y.unicode.replace(/^&#x?/,""),16)):"")),M=a((()=>v[y.name]||"❓")),R=a((()=>"number"==typeof y.badge&&y.badge>99?"99+":String(y.badge))),T=e=>{y.disabled||b("click",e)},H=e=>{m.value=!0,h.value=!1,b("load",e)},L=e=>{h.value=!0,m.value=!1,b("error",e)};return(e,t)=>{const a=c,g=f,y=p;return o(),l(y,{class:i(["custom-icon",x.value]),style:d(z.value),onClick:T},{default:s((()=>["font"===e.type?(o(),l(a,{key:0,class:i(["icon-font",_.value]),style:d(w.value)},{default:s((()=>[n(r(j.value),1)])),_:1},8,["class","style"])):"image"===e.type?(o(),l(g,{key:1,class:"icon-image",src:e.src,mode:e.imageMode,style:d(k.value),onLoad:H,onError:L},null,8,["src","mode","style"])):"svg"===e.type?(o(),l(y,{key:2,class:"icon-svg",style:d(C.value),innerHTML:e.svgContent},null,8,["style","innerHTML"])):"unicode"===e.type?(o(),l(a,{key:3,class:"icon-unicode",style:d(S.value)},{default:s((()=>[n(r(B.value),1)])),_:1},8,["style"])):(o(),l(a,{key:4,class:"icon-builtin",style:d($.value)},{default:s((()=>[n(r(M.value),1)])),_:1},8,["style"])),e.badge||e.dot?(o(),l(y,{key:5,class:i(["icon-badge",F.value]),style:d(I.value)},{default:s((()=>[e.badge&&!e.dot?(o(),l(a,{key:0,class:"badge-text"},{default:s((()=>[n(r(R.value),1)])),_:1})):u("",!0)])),_:1},8,["class","style"])):u("",!0)])),_:1},8,["class","style"])}}}),[["__scopeId","data-v-add9d15f"]]);export{y as I};
