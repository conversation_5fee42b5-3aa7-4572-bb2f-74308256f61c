import{y as e,z as l,D as a,H as t,P as o,o as u,c as d,w as s,a as n,j as r,b as c,t as i,l as f,i as v,e as m,f as g,A as _,B as b,x as C,W as k,k as w,r as p,F as x,I as y,S as h}from"./index-ChUEiI3E.js";import{n as S,C as P}from"./index.DI3nc_37.js";/* empty css                                                                               */import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";const V=T(e({__name:"VerificationCodeButton",props:{disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},countdown:{default:60},autoStart:{type:Boolean,default:!1},text:{default:"获取验证码"},loadingText:{default:"发送中..."},countdownFormat:{default:"{time}s后重新获取"},width:{default:"auto"},height:{default:36},backgroundColor:{default:"#007aff"},disabledBackgroundColor:{default:"#c8c9cc"},textColor:{default:"#ffffff"},disabledTextColor:{default:"#969799"},fontSize:{default:14},borderRadius:{default:4},borderWidth:{default:0},borderColor:{default:"transparent"},borderStyle:{default:"solid"},customClass:{default:""},customStyle:{default:()=>({})}},emits:["click","countdownStart","countdownEnd","countdownTick"],setup(e,{expose:_,emit:b}){const C=e,k=b,w=l(!1),p=l(!1),x=l(0);let y=null;const h=a((()=>C.disabled||w.value||p.value)),S=a((()=>C.text)),P=a((()=>C.countdownFormat.replace("{time}",x.value.toString()))),T=a((()=>{const e={width:"number"==typeof C.width?`${C.width}px`:C.width,height:"number"==typeof C.height?`${C.height}px`:C.height,fontSize:`${C.fontSize}px`,borderRadius:`${C.borderRadius}px`,borderWidth:`${C.borderWidth}px`,borderColor:C.borderColor,borderStyle:C.borderStyle,...C.customStyle};return h.value?e.backgroundColor=C.disabledBackgroundColor:e.backgroundColor=C.backgroundColor,e})),V=a((()=>({color:h.value?C.disabledTextColor:C.textColor}))),$=a((()=>({borderTopColor:h.value?C.disabledTextColor:C.textColor}))),B=()=>{w.value||(w.value=!0,x.value=C.countdown,k("countdownStart",C.countdown),y=setInterval((()=>{x.value--,k("countdownTick",x.value),x.value<=0&&R()}),1e3))},R=()=>{y&&(clearInterval(y),y=null),w.value=!1,x.value=0,k("countdownEnd")},z=()=>{h.value||(k("click"),C.autoStart&&B())};return t((()=>C.loading),(e=>{p.value=e})),o((()=>{y&&clearInterval(y)})),_({startCountdown:B,stopCountdown:R,setLoading:e=>{p.value=e},getState:()=>({isCounting:w.value,isLoading:p.value,remainingTime:x.value,isDisabled:h.value})}),(e,l)=>{const a=v,t=m,o=g;return u(),d(o,{class:f(["verification-code-button",{"is-disabled":h.value,"is-counting":w.value,"is-loading":p.value},e.customClass]),style:r(T.value),disabled:h.value,onClick:z},{default:s((()=>[p.value?(u(),d(a,{key:0,class:"loading-container"},{default:s((()=>[n(a,{class:"loading-spinner",style:r($.value)},null,8,["style"]),n(t,{class:"button-text",style:r(V.value)},{default:s((()=>[c(i(e.loadingText),1)])),_:1},8,["style"])])),_:1})):w.value?(u(),d(t,{key:1,class:"button-text",style:r(V.value)},{default:s((()=>[c(i(P.value),1)])),_:1},8,["style"])):(u(),d(t,{key:2,class:"button-text",style:r(V.value)},{default:s((()=>[c(i(S.value),1)])),_:1},8,["style"]))])),_:1},8,["class","style","disabled"])}}}),[["__scopeId","data-v-d7f81e1d"]]),$={countdown:60,text:"获取验证码",loadingText:"发送中...",countdownFormat:"{time}s后重新获取",width:"auto",height:36,backgroundColor:"#007aff",textColor:"#ffffff",fontSize:14,borderRadius:4},B={default:{backgroundColor:"#007aff",disabledBackgroundColor:"#c8c9cc",textColor:"#ffffff",disabledTextColor:"#969799"},success:{backgroundColor:"#07c160",disabledBackgroundColor:"#c8c9cc",textColor:"#ffffff",disabledTextColor:"#969799"},warning:{backgroundColor:"#ff976a",disabledBackgroundColor:"#c8c9cc",textColor:"#ffffff",disabledTextColor:"#969799"},danger:{backgroundColor:"#ee0a24",disabledBackgroundColor:"#c8c9cc",textColor:"#ffffff",disabledTextColor:"#969799"},plain:{backgroundColor:"transparent",disabledBackgroundColor:"transparent",textColor:"#007aff",disabledTextColor:"#c8c9cc",borderWidth:1,borderColor:"#007aff"},dark:{backgroundColor:"#1f2937",disabledBackgroundColor:"#4b5563",textColor:"#ffffff",disabledTextColor:"#9ca3af"}},R={small:{height:28,fontSize:12,borderRadius:3},default:{height:36,fontSize:14,borderRadius:4},large:{height:44,fontSize:16,borderRadius:6},xlarge:{height:52,fontSize:18,borderRadius:8}},z={login:{text:"获取验证码",countdown:60,countdownFormat:"{time}s后重新获取"},register:{text:"发送验证码",countdown:60,countdownFormat:"重新发送({time}s)"},resetPassword:{text:"获取验证码",countdown:120,countdownFormat:"{time}s后重新获取"},bindPhone:{text:"发送验证码",countdown:60,countdownFormat:"{time}s后重新发送"},changePassword:{text:"获取验证码",countdown:60,countdownFormat:"重新获取({time}s)"}},F={createPreset:(e="default",l="default",a="login")=>({...$,...B[e],...R[l],...z[a]}),validatePhone:e=>/^1[3-9]\d{9}$/.test(e),validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),formatCountdown:(e,l="{time}s后重新获取")=>l.replace("{time}",e.toString()),createAsyncHandler:(e,l={})=>async a=>{var t,o,u,d;try{null==(t=l.onStart)||t.call(l),null==a||a.setLoading(!0);const u=await e();null==(o=l.onSuccess)||o.call(l,u),null==a||a.startCountdown()}catch(s){null==(u=l.onError)||u.call(l,s)}finally{null==a||a.setLoading(!1),null==(d=l.onFinally)||d.call(l)}}},L=T(e({__name:"verification-code-demo",setup(e){const a=l(0),t=l(""),o=l(""),f=l(""),T=l(""),$=l(""),B=l(""),R=l(""),z=l(""),L=l(""),E=l(""),I=l(""),j=l(""),U=l(""),W=l(""),A=l(!1),D=l(),H=l(),J=l(),N=l(),O=l([]),q=F.createPreset("default","small"),G=F.createPreset("success","small"),K=F.createPreset("warning","small"),M=F.createPreset("danger","small"),Q=F.createPreset("plain","small"),X=F.createPreset("dark","small"),Y=F.createPreset("default","small"),Z=F.createPreset("default","default"),ee=F.createPreset("default","large"),le=F.createPreset("default","xlarge");_((()=>{a.value=S.getTotalHeight()}));const ae=()=>{var e;F.validatePhone(t.value)?(R.value=`向 ${t.value} 发送验证码`,null==(e=D.value)||e.startCountdown()):R.value="请输入正确的手机号"},te=()=>{F.validatePhone(o.value)?z.value=`向 ${o.value} 发送验证码（自动开始倒计时）`:z.value="请输入正确的手机号"},oe=()=>{F.validatePhone(f.value)?L.value=`向 ${f.value} 发送验证码（30秒倒计时）`:L.value="请输入正确的手机号"},ue=e=>{E.value=`点击了 ${e} 主题按钮`},de=e=>{I.value=`点击了 ${e} 尺寸按钮`},se=async()=>{var e;if(F.validatePhone(T.value)){A.value=!0,j.value="正在发送验证码...";try{await new Promise((e=>setTimeout(e,2e3))),j.value=`验证码已发送到 ${T.value}`,null==(e=H.value)||e.startCountdown()}catch(l){j.value="发送失败，请重试"}finally{A.value=!1}}else j.value="请输入正确的手机号"},ne=async()=>{var e,l;if(F.validatePhone($.value)){null==(e=J.value)||e.setLoading(!0),U.value="正在发送验证码...";try{await new Promise(((e,l)=>setTimeout((()=>l(new Error("网络错误"))),1500)))}catch(a){U.value="发送失败：网络错误"}finally{null==(l=J.value)||l.setLoading(!1)}}else U.value="请输入正确的手机号"},re=()=>{W.value="点击了手动控制按钮"},ce=()=>{var e;null==(e=N.value)||e.startCountdown(),W.value="手动开始倒计时"},ie=()=>{var e;null==(e=N.value)||e.stopCountdown(),W.value="手动停止倒计时"},fe=()=>{var e,l;const a=null==(e=N.value)?void 0:e.getState(),t=!(null==a?void 0:a.isLoading);null==(l=N.value)||l.setLoading(t),W.value=`设置加载状态: ${t}`},ve=()=>{var e;const l=null==(e=N.value)?void 0:e.getState();W.value=`当前状态: ${JSON.stringify(l)}`},me=(e,l)=>{const a=(new Date).toLocaleTimeString();O.value.push({time:a,event:e,data:"object"==typeof l?JSON.stringify(l):String(l)}),O.value.length>10&&O.value.shift()},ge=()=>{F.validatePhone(B.value)?me("click",`向 ${B.value} 发送验证码`):me("click","手机号格式错误")},_e=e=>{me("countdownStart",`开始倒计时 ${e} 秒`)},be=e=>{me("countdownTick",`剩余 ${e} 秒`)},Ce=()=>{me("countdownEnd","倒计时结束")};return(e,l)=>{const _=m,S=y,F=v,me=g,ke=h;return u(),d(F,{class:"page",style:r({paddingTop:a.value+"px"})},{default:s((()=>[n(b(P),{title:"验证码按钮演示","show-back":!0,"background-color":"#007aff",color:"#ffffff"}),n(ke,{"scroll-y":"",class:"content",style:r({height:`calc(100vh - ${a.value}px)`})},{default:s((()=>[n(F,{class:"demo-section"},{default:s((()=>[n(_,{class:"section-title"},{default:s((()=>[c("📱 基础用法")])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("默认样式:")])),_:1}),n(F,{class:"demo-row"},{default:s((()=>[n(S,{modelValue:t.value,"onUpdate:modelValue":l[0]||(l[0]=e=>t.value=e),placeholder:"请输入手机号",class:"demo-input"},null,8,["modelValue"]),n(b(V),{ref_key:"basicButtonRef",ref:D,onClick:ae},null,512)])),_:1}),R.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(R.value),1)])),_:1})):C("",!0)])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("自动开始倒计时:")])),_:1}),n(F,{class:"demo-row"},{default:s((()=>[n(S,{modelValue:o.value,"onUpdate:modelValue":l[1]||(l[1]=e=>o.value=e),placeholder:"请输入手机号",class:"demo-input"},null,8,["modelValue"]),n(b(V),{"auto-start":!0,onClick:te})])),_:1}),z.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(z.value),1)])),_:1})):C("",!0)])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("自定义倒计时:")])),_:1}),n(F,{class:"demo-row"},{default:s((()=>[n(S,{modelValue:f.value,"onUpdate:modelValue":l[2]||(l[2]=e=>f.value=e),placeholder:"请输入手机号",class:"demo-input"},null,8,["modelValue"]),n(b(V),{countdown:30,"countdown-format":"重新发送({time}s)",onClick:oe})])),_:1}),L.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(L.value),1)])),_:1})):C("",!0)])),_:1})])),_:1}),n(F,{class:"demo-section"},{default:s((()=>[n(_,{class:"section-title"},{default:s((()=>[c("🎨 样式主题")])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("不同主题:")])),_:1}),n(F,{class:"theme-grid"},{default:s((()=>[n(b(V),k(b(q),{text:"默认",onClick:l[3]||(l[3]=e=>ue("default"))}),null,16),n(b(V),k(b(G),{text:"成功",onClick:l[4]||(l[4]=e=>ue("success"))}),null,16),n(b(V),k(b(K),{text:"警告",onClick:l[5]||(l[5]=e=>ue("warning"))}),null,16),n(b(V),k(b(M),{text:"危险",onClick:l[6]||(l[6]=e=>ue("danger"))}),null,16),n(b(V),k(b(Q),{text:"朴素",onClick:l[7]||(l[7]=e=>ue("plain"))}),null,16),n(b(V),k(b(X),{text:"暗色",onClick:l[8]||(l[8]=e=>ue("dark"))}),null,16)])),_:1}),E.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(E.value),1)])),_:1})):C("",!0)])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("不同尺寸:")])),_:1}),n(F,{class:"size-list"},{default:s((()=>[n(b(V),k(b(Y),{text:"小尺寸",onClick:l[9]||(l[9]=e=>de("small"))}),null,16),n(b(V),k(b(Z),{text:"默认尺寸",onClick:l[10]||(l[10]=e=>de("default"))}),null,16),n(b(V),k(b(ee),{text:"大尺寸",onClick:l[11]||(l[11]=e=>de("large"))}),null,16),n(b(V),k(b(le),{text:"超大尺寸",onClick:l[12]||(l[12]=e=>de("xlarge"))}),null,16)])),_:1}),I.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(I.value),1)])),_:1})):C("",!0)])),_:1})])),_:1}),n(F,{class:"demo-section"},{default:s((()=>[n(_,{class:"section-title"},{default:s((()=>[c("⚡ 异步操作")])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("模拟API调用:")])),_:1}),n(F,{class:"demo-row"},{default:s((()=>[n(S,{modelValue:T.value,"onUpdate:modelValue":l[13]||(l[13]=e=>T.value=e),placeholder:"请输入手机号",class:"demo-input"},null,8,["modelValue"]),n(b(V),{ref_key:"asyncButtonRef",ref:H,loading:A.value,"loading-text":"发送中...",onClick:se},null,8,["loading"])])),_:1}),j.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(j.value),1)])),_:1})):C("",!0)])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("错误处理:")])),_:1}),n(F,{class:"demo-row"},{default:s((()=>[n(S,{modelValue:$.value,"onUpdate:modelValue":l[14]||(l[14]=e=>$.value=e),placeholder:"请输入手机号",class:"demo-input"},null,8,["modelValue"]),n(b(V),{ref_key:"errorButtonRef",ref:J,onClick:ne},null,512)])),_:1}),U.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(U.value),1)])),_:1})):C("",!0)])),_:1})])),_:1}),n(F,{class:"demo-section"},{default:s((()=>[n(_,{class:"section-title"},{default:s((()=>[c("🎮 控制方法")])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("手动控制:")])),_:1}),n(F,{class:"control-area"},{default:s((()=>[n(b(V),{ref_key:"controlButtonRef",ref:N,text:"手动控制",onClick:re},null,512),n(F,{class:"control-buttons"},{default:s((()=>[n(me,{onClick:ce,class:"control-btn"},{default:s((()=>[c("开始倒计时")])),_:1}),n(me,{onClick:ie,class:"control-btn"},{default:s((()=>[c("停止倒计时")])),_:1}),n(me,{onClick:fe,class:"control-btn"},{default:s((()=>[c("设置加载")])),_:1}),n(me,{onClick:ve,class:"control-btn"},{default:s((()=>[c("获取状态")])),_:1})])),_:1})])),_:1}),W.value?(u(),d(_,{key:0,class:"demo-result"},{default:s((()=>[c(i(W.value),1)])),_:1})):C("",!0)])),_:1})])),_:1}),n(F,{class:"demo-section"},{default:s((()=>[n(_,{class:"section-title"},{default:s((()=>[c("📡 事件监听")])),_:1}),n(F,{class:"demo-card"},{default:s((()=>[n(_,{class:"demo-label"},{default:s((()=>[c("事件日志:")])),_:1}),n(F,{class:"demo-row"},{default:s((()=>[n(S,{modelValue:B.value,"onUpdate:modelValue":l[15]||(l[15]=e=>B.value=e),placeholder:"请输入手机号",class:"demo-input"},null,8,["modelValue"]),n(b(V),{"auto-start":!0,onClick:ge,onCountdownStart:_e,onCountdownTick:be,onCountdownEnd:Ce})])),_:1}),O.value.length>0?(u(),d(ke,{key:0,"scroll-y":"",class:"event-logs"},{default:s((()=>[(u(!0),w(x,null,p(O.value,((e,l)=>(u(),d(F,{key:l,class:"event-log"},{default:s((()=>[n(_,{class:"event-time"},{default:s((()=>[c(i(e.time),1)])),_:2},1024),n(_,{class:"event-name"},{default:s((()=>[c(i(e.event),1)])),_:2},1024),n(_,{class:"event-data"},{default:s((()=>[c(i(e.data),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):C("",!0)])),_:1})])),_:1})])),_:1},8,["style"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-f760ebc9"]]);export{L as default};
