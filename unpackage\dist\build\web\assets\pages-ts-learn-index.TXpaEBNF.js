import{s as e,n as t,g as s,h as a,c as l,w as i,i as c,o as n,a as o,b as d,j as r,t as p,k as u,r as g,F as f,e as m,f as h,l as _}from"./index-ChUEiI3E.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const y=k({data:()=>({learningItems:[{id:"basic-types",title:"基础类型",description:"学习 TypeScript 的基本数据类型：string、number、boolean、array 等",path:"/pages/ts-learn/basic-types",tags:["基础","必学"],duration:"15分钟",completed:!1,locked:!1,difficulty:"beginner"},{id:"interfaces",title:"接口 (Interface)",description:"深入理解接口的定义、使用、继承和可选属性",path:"/pages/ts-learn/interfaces",tags:["核心概念","重要"],duration:"20分钟",completed:!1,locked:!1,difficulty:"beginner"},{id:"type-aliases",title:"类型别名 (Type)",description:"掌握 type 关键字、联合类型、交叉类型的使用",path:"/pages/ts-learn/type-aliases",tags:["类型系统","进阶"],duration:"18分钟",completed:!1,locked:!1,difficulty:"intermediate"},{id:"functions",title:"函数类型",description:"学习函数参数、返回值、重载的类型定义",path:"/pages/ts-learn/functions",tags:["函数","实用"],duration:"25分钟",completed:!1,locked:!1,difficulty:"intermediate"},{id:"generics",title:"泛型 (Generics)",description:"理解泛型的概念、约束和在实际开发中的应用",path:"/pages/ts-learn/generics",tags:["高级特性","重要"],duration:"30分钟",completed:!1,locked:!1,difficulty:"intermediate"},{id:"classes",title:"类和继承",description:"掌握 TypeScript 中的类、继承、访问修饰符等面向对象特性",path:"/pages/ts-learn/classes",tags:["面向对象","OOP"],duration:"25分钟",completed:!1,locked:!1,difficulty:"intermediate"},{id:"modules",title:"模块和命名空间",description:"学习模块的导入导出、命名空间的使用和模块化开发",path:"/pages/ts-learn/modules",tags:["模块化","工程化"],duration:"20分钟",completed:!1,locked:!1,difficulty:"intermediate"},{id:"advanced-types",title:"高级类型",description:"掌握条件类型、映射类型、工具类型等高级概念",path:"/pages/ts-learn/advanced-types",tags:["高级","进阶"],duration:"35分钟",completed:!1,locked:!1,difficulty:"advanced"},{id:"practical-cases",title:"实战案例",description:"通过实际项目案例学习 TypeScript 的最佳实践",path:"/pages/ts-learn/practical-cases",tags:["实战","项目"],duration:"40分钟",completed:!1,locked:!1,difficulty:"advanced"}],completedCount:0,totalCount:9,progress:0}),computed:{progressPercentage(){return Math.round(this.completedCount/this.totalCount*100)}},methods:{navigateToLesson(s){s.locked?e({title:"请先完成前面的课程",icon:"none"}):t({url:s.path})},navigateTo(e){t({url:e})},updateProgress(){this.completedCount=this.learningItems.filter((e=>e.completed)).length,this.progress=Math.round(this.completedCount/this.totalCount*100);for(let e=0;e<this.learningItems.length;e++)this.learningItems[e].locked=0!==e&&!this.learningItems[e-1].completed},markAsCompleted(e){const t=this.learningItems.find((t=>t.id===e));t&&(t.completed=!0,this.updateProgress(),s("ts-learn-progress",{completedItems:this.learningItems.filter((e=>e.completed)).map((e=>e.id)),lastUpdate:Date.now()}))}},onLoad(){try{const e=a("ts-learn-progress");e&&e.completedItems&&(e.completedItems.forEach((e=>{const t=this.learningItems.find((t=>t.id===e));t&&(t.completed=!0)})),this.updateProgress())}catch(e){console.error("恢复学习进度失败:",e)}},onShow(){this.updateProgress()}},[["render",function(e,t,s,a,k,y){const C=m,b=c,I=h;return n(),l(b,{class:"container"},{default:i((()=>[o(b,{class:"header"},{default:i((()=>[o(C,{class:"title"},{default:i((()=>[d("TypeScript 学习指南")])),_:1}),o(C,{class:"subtitle"},{default:i((()=>[d("从基础到进阶，全面掌握 TypeScript")])),_:1})])),_:1}),o(b,{class:"progress-section"},{default:i((()=>[o(C,{class:"section-title"},{default:i((()=>[d("学习进度")])),_:1}),o(b,{class:"progress-bar"},{default:i((()=>[o(b,{class:"progress-fill",style:r({width:k.progress+"%"})},null,8,["style"])])),_:1}),o(C,{class:"progress-text"},{default:i((()=>[d(p(k.completedCount)+"/"+p(k.totalCount)+" 已完成 ("+p(k.progress)+"%)",1)])),_:1})])),_:1}),o(b,{class:"content"},{default:i((()=>[o(b,{class:"learning-path"},{default:i((()=>[o(C,{class:"section-title"},{default:i((()=>[d("学习路径")])),_:1}),(n(!0),u(f,null,g(k.learningItems,((e,t)=>(n(),l(b,{key:e.id,class:_(["learning-item",{completed:e.completed,locked:e.locked}]),onClick:t=>y.navigateToLesson(e)},{default:i((()=>[o(b,{class:"item-left"},{default:i((()=>[o(b,{class:"step-number"},{default:i((()=>[d(p(t+1),1)])),_:2},1024),o(b,{class:"item-content"},{default:i((()=>[o(C,{class:"item-title"},{default:i((()=>[d(p(e.title),1)])),_:2},1024),o(C,{class:"item-desc"},{default:i((()=>[d(p(e.description),1)])),_:2},1024),o(b,{class:"item-tags"},{default:i((()=>[(n(!0),u(f,null,g(e.tags,(e=>(n(),l(C,{key:e,class:"tag"},{default:i((()=>[d(p(e),1)])),_:2},1024)))),128))])),_:2},1024)])),_:2},1024)])),_:2},1024),o(b,{class:"item-right"},{default:i((()=>[o(b,{class:"status-icon"},{default:i((()=>[e.completed?(n(),l(C,{key:0,class:"icon-completed"},{default:i((()=>[d("✓")])),_:1})):e.locked?(n(),l(C,{key:1,class:"icon-locked"},{default:i((()=>[d("🔒")])),_:1})):(n(),l(C,{key:2,class:"icon-arrow"},{default:i((()=>[d("→")])),_:1}))])),_:2},1024),o(C,{class:"duration"},{default:i((()=>[d(p(e.duration),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),o(b,{class:"quick-access"},{default:i((()=>[o(C,{class:"section-title"},{default:i((()=>[d("快速访问")])),_:1}),o(b,{class:"quick-buttons"},{default:i((()=>[o(I,{onClick:t[0]||(t[0]=e=>y.navigateTo("/pages/ts-learn/cheatsheet")),class:"quick-btn"},{default:i((()=>[d(" 📋 速查表 ")])),_:1}),o(I,{onClick:t[1]||(t[1]=e=>y.navigateTo("/pages/ts-learn/playground")),class:"quick-btn"},{default:i((()=>[d(" 🎮 在线练习 ")])),_:1}),o(I,{onClick:t[2]||(t[2]=e=>y.navigateTo("/pages/ts-learn/examples")),class:"quick-btn"},{default:i((()=>[d(" 💡 实战案例 ")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-75daecea"]]);export{y as default};
