// 全局类型定义文件
declare namespace App {
  // 用户信息接口
  interface UserInfo {
    id: number
    name: string
    email: string
    avatar?: string
    isActive: boolean
    createTime: string
    updateTime?: string
  }
  
  // API 响应接口
  interface ApiResponse<T = any> {
    code: number
    message: string
    data: T
    timestamp: number
  }
  
  // 分页数据接口
  interface PageData<T> {
    list: T[]
    total: number
    pageNum: number
    pageSize: number
  }
  
  // 表单验证规则
  interface ValidationRule {
    required?: boolean
    pattern?: RegExp
    message: string
    validator?: (value: any) => boolean
  }
  
  // 菜单项接口
  interface MenuItem {
    id: string
    title: string
    icon?: string
    path?: string
    children?: MenuItem[]
  }
  
  // 文件上传接口
  interface UploadFile {
    name: string
    url: string
    size: number
    type: string
  }
  
  // 地址信息接口
  interface AddressInfo {
    province: string
    city: string
    district: string
    detail: string
    longitude?: number
    latitude?: number
  }
  
  // 订单状态枚举
  enum OrderStatus {
    PENDING = 1,
    PAID = 2,
    SHIPPED = 3,
    DELIVERED = 4,
    CANCELLED = 5
  }
  
  // 用户状态枚举
  enum UserStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    PENDING = 'pending',
    BANNED = 'banned'
  }
  
  // 支付方式枚举
  enum PaymentMethod {
    WECHAT = 'wechat',
    ALIPAY = 'alipay',
    BANK_CARD = 'bank_card',
    BALANCE = 'balance'
  }
}

// 扩展 Vue 实例类型
declare module 'vue/types/vue' {
  interface Vue {
    $utils: {
      formatDate: (date: Date | string, format?: string) => string
      getTimeAgo: (date: Date | string) => string
      showLoading: (title?: string) => void
      hideLoading: () => void
      showToast: (title: string, icon?: 'success' | 'error' | 'none') => void
      showModal: (title: string, content: string) => Promise<boolean>
      navigateTo: (url: string) => void
      navigateBack: (delta?: number) => void
      setStorage: (key: string, value: any) => void
      getStorage: <T>(key: string, defaultValue?: T) => T | null
      removeStorage: (key: string) => void
      isEmail: (email: string) => boolean
      isPhone: (phone: string) => boolean
      isIdCard: (idCard: string) => boolean
    }
  }
}

// 扩展全局对象
declare global {
  interface Window {
    // 微信小程序相关
    wx?: any
    // 支付宝小程序相关
    my?: any
    // App 相关
    plus?: any
  }
  
  // 环境变量
  const process: {
    env: {
      NODE_ENV: 'development' | 'production'
      UNI_PLATFORM: string
    }
  }
}

// 组件 Props 类型
declare namespace ComponentProps {
  // 按钮组件 Props
  interface ButtonProps {
    type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
    size?: 'small' | 'medium' | 'large'
    disabled?: boolean
    loading?: boolean
    block?: boolean
  }

  // 输入框组件 Props
  interface InputProps {
    value: string
    placeholder?: string
    type?: 'text' | 'number' | 'password' | 'email' | 'tel'
    disabled?: boolean
    readonly?: boolean
    maxlength?: number
    rules?: App.ValidationRule[]
  }

  // 列表项组件 Props
  interface ListItemProps {
    title: string
    subtitle?: string
    icon?: string
    rightText?: string
    rightIcon?: string
    clickable?: boolean
    border?: boolean
  }

  // 抽屉组件 Props
  interface DrawerProps {
    /** 是否显示抽屉 */
    visible?: boolean
    /** 是否需要遮罩层 */
    mask?: boolean
    /** 遮罩是否可点击关闭 */
    maskClosable?: boolean
    /** 抽屉弹出方向 */
    mode?: 'left' | 'right' | 'top' | 'bottom'
    /** 抽屉宽度（left/right模式时有效） */
    width?: string
    /** 抽屉高度（top/bottom模式时有效） */
    height?: string
    /** 抽屉 z-index 值 */
    zIndex?: number | string
    /** 遮罩 z-index 值 */
    maskZIndex?: number | string
    /** 抽屉背景色 */
    backgroundColor?: string
    /** 遮罩背景色 */
    maskColor?: string
    /** 圆角大小 */
    borderRadius?: string
    /** 动画持续时间（毫秒） */
    duration?: number
    /** 是否显示关闭按钮 */
    showClose?: boolean
    /** 关闭按钮位置 */
    closePosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  }

  // 抽屉组件事件
  interface DrawerEvents {
    /** 抽屉关闭事件 */
    close: () => void
    /** 抽屉打开事件 */
    open: () => void
    /** 抽屉打开完成事件 */
    opened: () => void
    /** 抽屉关闭完成事件 */
    closed: () => void
  }
}

// 事件类型定义
declare namespace EventTypes {
  // 通用事件
  interface BaseEvent {
    type: string
    timestamp: number
  }
  
  // 用户事件
  interface UserEvent extends BaseEvent {
    userId: number
    action: 'login' | 'logout' | 'register' | 'update_profile'
  }
  
  // 页面事件
  interface PageEvent extends BaseEvent {
    page: string
    action: 'view' | 'leave' | 'share'
  }
  
  // 商品事件
  interface ProductEvent extends BaseEvent {
    productId: number
    action: 'view' | 'add_cart' | 'purchase' | 'favorite'
  }
}

export {}
