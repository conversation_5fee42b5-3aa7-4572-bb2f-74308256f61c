import{m as e,n as t,h as s,g as a,c as l,w as n,i as r,o as c,a as o,b as i,f as u,e as d,S as p}from"./index-ChUEiI3E.js";import{_ as f}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=f({methods:{goBack(){e()},previousLesson(){t({url:"/pages/ts-learn/functions"})},nextLesson(){this.markAsCompleted(),t({url:"/pages/ts-learn/classes"})},markAsCompleted(){try{let e=s("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("generics")||(e.completedItems.push("generics"),e.lastUpdate=Date.now(),a("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("泛型课程加载完成")}},[["render",function(e,t,s,a,f,g){const m=u,_=d,x=r,b=p;return c(),l(x,{class:"container"},{default:n((()=>[o(x,{class:"header"},{default:n((()=>[o(m,{onClick:g.goBack,class:"back-btn"},{default:n((()=>[i("← 返回")])),_:1},8,["onClick"]),o(_,{class:"title"},{default:n((()=>[i("泛型 (Generics)")])),_:1}),o(x,{class:"progress-indicator"},{default:n((()=>[i("4/9")])),_:1})])),_:1}),o(b,{"scroll-y":"",class:"content"},{default:n((()=>[o(x,{class:"lesson-intro"},{default:n((()=>[o(_,{class:"intro-title"},{default:n((()=>[i("学习目标")])),_:1}),o(_,{class:"intro-text"},{default:n((()=>[i("理解泛型的概念和作用，掌握泛型函数、泛型接口、泛型类的使用方法")])),_:1})])),_:1}),o(x,{class:"section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("🔧 泛型基础概念")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i('// 不使用泛型的问题 function identity(arg: any): any { return arg } // 使用泛型的解决方案 function identityGeneric<T>(arg: T): T { return arg } // 使用泛型函数 let output1 = identityGeneric<string>("hello") // string let output2 = identityGeneric<number>(42) // number let output3 = identityGeneric("world") // 类型推断为 string')])),_:1})])),_:1}),o(x,{class:"explanation"},{default:n((()=>[o(_,{class:"exp-text"},{default:n((()=>[i("• 泛型允许在定义时不指定具体类型")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 使用 <T> 语法定义类型参数")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 保持类型安全的同时提供灵活性")])),_:1})])),_:1})])),_:1}),o(x,{class:"section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("⚡ 泛型函数")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i('// 单个类型参数 function swap<T>(a: T, b: T): [T, T] { return [b, a] } // 多个类型参数 function pair<T, U>(first: T, second: U): [T, U] { return [first, second] } // 使用示例 const [x, y] = swap(1, 2) // [2, 1] const [name, age] = pair("张三", 25) // ["张三", 25] // 泛型箭头函数 const getFirst = <T>(arr: T[]): T | undefined => { return arr[0] } const firstNumber = getFirst([1, 2, 3]) // number | undefined const firstName = getFirst(["a", "b", "c"]) // string | undefined')])),_:1})])),_:1}),o(x,{class:"explanation"},{default:n((()=>[o(_,{class:"exp-text"},{default:n((()=>[i("• 泛型函数可以处理多种类型的数据")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 支持多个类型参数")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• TypeScript 可以自动推断类型")])),_:1})])),_:1})])),_:1}),o(x,{class:"section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("📋 泛型接口")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i('// 泛型接口定义 interface Container<T> { value: T getValue(): T setValue(value: T): void } // 实现泛型接口 class Box<T> implements Container<T> { constructor(public value: T) {} getValue(): T { return this.value } setValue(value: T): void { this.value = value } } // 使用泛型接口 const stringBox = new Box<string>("hello") const numberBox = new Box<number>(42) // 泛型函数接口 interface Comparator<T> { (a: T, b: T): number } const numberComparator: Comparator<number> = (a, b) => a - b const stringComparator: Comparator<string> = (a, b) => a.localeCompare(b)')])),_:1})])),_:1}),o(x,{class:"explanation"},{default:n((()=>[o(_,{class:"exp-text"},{default:n((()=>[i("• 接口可以使用泛型参数")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 实现时需要指定具体类型")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 提供类型安全的抽象")])),_:1})])),_:1})])),_:1}),o(x,{class:"section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("🏗️ 泛型类")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i('// 泛型类定义 class Stack<T> { private items: T[] = [] push(item: T): void { this.items.push(item) } pop(): T | undefined { return this.items.pop() } peek(): T | undefined { return this.items[this.items.length - 1] } isEmpty(): boolean { return this.items.length === 0 } size(): number { return this.items.length } } // 使用泛型类 const numberStack = new Stack<number>() numberStack.push(1) numberStack.push(2) console.log(numberStack.pop()) // 2 const stringStack = new Stack<string>() stringStack.push("hello") stringStack.push("world") console.log(stringStack.peek()) // "world"')])),_:1})])),_:1}),o(x,{class:"explanation"},{default:n((()=>[o(_,{class:"exp-text"},{default:n((()=>[i("• 类可以使用泛型参数")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 类的所有成员都可以使用泛型类型")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 创建实例时指定具体类型")])),_:1})])),_:1})])),_:1}),o(x,{class:"section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("🔒 泛型约束")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i('// 基本约束：T 必须有 length 属性 interface Lengthwise { length: number } function logLength<T extends Lengthwise>(arg: T): T { console.log(arg.length) return arg } logLength("hello") // OK: string 有 length logLength([1, 2, 3]) // OK: array 有 length // logLength(123) // Error: number 没有 length // 使用 keyof 约束 function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] { return obj[key] } const person = { name: "张三", age: 25, city: "北京" } const name = getProperty(person, "name") // string const age = getProperty(person, "age") // number // const invalid = getProperty(person, "salary") // Error // 条件约束 type NonNullable<T> = T extends null | undefined ? never : T type Example1 = NonNullable<string | null> // string type Example2 = NonNullable<number | undefined> // number')])),_:1})])),_:1}),o(x,{class:"explanation"},{default:n((()=>[o(_,{class:"exp-text"},{default:n((()=>[i("• 使用 extends 关键字添加约束")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• keyof 操作符获取对象的键类型")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 约束确保类型参数满足特定条件")])),_:1})])),_:1})])),_:1}),o(x,{class:"section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("🛠️ 内置工具类型")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i('// 常用工具类型 type User = { id: number name: string email: string age: number } // Partial<T> - 所有属性变为可选 type PartialUser = Partial<User> // 等价于: { id?: number, name?: string, email?: string, age?: number } // Required<T> - 所有属性变为必需 type RequiredUser = Required<PartialUser> // Pick<T, K> - 选择特定属性 type UserBasic = Pick<User, "id" | "name"> // 等价于: { id: number, name: string } // Omit<T, K> - 排除特定属性 type UserWithoutId = Omit<User, "id"> // 等价于: { name: string, email: string, age: number } // Record<K, T> - 创建键值对类型 type UserRoles = Record<"admin" | "user" | "guest", boolean> // 等价于: { admin: boolean, user: boolean, guest: boolean }')])),_:1})])),_:1}),o(x,{class:"explanation"},{default:n((()=>[o(_,{class:"exp-text"},{default:n((()=>[i("• TypeScript 提供了许多内置工具类型")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 这些工具类型基于泛型实现")])),_:1}),o(_,{class:"exp-text"},{default:n((()=>[i("• 大大简化了类型操作")])),_:1})])),_:1})])),_:1}),o(x,{class:"practice-section"},{default:n((()=>[o(_,{class:"section-title"},{default:n((()=>[i("🎯 实践练习")])),_:1}),o(x,{class:"practice-item"},{default:n((()=>[o(_,{class:"practice-title"},{default:n((()=>[i("练习 1：创建泛型函数")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i("// 创建一个泛型函数 findItem // 功能：在数组中查找满足条件的第一个元素 // 参数：数组和判断函数 // 返回：找到的元素或 undefined")])),_:1})])),_:1})])),_:1}),o(x,{class:"practice-item"},{default:n((()=>[o(_,{class:"practice-title"},{default:n((()=>[i("练习 2：泛型类实现")])),_:1}),o(x,{class:"code-block"},{default:n((()=>[o(_,{class:"code"},{default:n((()=>[i("// 实现一个泛型 Queue 类 // 包含方法：enqueue(入队)、dequeue(出队)、front(查看队首)、isEmpty、size")])),_:1})])),_:1})])),_:1})])),_:1}),o(x,{class:"navigation"},{default:n((()=>[o(m,{onClick:g.previousLesson,class:"nav-btn secondary"},{default:n((()=>[i("上一课：函数类型")])),_:1},8,["onClick"]),o(m,{onClick:g.nextLesson,class:"nav-btn primary"},{default:n((()=>[i("下一课：类和继承")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-b127604d"]]);export{g as default};
