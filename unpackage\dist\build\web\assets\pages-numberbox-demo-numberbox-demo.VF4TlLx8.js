import{y as e,z as l,H as a,D as o,o as t,c as u,w as d,a as s,l as n,j as r,b as c,t as m,e as i,i as f,I as b,A as p,B as v,k as _,r as h,F as g,s as V,f as x,S as y,d as C}from"./index-ChUEiI3E.js";import{_ as k}from"./product.q7Ixu4ET.js";import{n as U,C as w}from"./index.DI3nc_37.js";import"./index.BAv8G1Ca.js";import{_ as B}from"./_plugin-vue_export-helper.BCo6x5W8.js";const $=B(e({__name:"NumberBox",props:{value:{default:0},modelValue:{default:0},min:{default:0},max:{default:99999},step:{default:1},precision:{default:0},width:{default:"auto"},height:{default:60},backgroundColor:{default:"#ffffff"},borderColor:{default:"#e5e5e5"},borderRadius:{default:8},btnWidth:{default:60},btnHeight:{default:60},btnBackgroundColor:{default:"#f8f9fa"},btnBorderColor:{default:"#e5e5e5"},color:{default:"#333333"},inputBackgroundColor:{default:"#ffffff"},inputBorderColor:{default:"#e5e5e5"},inputColor:{default:"#333333"},minusText:{default:"-"},plusText:{default:"+"},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},disabledColor:{default:"#cccccc"},placeholder:{default:""},longpress:{type:Boolean,default:!0},longpressTime:{default:600},customClass:{default:""},customStyle:{default:()=>({})},formatter:{type:Function,default:void 0},parser:{type:Function,default:void 0}},emits:["update:modelValue","update:value","change","input","focus","blur","plus","minus","max","min"],setup(e,{emit:p}){const v=e,_=p,h=l(0),g=l(!1),V=l(null),x=l(null),y=l(null);a((()=>v.modelValue??v.value),(e=>{const l=I(e);l!==h.value&&(h.value=l)}),{immediate:!0});const C=o((()=>["number-box-base",{"number-box-disabled":v.disabled,"number-box-readonly":v.readonly},v.customClass])),k=o((()=>({width:"number"==typeof v.width?`${v.width}px`:v.width,height:"number"==typeof v.height?`${v.height}px`:v.height,backgroundColor:v.backgroundColor,borderColor:v.borderColor,borderRadius:"number"==typeof v.borderRadius?`${v.borderRadius}px`:v.borderRadius,...v.customStyle}))),U=o((()=>["btn-base",{"btn-disabled":H.value}])),w=o((()=>["btn-base",{"btn-disabled":W.value}])),B=o((()=>["input-base",{"input-focused":g.value,"input-disabled":v.disabled}])),$=o((()=>({width:"number"==typeof v.btnWidth?`${v.btnWidth}px`:v.btnWidth,height:"number"==typeof v.btnHeight?`${v.btnHeight}px`:v.btnHeight,backgroundColor:H.value?v.disabledColor:v.btnBackgroundColor,borderColor:v.btnBorderColor}))),T=o((()=>({width:"number"==typeof v.btnWidth?`${v.btnWidth}px`:v.btnWidth,height:"number"==typeof v.btnHeight?`${v.btnHeight}px`:v.btnHeight,backgroundColor:W.value?v.disabledColor:v.btnBackgroundColor,borderColor:v.btnBorderColor}))),F=o((()=>({backgroundColor:v.inputBackgroundColor,borderColor:v.inputBorderColor,color:v.disabled?v.disabledColor:v.inputColor}))),H=o((()=>v.disabled||h.value<=v.min)),W=o((()=>v.disabled||h.value>=v.max)),j=o((()=>v.formatter&&"function"==typeof v.formatter?v.formatter(h.value):h.value.toString())),I=e=>{let l=Number(e)||0;return l=v.precision>0?Number(l.toFixed(v.precision)):Math.round(l),l=Math.max(v.min,Math.min(v.max,l)),l},M=e=>{const l=h.value,a=I(e);a!==l&&(h.value=a,_("update:modelValue",a),_("update:value",a),_("change",a,l),_("input",a),a===v.max&&_("max",a),a===v.min&&_("min",a))},R=()=>{if(W.value)return;const e=h.value+v.step;M(e),_("plus",h.value)},S=()=>{if(H.value)return;const e=h.value-v.step;M(e),_("minus",h.value)},N=e=>{let l=e.detail.value;l=v.parser&&"function"==typeof v.parser?v.parser(l):Number(l)||0,M(l)},D=e=>{g.value=!1,M(h.value),_("blur",e)},q=e=>{v.disabled||(g.value=!0,_("focus",e))},z=e=>{v.longpress&&(v.disabled||"plus"===e&&W.value||"minus"===e&&H.value||(y.value=e,V.value=setTimeout((()=>{L()}),v.longpressTime)))},A=()=>{V.value&&(clearTimeout(V.value),V.value=null),x.value&&(clearInterval(x.value),x.value=null),y.value=null},L=()=>{y.value&&(x.value=setInterval((()=>{"plus"===y.value?R():"minus"===y.value&&S()}),100))};return(()=>{const e=v.modelValue??v.value;h.value=I(e)})(),(e,l)=>{const a=i,o=f,p=b;return t(),u(o,{class:n(["number-box",C.value]),style:r(k.value)},{default:d((()=>[s(o,{class:n(["number-btn number-btn-minus",U.value]),style:r($.value),onClick:S,onTouchstart:l[0]||(l[0]=e=>z("minus")),onTouchend:A},{default:d((()=>[s(a,{class:"btn-text",style:r({color:H.value?e.disabledColor:e.color})},{default:d((()=>[c(m(e.minusText),1)])),_:1},8,["style"])])),_:1},8,["class","style"]),e.readonly?(t(),u(o,{key:1,class:n(["number-display",B.value]),style:r(F.value)},{default:d((()=>[s(a,{style:r({color:e.disabled?e.disabledColor:e.inputColor})},{default:d((()=>[c(m(j.value),1)])),_:1},8,["style"])])),_:1},8,["class","style"])):(t(),u(p,{key:0,class:n(["number-input",B.value]),style:r(F.value),type:"number",value:j.value,disabled:e.disabled,placeholder:e.placeholder,onInput:N,onBlur:D,onFocus:q},null,8,["class","style","value","disabled","placeholder"])),s(o,{class:n(["number-btn number-btn-plus",w.value]),style:r(T.value),onClick:R,onTouchstart:l[1]||(l[1]=e=>z("plus")),onTouchend:A},{default:d((()=>[s(a,{class:"btn-text",style:r({color:W.value?e.disabledColor:e.color})},{default:d((()=>[c(m(e.plusText),1)])),_:1},8,["style"])])),_:1},8,["class","style"])])),_:1},8,["class","style"])}}}),[["__scopeId","data-v-1fa92144"]]),T=B(e({__name:"numberbox-demo",setup(e){const a=l(0),o=l(0),n=l(5),b=l(2.5),B=l(1),T=l(3),F=l(2),H=l(1),W=l(5),j=l(8),I=l(3),M=l(!1),R=l(!1),S=l(99.99),N=l(75.5),D=l(4.5),q=l(1),z=l(5),A=l(2),L=l(10),P=l(20),E=l(30),G=l(5),J=l([]),K=l(1),O=l(5),Q=l(299.99);p((()=>{a.value=U.getTotalHeight()}));const X=e=>`¥${e.toFixed(2)}`,Y=e=>parseFloat(e.replace(/¥/g,""))||0,Z=e=>`${e.toFixed(1)}%`,ee=e=>parseFloat(e.replace(/%/g,""))||0,le=e=>`${e.toFixed(1)}分`,ae=(e,l)=>{console.log("值改变:",e,"原值:",l)},oe=e=>{V({title:`已达到最小值: ${e}`,icon:"none"})},te=e=>{V({title:`已达到最大值: ${e}`,icon:"none"})},ue=()=>{M.value=!M.value},de=()=>{R.value=!R.value},se=e=>{const l=(new Date).toLocaleTimeString();J.value.unshift(`[${l}] ${e}`),J.value.length>10&&(J.value=J.value.slice(0,10))},ne=(e,l)=>{se(`值改变: ${l} → ${e}`)},re=e=>{se(`点击增加按钮，当前值: ${e}`)},ce=e=>{se(`点击减少按钮，当前值: ${e}`)},me=()=>{se("输入框获得焦点")},ie=()=>{se("输入框失去焦点")};return(e,l)=>{const p=i,V=f,U=x,se=y,fe=C;return t(),u(V,{class:"page"},{default:d((()=>[s(v(w),{title:"数字框组件演示","show-back":!0}),s(se,{"scroll-y":"",class:"content",style:r({paddingTop:a.value+"px"})},{default:d((()=>[s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("📝 基础用法")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("默认样式:")])),_:1}),s(v($),{modelValue:o.value,"onUpdate:modelValue":l[0]||(l[0]=e=>o.value=e),onChange:ae},null,8,["modelValue"]),s(p,{class:"demo-value"},{default:d((()=>[c("值: "+m(o.value),1)])),_:1})])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("设置范围 (1-10):")])),_:1}),s(v($),{modelValue:n.value,"onUpdate:modelValue":l[1]||(l[1]=e=>n.value=e),min:1,max:10,onMin:oe,onMax:te},null,8,["modelValue"]),s(p,{class:"demo-value"},{default:d((()=>[c("值: "+m(n.value),1)])),_:1})])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("步长为 0.5:")])),_:1}),s(v($),{modelValue:b.value,"onUpdate:modelValue":l[2]||(l[2]=e=>b.value=e),step:.5,precision:1,min:0,max:10},null,8,["modelValue"]),s(p,{class:"demo-value"},{default:d((()=>[c("值: "+m(b.value),1)])),_:1})])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("🎨 样式定制")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("自定义尺寸:")])),_:1}),s(v($),{modelValue:B.value,"onUpdate:modelValue":l[3]||(l[3]=e=>B.value=e),width:240,height:80,"btn-width":80,"btn-height":80},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("自定义颜色:")])),_:1}),s(v($),{modelValue:T.value,"onUpdate:modelValue":l[4]||(l[4]=e=>T.value=e),"background-color":"#f0f8ff","border-color":"#007aff","btn-background-color":"#007aff",color:"#ffffff","input-color":"#007aff"},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("圆角样式:")])),_:1}),s(v($),{modelValue:F.value,"onUpdate:modelValue":l[5]||(l[5]=e=>F.value=e),"border-radius":25,"background-color":"#f8f9fa","btn-background-color":"#4cd964",color:"#ffffff"},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("自定义按钮文本:")])),_:1}),s(v($),{modelValue:H.value,"onUpdate:modelValue":l[6]||(l[6]=e=>H.value=e),"minus-text":"减","plus-text":"加","background-color":"#fff3cd","border-color":"#ffc107","btn-background-color":"#ffc107",color:"#856404"},null,8,["modelValue"])])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("🔒 状态演示")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("禁用状态:")])),_:1}),s(v($),{modelValue:W.value,"onUpdate:modelValue":l[7]||(l[7]=e=>W.value=e),disabled:!0},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("只读状态:")])),_:1}),s(v($),{modelValue:j.value,"onUpdate:modelValue":l[8]||(l[8]=e=>j.value=e),readonly:!0},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("动态切换:")])),_:1}),s(V,{class:"control-buttons"},{default:d((()=>[s(U,{onClick:ue,class:"control-btn"},{default:d((()=>[c(m(M.value?"启用":"禁用"),1)])),_:1}),s(U,{onClick:de,class:"control-btn"},{default:d((()=>[c(m(R.value?"可编辑":"只读"),1)])),_:1})])),_:1}),s(v($),{modelValue:I.value,"onUpdate:modelValue":l[9]||(l[9]=e=>I.value=e),disabled:M.value,readonly:R.value},null,8,["modelValue","disabled","readonly"])])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("💰 格式化演示")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("货币格式:")])),_:1}),s(v($),{modelValue:S.value,"onUpdate:modelValue":l[10]||(l[10]=e=>S.value=e),step:.01,precision:2,min:0,max:9999.99,formatter:X,parser:Y},null,8,["modelValue"]),s(p,{class:"demo-value"},{default:d((()=>[c("实际值: "+m(S.value),1)])),_:1})])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("百分比格式:")])),_:1}),s(v($),{modelValue:N.value,"onUpdate:modelValue":l[11]||(l[11]=e=>N.value=e),step:.1,precision:1,min:0,max:100,formatter:Z,parser:ee},null,8,["modelValue"]),s(p,{class:"demo-value"},{default:d((()=>[c("实际值: "+m(N.value),1)])),_:1})])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("评分格式:")])),_:1}),s(v($),{modelValue:D.value,"onUpdate:modelValue":l[12]||(l[12]=e=>D.value=e),step:.5,precision:1,min:0,max:5,formatter:le},null,8,["modelValue"]),s(p,{class:"demo-value"},{default:d((()=>[c("实际值: "+m(D.value),1)])),_:1})])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("🎯 预设样式")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("购物车样式:")])),_:1}),s(v($),{modelValue:q.value,"onUpdate:modelValue":l[13]||(l[13]=e=>q.value=e),width:200,height:50,"btn-width":50,"btn-height":50,"border-radius":25,"background-color":"#f8f9fa","btn-background-color":"#007aff",color:"#ffffff",min:0,max:999},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("计数器样式:")])),_:1}),s(v($),{modelValue:z.value,"onUpdate:modelValue":l[14]||(l[14]=e=>z.value=e),width:150,height:40,"btn-width":40,"btn-height":40,"border-radius":4,min:1,max:99},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("数量选择:")])),_:1}),s(v($),{modelValue:A.value,"onUpdate:modelValue":l[15]||(l[15]=e=>A.value=e),width:180,height:50,min:1,max:999,longpress:!0,"longpress-time":500},null,8,["modelValue"])])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("⏰ 长按功能")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("启用长按 (600ms):")])),_:1}),s(v($),{modelValue:L.value,"onUpdate:modelValue":l[16]||(l[16]=e=>L.value=e),longpress:!0,"longpress-time":600,min:0,max:9999},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("快速长按 (300ms):")])),_:1}),s(v($),{modelValue:P.value,"onUpdate:modelValue":l[17]||(l[17]=e=>P.value=e),longpress:!0,"longpress-time":300,min:0,max:9999},null,8,["modelValue"])])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("禁用长按:")])),_:1}),s(v($),{modelValue:E.value,"onUpdate:modelValue":l[18]||(l[18]=e=>E.value=e),longpress:!1,min:0,max:9999},null,8,["modelValue"])])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("📡 事件演示")])),_:1}),s(V,{class:"demo-item"},{default:d((()=>[s(p,{class:"demo-label"},{default:d((()=>[c("事件监听:")])),_:1}),s(v($),{modelValue:G.value,"onUpdate:modelValue":l[19]||(l[19]=e=>G.value=e),min:0,max:10,onChange:ne,onPlus:re,onMinus:ce,onFocus:me,onBlur:ie},null,8,["modelValue"])])),_:1}),s(V,{class:"event-log"},{default:d((()=>[s(p,{class:"log-title"},{default:d((()=>[c("事件日志:")])),_:1}),s(se,{"scroll-y":"",class:"log-content"},{default:d((()=>[(t(!0),_(g,null,h(J.value,((e,l)=>(t(),u(p,{key:l,class:"log-item"},{default:d((()=>[c(m(e),1)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1}),s(V,{class:"demo-section"},{default:d((()=>[s(p,{class:"section-title"},{default:d((()=>[c("🛒 应用场景")])),_:1}),s(V,{class:"scenario-card"},{default:d((()=>[s(p,{class:"card-title"},{default:d((()=>[c("购物车商品")])),_:1}),s(V,{class:"product-item"},{default:d((()=>[s(fe,{class:"product-image",src:k,mode:"aspectFill"}),s(V,{class:"product-info"},{default:d((()=>[s(p,{class:"product-name"},{default:d((()=>[c("商品名称")])),_:1}),s(p,{class:"product-price"},{default:d((()=>[c("¥99.00")])),_:1})])),_:1}),s(v($),{modelValue:K.value,"onUpdate:modelValue":l[20]||(l[20]=e=>K.value=e),width:120,height:40,"btn-width":40,"btn-height":40,min:1,max:99,"background-color":"#f8f9fa","btn-background-color":"#007aff",color:"#ffffff"},null,8,["modelValue"])])),_:1})])),_:1}),s(V,{class:"scenario-card"},{default:d((()=>[s(p,{class:"card-title"},{default:d((()=>[c("商品评分")])),_:1}),s(V,{class:"rating-item"},{default:d((()=>[s(p,{class:"rating-label"},{default:d((()=>[c("请为商品打分:")])),_:1}),s(v($),{modelValue:O.value,"onUpdate:modelValue":l[21]||(l[21]=e=>O.value=e),step:.5,precision:1,min:0,max:5,formatter:le,"background-color":"#fff3cd","border-color":"#ffc107","btn-background-color":"#ffc107",color:"#856404"},null,8,["modelValue"])])),_:1})])),_:1}),s(V,{class:"scenario-card"},{default:d((()=>[s(p,{class:"card-title"},{default:d((()=>[c("价格设置")])),_:1}),s(V,{class:"price-item"},{default:d((()=>[s(p,{class:"price-label"},{default:d((()=>[c("设置商品价格:")])),_:1}),s(v($),{modelValue:Q.value,"onUpdate:modelValue":l[22]||(l[22]=e=>Q.value=e),step:.01,precision:2,min:0,max:99999.99,formatter:X,parser:Y,width:200,"background-color":"#f0f8ff","border-color":"#007aff","input-color":"#007aff"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}}}),[["__scopeId","data-v-38472152"]]);export{T as default};
