import"./index-ChUEiI3E.js";const t={createConfig:(t={})=>({current:0,tabs:[],height:"auto",backgroundColor:"#ffffff",tabHeight:88,tabBackgroundColor:"#ffffff",tabBorderColor:"#e5e5e5",tabWidth:"auto",tabMinWidth:120,tabPadding:"0 20rpx",tabMargin:"0",fontSize:28,color:"#666666",activeColor:"#007aff",disabledColor:"#cccccc",showIndicator:!0,indicatorColor:"#007aff",indicatorHeight:4,indicatorWidth:"auto",indicatorRadius:2,badgeColor:"#ff3b30",badgeTextColor:"#ffffff",badgeSize:32,scrollable:!0,closable:!1,swipeable:!1,animated:!0,position:"top",align:"left",customClass:"",customStyle:{},...t}),createTab:t=>({key:t.key||Date.now(),title:t.title,content:t.content||"",icon:t.icon||"",badge:t.badge||"",dot:t.dot||!1,disabled:t.disabled||!1,closable:t.closable||!1,...t}),createTabs(t){return t.map((t=>this.createTab(t)))},createNavConfig(){return this.createConfig({height:88,tabHeight:88,showIndicator:!0,scrollable:!0,animated:!0})},createCardConfig(){return this.createConfig({height:"auto",tabHeight:80,tabBackgroundColor:"#f8f9fa",tabBorderColor:"#e9ecef",showIndicator:!1,scrollable:!1})},createButtonConfig(){return this.createConfig({height:"auto",tabHeight:64,tabPadding:"0 24rpx",tabMargin:"0 8rpx",showIndicator:!1,scrollable:!1,align:"center"})},createVerticalConfig(){return this.createConfig({position:"left",height:600,tabWidth:200,showIndicator:!0,scrollable:!0})},validateIndex:(t,e)=>t>=0&&t<e,findTabIndex:(t,e)=>t.findIndex((t=>t.key===e)),filterAvailableTabs:t=>t.filter((t=>!t.disabled)),getNextAvailableIndex(t,e,a="next"){const o=this.filterAvailableTabs(t);if(0===o.length)return-1;const i=t[e],n=o.findIndex((t=>t.key===(null==i?void 0:i.key)));if("next"===a){const e=(n+1)%o.length;return t.findIndex((t=>t.key===o[e].key))}{const e=n<=0?o.length-1:n-1;return t.findIndex((t=>t.key===o[e].key))}},generateRandomTabs(t=5){const e=["🏠","👤","📊","⚙️","📝","💬","📷","🎵","📱","💡"],a=["首页","用户","数据","设置","文档","消息","相册","音乐","应用","创意"];return Array.from({length:t},((t,o)=>({key:`tab-${o}`,title:a[o]||`标签 ${o+1}`,icon:e[o]||"📄",content:`这是第 ${o+1} 个标签页的内容`,badge:Math.random()>.7?Math.floor(99*Math.random())+1:"",dot:Math.random()>.8,disabled:Math.random()>.9,closable:Math.random()>.7})))},getThemeColors(){const t=this.isDarkTheme();return{backgroundColor:t?"#2a2a2a":"#ffffff",tabBackgroundColor:t?"#2a2a2a":"#ffffff",tabBorderColor:t?"#444444":"#e5e5e5",color:t?"#cccccc":"#666666",activeColor:t?"#ffffff":"#007aff",disabledColor:t?"#666666":"#cccccc"}},isDarkTheme:()=>!("undefined"==typeof window||!window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches};t.createConfig(),t.createNavConfig(),t.createCardConfig(),t.createButtonConfig(),t.createVerticalConfig(),t.createTabs([{title:"首页",icon:"🏠"},{title:"分类",icon:"📂"},{title:"购物车",icon:"🛒",badge:3},{title:"我的",icon:"👤",dot:!0}]),t.createTabs([{title:"推荐",content:"推荐内容"},{title:"热门",content:"热门内容",badge:"HOT"},{title:"最新",content:"最新内容"},{title:"关注",content:"关注内容",dot:!0},{title:"收藏",content:"收藏内容"}]),t.createTabs([{title:"基本设置",icon:"⚙️"},{title:"隐私设置",icon:"🔒"},{title:"通知设置",icon:"🔔",badge:2},{title:"账户设置",icon:"👤"},{title:"关于",icon:"ℹ️"}]);
