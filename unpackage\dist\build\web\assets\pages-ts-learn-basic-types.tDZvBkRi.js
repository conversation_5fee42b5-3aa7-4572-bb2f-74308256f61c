import{m as e,n as l,h as t,g as s,c as a,w as c,i as n,o,a as d,b as u,f as i,e as f,S as r}from"./index-ChUEiI3E.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";const p=_({methods:{goBack(){e()},nextLesson(){this.markAsCompleted(),l({url:"/pages/ts-learn/interfaces"})},markAsCompleted(){try{let e=t("ts-learn-progress")||{completedItems:[]};e.completedItems.includes("basic-types")||(e.completedItems.push("basic-types"),e.lastUpdate=Date.now(),s("ts-learn-progress",e))}catch(e){console.error("保存学习进度失败:",e)}}},onLoad(){console.log("基础类型课程加载完成")}},[["render",function(e,l,t,s,_,p){const x=i,m=f,b=n,g=r;return o(),a(b,{class:"container"},{default:c((()=>[d(b,{class:"header"},{default:c((()=>[d(x,{onClick:p.goBack,class:"back-btn"},{default:c((()=>[u("← 返回")])),_:1},8,["onClick"]),d(m,{class:"title"},{default:c((()=>[u("基础类型")])),_:1}),d(b,{class:"progress-indicator"},{default:c((()=>[u("1/9")])),_:1})])),_:1}),d(g,{"scroll-y":"",class:"content"},{default:c((()=>[d(b,{class:"content-wrapper"},{default:c((()=>[d(b,{class:"lesson-intro"},{default:c((()=>[d(m,{class:"intro-title"},{default:c((()=>[u("学习目标")])),_:1}),d(m,{class:"intro-text"},{default:c((()=>[u("掌握 TypeScript 的基本数据类型，理解类型注解的语法和作用")])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("🔤 字符串类型 (string)")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('let name: string = "张三" let message: string = `你好，${name}!` let multiline: string = ` 这是一个 多行字符串 `')])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• 使用 string 类型表示文本数据")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 支持单引号、双引号和模板字符串")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 模板字符串支持变量插值和多行文本")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("🔢 数字类型 (number)")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u("let age: number = 25 let price: number = 99.99 let hex: number = 0xff00 // 十六进制 let binary: number = 0b1010 // 二进制 let octal: number = 0o744 // 八进制")])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• TypeScript 中所有数字都是浮点数")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 支持十进制、十六进制、二进制和八进制")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 包括整数和小数")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("✅ 布尔类型 (boolean)")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u("let isActive: boolean = true let isCompleted: boolean = false let isValid: boolean = age > 18")])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• 只有两个值：true 和 false")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 常用于条件判断和状态标识")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("📋 数组类型 (Array)")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('// 方式一：类型[] let numbers: number[] = [1, 2, 3, 4, 5] let names: string[] = ["张三", "李四", "王五"] // 方式二：Array<类型> let scores: Array<number> = [85, 92, 78] let flags: Array<boolean> = [true, false, true]')])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• 两种声明方式：类型[] 或 Array<类型>")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 数组中的所有元素必须是同一类型")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 推荐使用 类型[] 的简洁语法")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("📦 元组类型 (Tuple)")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('// 固定长度和类型的数组 let person: [string, number] = ["张三", 25] let coordinate: [number, number] = [10, 20] // 访问元组元素 let name = person[0] // string let age = person[1] // number')])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• 元组是固定长度的数组")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 每个位置的类型都是确定的")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 适合表示结构化的数据")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("📝 枚举类型 (enum)")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('// 数字枚举 enum Color { Red, // 0 Green, // 1 Blue // 2 } // 字符串枚举 enum Direction { Up = "UP", Down = "DOWN", Left = "LEFT", Right = "RIGHT" } let myColor: Color = Color.Red let move: Direction = Direction.Up')])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• 枚举用于定义命名的常量集合")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 数字枚举默认从 0 开始递增")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 字符串枚举需要显式赋值")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("❓ any 类型")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('let value: any = 42 value = "hello" // OK value = true // OK value = [1, 2, 3] // OK // 不推荐使用，会失去类型检查 let list: any[] = [1, "hello", true]')])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• any 类型可以是任何类型")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 失去了 TypeScript 的类型检查优势")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 应该尽量避免使用")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("🚫 void 类型")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('// 函数没有返回值 function sayHello(): void { console.log("Hello!") } // 变量声明为 void（很少使用） let unusable: void = undefined')])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• void 表示没有任何类型")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 主要用于函数没有返回值的情况")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• void 类型的变量只能赋值 undefined")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("⚪ null 和 undefined")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u("let n: null = null let u: undefined = undefined // 默认情况下，null 和 undefined 是所有类型的子类型 let name: string = null // OK（非严格模式） let age: number = undefined // OK（非严格模式） // 严格模式下需要联合类型 let name2: string | null = null let age2: number | undefined = undefined")])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• null 表示空值，undefined 表示未定义")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 在严格模式下，需要显式声明联合类型")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 推荐开启严格模式以避免空值错误")])),_:1})])),_:1})])),_:1}),d(b,{class:"section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("🔄 never 类型")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u("// 永远不会返回的函数 function error(message: string): never { throw new Error(message) } // 无限循环的函数 function infiniteLoop(): never { while (true) { // 无限循环 } }")])),_:1})])),_:1}),d(b,{class:"explanation"},{default:c((()=>[d(m,{class:"exp-text"},{default:c((()=>[u("• never 表示永远不会出现的值")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• 用于总是抛出异常或无限循环的函数")])),_:1}),d(m,{class:"exp-text"},{default:c((()=>[u("• never 是所有类型的子类型")])),_:1})])),_:1})])),_:1}),d(b,{class:"practice-section"},{default:c((()=>[d(m,{class:"section-title"},{default:c((()=>[u("🎯 实践练习")])),_:1}),d(b,{class:"practice-item"},{default:c((()=>[d(m,{class:"practice-title"},{default:c((()=>[u("练习 1：声明变量")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u('// 请为以下变量添加正确的类型注解 let userName = "小明" let userAge = 20 let isStudent = true let hobbies = ["读书", "游泳", "编程"] let position = [116.3974, 39.9093]')])),_:1})])),_:1})])),_:1}),d(b,{class:"practice-item"},{default:c((()=>[d(m,{class:"practice-title"},{default:c((()=>[u("练习 2：创建枚举")])),_:1}),d(b,{class:"code-block"},{default:c((()=>[d(m,{class:"code"},{default:c((()=>[u("// 创建一个表示用户状态的枚举 // 包含：活跃、非活跃、已禁用三种状态")])),_:1})])),_:1})])),_:1})])),_:1}),d(b,{class:"navigation"},{default:c((()=>[d(x,{onClick:p.goBack,class:"nav-btn secondary"},{default:c((()=>[u("上一课")])),_:1},8,["onClick"]),d(x,{onClick:p.nextLesson,class:"nav-btn primary"},{default:c((()=>[u("下一课：接口")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-245cb51e"]]);export{p as default};
