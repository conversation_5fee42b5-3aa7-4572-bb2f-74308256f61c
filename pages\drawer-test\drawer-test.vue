<template>
  <view class="container">
    <view class="header">
      <text class="title">TUI-Drawer 测试页面</text>
      <text class="subtitle">测试抽屉组件的各种功能</text>
    </view>

    <scroll-view scroll-y class="content">
      <!-- 基础功能测试 -->
      <view class="section">
        <text class="section-title">🎯 基础功能</text>
        <view class="button-group">
          <button class="test-btn primary" @click="openDrawer('left')">左侧抽屉</button>
          <button class="test-btn primary" @click="openDrawer('right')">右侧抽屉</button>
          <button class="test-btn primary" @click="openDrawer('top')">顶部抽屉</button>
          <button class="test-btn primary" @click="openDrawer('bottom')">底部抽屉</button>
        </view>
      </view>

      <!-- 自定义样式测试 -->
      <view class="section">
        <text class="section-title">🎨 自定义样式</text>
        <view class="button-group">
          <button class="test-btn success" @click="openCustomDrawer">自定义样式</button>
          <button class="test-btn warning" @click="openRoundedDrawer">圆角抽屉</button>
          <button class="test-btn danger" @click="openColorfulDrawer">彩色抽屉</button>
        </view>
      </view>

      <!-- 功能特性测试 -->
      <view class="section">
        <text class="section-title">⚙️ 功能特性</text>
        <view class="button-group">
          <button class="test-btn info" @click="openWithCloseBtn">带关闭按钮</button>
          <button class="test-btn secondary" @click="openNoMask">无遮罩</button>
          <button class="test-btn dark" @click="openNoMaskClose">禁止遮罩关闭</button>
        </view>
      </view>

      <!-- 事件测试 -->
      <view class="section">
        <text class="section-title">📡 事件监听</text>
        <view class="event-log">
          <text class="log-title">事件日志:</text>
          <view class="log-list">
            <text 
              v-for="(log, index) in eventLogs" 
              :key="index" 
              class="log-item"
            >
              {{ log }}
            </text>
          </view>
          <button class="clear-btn" @click="clearLogs">清空日志</button>
        </view>
      </view>
    </scroll-view>

    <!-- 基础抽屉 -->
    <tui-drawer 
      :visible="basicDrawer.visible"
      :mode="basicDrawer.mode"
      @open="onDrawerEvent('基础抽屉', 'open')"
      @opened="onDrawerEvent('基础抽屉', 'opened')"
      @close="closeBasicDrawer"
      @closed="onDrawerEvent('基础抽屉', 'closed')"
    >
      <view class="drawer-content">
        <text class="drawer-title">{{ basicDrawer.mode }} 抽屉</text>
        <text class="drawer-text">这是一个基础的 {{ basicDrawer.mode }} 方向抽屉</text>
        <button class="close-btn" @click="closeBasicDrawer">关闭抽屉</button>
      </view>
    </tui-drawer>

    <!-- 自定义样式抽屉 -->
    <tui-drawer 
      :visible="customDrawer.visible"
      mode="right"
      width="85%"
      background-color="#f8f9fa"
      border-radius="20rpx"
      :duration="500"
      @open="onDrawerEvent('自定义抽屉', 'open')"
      @opened="onDrawerEvent('自定义抽屉', 'opened')"
      @close="closeCustomDrawer"
      @closed="onDrawerEvent('自定义抽屉', 'closed')"
    >
      <view class="custom-drawer-content">
        <text class="custom-title">自定义样式抽屉</text>
        <text class="custom-desc">宽度: 85%, 圆角: 20rpx, 动画: 500ms</text>
        <view class="feature-list">
          <text class="feature-item">✅ 自定义宽度</text>
          <text class="feature-item">✅ 自定义背景色</text>
          <text class="feature-item">✅ 自定义圆角</text>
          <text class="feature-item">✅ 自定义动画时长</text>
        </view>
        <button class="custom-close-btn" @click="closeCustomDrawer">关闭</button>
      </view>
    </tui-drawer>

    <!-- 圆角抽屉 -->
    <tui-drawer 
      :visible="roundedDrawer.visible"
      mode="bottom"
      height="60%"
      background-color="#ffffff"
      border-radius="40rpx 40rpx 0 0"
      mask-color="rgba(0, 0, 0, 0.8)"
      @open="onDrawerEvent('圆角抽屉', 'open')"
      @opened="onDrawerEvent('圆角抽屉', 'opened')"
      @close="closeRoundedDrawer"
      @closed="onDrawerEvent('圆角抽屉', 'closed')"
    >
      <view class="rounded-drawer-content">
        <view class="handle"></view>
        <text class="rounded-title">圆角底部抽屉</text>
        <text class="rounded-desc">顶部圆角设计，更加美观</text>
        <button class="rounded-close-btn" @click="closeRoundedDrawer">关闭</button>
      </view>
    </tui-drawer>

    <!-- 彩色抽屉 -->
    <tui-drawer 
      :visible="colorfulDrawer.visible"
      mode="left"
      width="75%"
      background-color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      :duration="400"
      @open="onDrawerEvent('彩色抽屉', 'open')"
      @opened="onDrawerEvent('彩色抽屉', 'opened')"
      @close="closeColorfulDrawer"
      @closed="onDrawerEvent('彩色抽屉', 'closed')"
    >
      <view class="colorful-drawer-content">
        <text class="colorful-title">彩色渐变抽屉</text>
        <text class="colorful-desc">支持渐变背景色</text>
        <button class="colorful-close-btn" @click="closeColorfulDrawer">关闭</button>
      </view>
    </tui-drawer>

    <!-- 带关闭按钮抽屉 -->
    <tui-drawer 
      :visible="closeBtnDrawer.visible"
      mode="right"
      width="70%"
      :show-close="true"
      close-position="top-right"
      @open="onDrawerEvent('关闭按钮抽屉', 'open')"
      @opened="onDrawerEvent('关闭按钮抽屉', 'opened')"
      @close="closeCloseBtnDrawer"
      @closed="onDrawerEvent('关闭按钮抽屉', 'closed')"
    >
      <view class="close-btn-drawer-content">
        <text class="close-btn-title">带关闭按钮的抽屉</text>
        <text class="close-btn-desc">右上角有关闭按钮</text>
        <text class="close-btn-tip">点击右上角 ✕ 或遮罩关闭</text>
      </view>
    </tui-drawer>

    <!-- 无遮罩抽屉 -->
    <tui-drawer 
      :visible="noMaskDrawer.visible"
      mode="top"
      height="40%"
      :mask="false"
      background-color="#fff3cd"
      @open="onDrawerEvent('无遮罩抽屉', 'open')"
      @opened="onDrawerEvent('无遮罩抽屉', 'opened')"
      @close="closeNoMaskDrawer"
      @closed="onDrawerEvent('无遮罩抽屉', 'closed')"
    >
      <view class="no-mask-drawer-content">
        <text class="no-mask-title">无遮罩抽屉</text>
        <text class="no-mask-desc">没有遮罩层，不会阻挡背景内容</text>
        <button class="no-mask-close-btn" @click="closeNoMaskDrawer">关闭</button>
      </view>
    </tui-drawer>

    <!-- 禁止遮罩关闭抽屉 -->
    <tui-drawer 
      :visible="noMaskCloseDrawer.visible"
      mode="bottom"
      height="50%"
      :mask-closable="false"
      background-color="#d1ecf1"
      @open="onDrawerEvent('禁止遮罩关闭抽屉', 'open')"
      @opened="onDrawerEvent('禁止遮罩关闭抽屉', 'opened')"
      @close="closeNoMaskCloseDrawer"
      @closed="onDrawerEvent('禁止遮罩关闭抽屉', 'closed')"
    >
      <view class="no-mask-close-drawer-content">
        <text class="no-mask-close-title">禁止遮罩关闭</text>
        <text class="no-mask-close-desc">点击遮罩无法关闭，只能通过按钮关闭</text>
        <button class="no-mask-close-btn" @click="closeNoMaskCloseDrawer">关闭抽屉</button>
      </view>
    </tui-drawer>
  </view>
</template>

<script lang="ts">
interface DrawerState {
  visible: boolean
  mode?: 'left' | 'right' | 'top' | 'bottom'
}

interface ComponentData {
  basicDrawer: DrawerState
  customDrawer: DrawerState
  roundedDrawer: DrawerState
  colorfulDrawer: DrawerState
  closeBtnDrawer: DrawerState
  noMaskDrawer: DrawerState
  noMaskCloseDrawer: DrawerState
  eventLogs: string[]
}

export default {
  data(): ComponentData {
    return {
      basicDrawer: {
        visible: false,
        mode: 'right'
      },
      customDrawer: {
        visible: false
      },
      roundedDrawer: {
        visible: false
      },
      colorfulDrawer: {
        visible: false
      },
      closeBtnDrawer: {
        visible: false
      },
      noMaskDrawer: {
        visible: false
      },
      noMaskCloseDrawer: {
        visible: false
      },
      eventLogs: []
    }
  },
  methods: {
    /**
     * 打开基础抽屉
     */
    openDrawer(mode: 'left' | 'right' | 'top' | 'bottom'): void {
      this.basicDrawer.mode = mode
      this.basicDrawer.visible = true
    },

    /**
     * 关闭基础抽屉
     */
    closeBasicDrawer(): void {
      this.basicDrawer.visible = false
      this.onDrawerEvent('基础抽屉', 'close')
    },

    /**
     * 打开自定义样式抽屉
     */
    openCustomDrawer(): void {
      this.customDrawer.visible = true
    },

    /**
     * 关闭自定义样式抽屉
     */
    closeCustomDrawer(): void {
      this.customDrawer.visible = false
      this.onDrawerEvent('自定义抽屉', 'close')
    },

    /**
     * 打开圆角抽屉
     */
    openRoundedDrawer(): void {
      this.roundedDrawer.visible = true
    },

    /**
     * 关闭圆角抽屉
     */
    closeRoundedDrawer(): void {
      this.roundedDrawer.visible = false
      this.onDrawerEvent('圆角抽屉', 'close')
    },

    /**
     * 打开彩色抽屉
     */
    openColorfulDrawer(): void {
      this.colorfulDrawer.visible = true
    },

    /**
     * 关闭彩色抽屉
     */
    closeColorfulDrawer(): void {
      this.colorfulDrawer.visible = false
      this.onDrawerEvent('彩色抽屉', 'close')
    },

    /**
     * 打开带关闭按钮抽屉
     */
    openWithCloseBtn(): void {
      this.closeBtnDrawer.visible = true
    },

    /**
     * 关闭带关闭按钮抽屉
     */
    closeCloseBtnDrawer(): void {
      this.closeBtnDrawer.visible = false
      this.onDrawerEvent('关闭按钮抽屉', 'close')
    },

    /**
     * 打开无遮罩抽屉
     */
    openNoMask(): void {
      this.noMaskDrawer.visible = true
    },

    /**
     * 关闭无遮罩抽屉
     */
    closeNoMaskDrawer(): void {
      this.noMaskDrawer.visible = false
      this.onDrawerEvent('无遮罩抽屉', 'close')
    },

    /**
     * 打开禁止遮罩关闭抽屉
     */
    openNoMaskClose(): void {
      this.noMaskCloseDrawer.visible = true
    },

    /**
     * 关闭禁止遮罩关闭抽屉
     */
    closeNoMaskCloseDrawer(): void {
      this.noMaskCloseDrawer.visible = false
      this.onDrawerEvent('禁止遮罩关闭抽屉', 'close')
    },

    /**
     * 记录抽屉事件
     */
    onDrawerEvent(drawerName: string, eventType: string): void {
      const timestamp = new Date().toLocaleTimeString()
      const log = `[${timestamp}] ${drawerName} - ${eventType}`
      this.eventLogs.unshift(log)
      
      // 限制日志数量
      if (this.eventLogs.length > 20) {
        this.eventLogs = this.eventLogs.slice(0, 20)
      }
    },

    /**
     * 清空事件日志
     */
    clearLogs(): void {
      this.eventLogs = []
    }
  },

  onLoad() {
    console.log('抽屉测试页面加载完成')
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  padding: 40rpx 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.content {
  height: calc(100vh - 140rpx);
  padding: 20rpx;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.test-btn.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.test-btn.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.test-btn.danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.test-btn.info {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.test-btn.secondary {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
  color: #333;
}

.test-btn.dark {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.test-btn:active {
  transform: scale(0.98);
}

/* 事件日志样式 */
.event-log {
  margin-top: 20rpx;
}

.log-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.log-list {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  max-height: 300rpx;
  overflow-y: auto;
  margin-bottom: 15rpx;
}

.log-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  padding: 5rpx 0;
  border-bottom: 1rpx solid #eee;
}

.log-item:last-child {
  border-bottom: none;
}

.clear-btn {
  width: 100%;
  height: 60rpx;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 抽屉内容样式 */
.drawer-content {
  padding: 40rpx 30rpx;
  text-align: center;
}

.drawer-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.drawer-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.close-btn {
  width: 200rpx;
  height: 70rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
}

/* 自定义抽屉样式 */
.custom-drawer-content {
  padding: 40rpx 30rpx;
  text-align: center;
}

.custom-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.custom-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 30rpx;
}

.feature-list {
  text-align: left;
  margin-bottom: 40rpx;
}

.feature-item {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}

.custom-close-btn {
  width: 180rpx;
  height: 60rpx;
  background: #4facfe;
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
}

/* 圆角抽屉样式 */
.rounded-drawer-content {
  padding: 30rpx;
  text-align: center;
}

.handle {
  width: 60rpx;
  height: 8rpx;
  background: #ddd;
  border-radius: 4rpx;
  margin: 0 auto 30rpx;
}

.rounded-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.rounded-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 40rpx;
}

.rounded-close-btn {
  width: 160rpx;
  height: 60rpx;
  background: #fa709a;
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
}

/* 彩色抽屉样式 */
.colorful-drawer-content {
  padding: 40rpx 30rpx;
  text-align: center;
}

.colorful-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 20rpx;
}

.colorful-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  margin-bottom: 40rpx;
}

.colorful-close-btn {
  width: 160rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid white;
  border-radius: 30rpx;
  font-size: 26rpx;
}

/* 关闭按钮抽屉样式 */
.close-btn-drawer-content {
  padding: 40rpx 30rpx;
  text-align: center;
}

.close-btn-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.close-btn-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
}

.close-btn-tip {
  font-size: 24rpx;
  color: #999;
  display: block;
  font-style: italic;
}

/* 无遮罩抽屉样式 */
.no-mask-drawer-content {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #ffeaa7;
}

.no-mask-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #d63031;
  display: block;
  margin-bottom: 15rpx;
}

.no-mask-desc {
  font-size: 24rpx;
  color: #636e72;
  display: block;
  margin-bottom: 30rpx;
}

.no-mask-close-btn {
  width: 140rpx;
  height: 50rpx;
  background: #fdcb6e;
  color: #2d3436;
  border: none;
  border-radius: 25rpx;
  font-size: 24rpx;
}

/* 禁止遮罩关闭抽屉样式 */
.no-mask-close-drawer-content {
  padding: 40rpx 30rpx;
  text-align: center;
}

.no-mask-close-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #0984e3;
  display: block;
  margin-bottom: 15rpx;
}

.no-mask-close-desc {
  font-size: 26rpx;
  color: #636e72;
  display: block;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.no-mask-close-btn {
  width: 180rpx;
  height: 60rpx;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
}
</style>
