import{y as e,z as l,D as a,H as s,A as t,P as o,o as u,c as d,w as c,l as r,j as n,a as i,b as f,t as m,x as v,i as b,B as _,s as y,v as p,e as h,f as w,I as k,d as g,S as C}from"./index-ChUEiI3E.js";import{_ as S}from"./product.q7Ixu4ET.js";import{n as z,C as x}from"./index.DI3nc_37.js";import"./index.Dvz5gvUX.js";import{_ as L}from"./_plugin-vue_export-helper.BCo6x5W8.js";const D=L(e({__name:"Countdown",props:{time:{default:0},endTime:{default:void 0},startTime:{default:void 0},format:{default:"HH:mm:ss"},showDays:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showSeconds:{type:Boolean,default:!0},showMilliseconds:{type:Boolean,default:!1},showLabels:{type:Boolean,default:!1},showSeparator:{type:Boolean,default:!0},dayLabel:{default:"天"},hourLabel:{default:"时"},minuteLabel:{default:"分"},secondLabel:{default:"秒"},millisecondLabel:{default:"毫秒"},separator:{default:":"},size:{default:32},color:{default:"#333333"},backgroundColor:{default:"transparent"},borderColor:{default:"transparent"},borderRadius:{default:4},valueColor:{default:"#333333"},valueSize:{default:32},valueBold:{type:Boolean,default:!0},labelColor:{default:"#666666"},labelSize:{default:24},separatorColor:{default:"#666666"},separatorSize:{default:28},autoStart:{type:Boolean,default:!0},precision:{default:1e3},customClass:{default:""},customStyle:{default:()=>({})}},emits:["change","finish","start","pause","resume"],setup(e,{expose:_,emit:y}){const p=e,h=y,w=l({days:0,hours:0,minutes:0,seconds:0,milliseconds:0,total:0}),k=l(!1),g=l(null),C=a((()=>["countdown-container-base",{"countdown-running":k.value},p.customClass])),S=a((()=>({...p.customStyle}))),z=a((()=>["time-item-base"])),x=a((()=>({backgroundColor:p.backgroundColor,borderColor:p.borderColor,borderRadius:"number"==typeof p.borderRadius?`${p.borderRadius}px`:p.borderRadius,borderWidth:"transparent"!==p.borderColor?"1px":"0"}))),L=a((()=>({color:p.valueColor,fontSize:"number"==typeof p.valueSize?`${p.valueSize}px`:p.valueSize,fontWeight:p.valueBold?"bold":"normal"}))),D=a((()=>({color:p.labelColor,fontSize:"number"==typeof p.labelSize?`${p.labelSize}px`:p.labelSize}))),M=a((()=>({color:p.separatorColor,fontSize:"number"==typeof p.separatorSize?`${p.separatorSize}px`:p.separatorSize}))),B=e=>{if("number"==typeof e)return e;if("string"==typeof e){const l=Date.parse(e);if(!isNaN(l))return l;const a=Number(e);if(!isNaN(a))return a}return e instanceof Date?e.getTime():0},T=()=>{let e=0;if(p.endTime){const l=B(p.endTime),a=p.startTime?B(p.startTime):Date.now();e=Math.max(0,l-a)}else e=B(p.time);return{days:Math.floor(e/864e5),hours:Math.floor(e%864e5/36e5),minutes:Math.floor(e%36e5/6e4),seconds:Math.floor(e%6e4/1e3),milliseconds:e%1e3,total:e}},R=(e,l=2)=>e.toString().padStart(l,"0"),F=()=>{const e=T();(e.days!==w.value.days||e.hours!==w.value.hours||e.minutes!==w.value.minutes||e.seconds!==w.value.seconds||e.milliseconds!==w.value.milliseconds)&&(w.value=e,h("change",e),e.total<=0&&(j(),h("finish")))},H=()=>{k.value||(k.value=!0,h("start"),g.value=setInterval((()=>{if(p.endTime)F();else if(w.value.total>0){w.value.total-=p.precision;const e=Math.floor(w.value.total/864e5),l=Math.floor(w.value.total%864e5/36e5),a=Math.floor(w.value.total%36e5/6e4),s=Math.floor(w.value.total%6e4/1e3),t=w.value.total%1e3;w.value={days:e,hours:l,minutes:a,seconds:s,milliseconds:t,total:Math.max(0,w.value.total)},h("change",w.value),w.value.total<=0&&(j(),h("finish"))}}),p.precision))},j=()=>{k.value=!1,g.value&&(clearInterval(g.value),g.value=null)},I=()=>{j(),w.value=T()};return s((()=>[p.time,p.endTime,p.startTime]),(()=>{I(),p.autoStart&&H()}),{immediate:!0}),t((()=>{F(),p.autoStart&&H()})),o((()=>{j()})),_({start:H,pause:()=>{k.value&&(k.value=!1,g.value&&(clearInterval(g.value),g.value=null),h("pause"))},resume:()=>{k.value||(H(),h("resume"))},stop:j,reset:I,timeData:w.value,isRunning:k.value}),(e,l)=>{const a=b;return u(),d(a,{class:r(["countdown-container",C.value]),style:n(S.value)},{default:c((()=>[e.showDays&&w.value.days>0?(u(),d(a,{key:0,class:r(["time-item",z.value]),style:n(x.value)},{default:c((()=>[i(a,{class:"time-value",style:n(L.value)},{default:c((()=>[f(m(R(w.value.days)),1)])),_:1},8,["style"]),e.showLabels?(u(),d(a,{key:0,class:"time-label",style:n(D.value)},{default:c((()=>[f(m(e.dayLabel),1)])),_:1},8,["style"])):v("",!0)])),_:1},8,["class","style"])):v("",!0),e.showDays&&w.value.days>0&&e.showSeparator?(u(),d(a,{key:1,class:"time-separator",style:n(M.value)},{default:c((()=>[f(m(e.separator),1)])),_:1},8,["style"])):v("",!0),e.showHours?(u(),d(a,{key:2,class:r(["time-item",z.value]),style:n(x.value)},{default:c((()=>[i(a,{class:"time-value",style:n(L.value)},{default:c((()=>[f(m(R(w.value.hours)),1)])),_:1},8,["style"]),e.showLabels?(u(),d(a,{key:0,class:"time-label",style:n(D.value)},{default:c((()=>[f(m(e.hourLabel),1)])),_:1},8,["style"])):v("",!0)])),_:1},8,["class","style"])):v("",!0),e.showHours&&e.showSeparator?(u(),d(a,{key:3,class:"time-separator",style:n(M.value)},{default:c((()=>[f(m(e.separator),1)])),_:1},8,["style"])):v("",!0),e.showMinutes?(u(),d(a,{key:4,class:r(["time-item",z.value]),style:n(x.value)},{default:c((()=>[i(a,{class:"time-value",style:n(L.value)},{default:c((()=>[f(m(R(w.value.minutes)),1)])),_:1},8,["style"]),e.showLabels?(u(),d(a,{key:0,class:"time-label",style:n(D.value)},{default:c((()=>[f(m(e.minuteLabel),1)])),_:1},8,["style"])):v("",!0)])),_:1},8,["class","style"])):v("",!0),e.showMinutes&&e.showSeparator?(u(),d(a,{key:5,class:"time-separator",style:n(M.value)},{default:c((()=>[f(m(e.separator),1)])),_:1},8,["style"])):v("",!0),e.showSeconds?(u(),d(a,{key:6,class:r(["time-item",z.value]),style:n(x.value)},{default:c((()=>[i(a,{class:"time-value",style:n(L.value)},{default:c((()=>[f(m(R(w.value.seconds)),1)])),_:1},8,["style"]),e.showLabels?(u(),d(a,{key:0,class:"time-label",style:n(D.value)},{default:c((()=>[f(m(e.secondLabel),1)])),_:1},8,["style"])):v("",!0)])),_:1},8,["class","style"])):v("",!0),e.showMilliseconds?(u(),d(a,{key:7,class:r(["time-item",z.value]),style:n(x.value)},{default:c((()=>[i(a,{class:"time-value",style:n(L.value)},{default:c((()=>[f(m(R(w.value.milliseconds,3)),1)])),_:1},8,["style"]),e.showLabels?(u(),d(a,{key:0,class:"time-label",style:n(D.value)},{default:c((()=>[f(m(e.millisecondLabel),1)])),_:1},8,["style"])):v("",!0)])),_:1},8,["class","style"])):v("",!0)])),_:1},8,["class","style"])}}}),[["__scopeId","data-v-e5a53b13"]]),M=L(e({__name:"countdown-demo",setup(e){const s=l(0),o=l(3e5),v=l(),L=l(Date.now()+72e5),M=l(108e5),B=l(6e5),T=l(Date.now()+18e5),R=l(Date.now()+6048e5),F=l(72e5),H=l(6e4),j=l(!1),I=l(),N=l(3e5),$=l(6e5),q=l(),A=l(Date.now()+27e5),J=l(Date.now()+2592e5),P=l(54e5),W=l(12e4),E=l(),G=l("未开始"),K=a((()=>new Date(L.value).toLocaleString()));t((()=>{s.value=z.getTotalHeight()}));const O=e=>{console.log("基础倒计时变化:",e)},Q=()=>{y({title:"基础倒计时结束！",icon:"success"})},U=()=>{var e;null==(e=v.value)||e.start()},V=()=>{var e;null==(e=v.value)||e.pause()},X=()=>{var e;null==(e=v.value)||e.reset()},Y=e=>{console.log("结束时间倒计时变化:",e)},Z=()=>{y({title:"时间到！",icon:"success"})},ee=()=>{var e;j.value||(j.value=!0,null==(e=I.value)||e.start(),y({title:"验证码已发送",icon:"success"}))},le=()=>{j.value=!1,y({title:"可以重新发送验证码",icon:"none"})},ae=e=>{console.log("运动计时器:",e)},se=()=>{var e;null==(e=q.value)||e.start()},te=()=>{var e;null==(e=q.value)||e.pause()},oe=()=>{var e;null==(e=q.value)||e.reset()},ue=()=>{p({title:"秒杀结束",content:"本次秒杀活动已结束，感谢您的参与！",showCancel:!1})},de=()=>{p({title:"考试时间到",content:"考试时间已结束，系统将自动提交试卷。",showCancel:!1,success:()=>{y({title:"试卷已提交",icon:"success"})}})},ce=e=>{console.log("手动控制倒计时变化:",e)},re=()=>{G.value="运行中"},ne=()=>{G.value="已暂停"},ie=()=>{G.value="运行中"},fe=()=>{G.value="已结束",y({title:"手动控制倒计时结束！",icon:"success"})},me=()=>{var e;null==(e=E.value)||e.start()},ve=()=>{var e;null==(e=E.value)||e.pause()},be=()=>{var e;null==(e=E.value)||e.resume()},_e=()=>{var e;null==(e=E.value)||e.stop(),G.value="已停止"},ye=()=>{var e;null==(e=E.value)||e.reset(),G.value="未开始"};return(e,l)=>{const a=h,t=w,v=b,y=k,p=g,z=C;return u(),d(v,{class:"page"},{default:c((()=>[i(_(x),{title:"倒计时组件演示","show-back":!0}),i(z,{"scroll-y":"",class:"content",style:n({paddingTop:s.value+"px"})},{default:c((()=>[i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("📝 基础用法")])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("基础倒计时 (5分钟):")])),_:1}),i(_(D),{time:o.value,onChange:O,onFinish:Q},null,8,["time"]),i(v,{class:"control-buttons"},{default:c((()=>[i(t,{onClick:U,class:"control-btn"},{default:c((()=>[f("开始")])),_:1}),i(t,{onClick:V,class:"control-btn secondary"},{default:c((()=>[f("暂停")])),_:1}),i(t,{onClick:X,class:"control-btn"},{default:c((()=>[f("重置")])),_:1})])),_:1})])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("指定结束时间:")])),_:1}),i(_(D),{"end-time":L.value,onChange:Y,onFinish:Z},null,8,["end-time"]),i(a,{class:"demo-info"},{default:c((()=>[f("结束时间: "+m(K.value),1)])),_:1})])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("🎛️ 显示控制")])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("只显示时分秒:")])),_:1}),i(_(D),{time:M.value,"show-days":!1,"show-hours":!0,"show-minutes":!0,"show-seconds":!0},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("显示标签:")])),_:1}),i(_(D),{time:M.value,"show-labels":!0,"day-label":"天","hour-label":"小时","minute-label":"分钟","second-label":"秒"},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("自定义分隔符:")])),_:1}),i(_(D),{time:M.value,"show-days":!1,separator:" | ","separator-color":"#007aff"},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("包含毫秒:")])),_:1}),i(_(D),{time:6e4,"show-days":!1,"show-hours":!1,"show-milliseconds":!0,precision:100})])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("🎨 样式定制")])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("自定义颜色:")])),_:1}),i(_(D),{time:B.value,"background-color":"#007aff","value-color":"#ffffff","label-color":"#ffffff","border-radius":12,"show-labels":!0},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("大尺寸:")])),_:1}),i(_(D),{time:B.value,"value-size":48,"label-size":28,"background-color":"#4cd964","value-color":"#ffffff","border-radius":16,"show-labels":!0},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("边框样式:")])),_:1}),i(_(D),{time:B.value,"background-color":"transparent","border-color":"#ff3b30","value-color":"#ff3b30","border-radius":8},null,8,["time"])])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("🎯 预设样式")])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("秒杀倒计时:")])),_:1}),i(_(D),{"end-time":T.value,"show-days":!1,"background-color":"#ff3b30","value-color":"#ffffff","border-radius":8,"value-bold":!0},null,8,["end-time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("活动倒计时:")])),_:1}),i(_(D),{"end-time":R.value,"show-labels":!0,"background-color":"#007aff","value-color":"#ffffff","label-color":"#ffffff","border-radius":12},null,8,["end-time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("考试倒计时:")])),_:1}),i(_(D),{time:F.value,"show-days":!1,"background-color":"#ff9500","value-color":"#ffffff","border-radius":6,"value-size":36,"value-bold":!0},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("验证码倒计时:")])),_:1}),i(v,{class:"verify-code-demo"},{default:c((()=>[i(y,{placeholder:"请输入手机号",class:"phone-input"}),i(t,{onClick:ee,disabled:j.value,class:r(["verify-btn",{disabled:j.value}])},{default:c((()=>[j.value?(u(),d(_(D),{key:0,ref_key:"verifyCodeRef",ref:I,time:H.value,"show-days":!1,"show-hours":!1,"show-minutes":!1,"show-seconds":!0,"show-separator":!1,"background-color":"transparent","value-color":"#999999","auto-start":!1,onFinish:le},null,8,["time"])):(u(),d(a,{key:1},{default:c((()=>[f("获取验证码")])),_:1}))])),_:1},8,["disabled","class"])])),_:1})])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("⏱️ 精确倒计时")])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("毫秒级倒计时:")])),_:1}),i(_(D),{time:N.value,"show-days":!1,"show-hours":!1,"show-milliseconds":!0,precision:10,"background-color":"#4cd964","value-color":"#ffffff","border-radius":4},null,8,["time"])])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("运动计时器:")])),_:1}),i(_(D),{ref_key:"sportTimerRef",ref:q,time:$.value,"show-days":!1,"show-hours":!1,"show-milliseconds":!0,precision:100,"auto-start":!1,"background-color":"#ff9500","value-color":"#ffffff","value-size":40,"border-radius":8,onChange:ae},null,8,["time"]),i(v,{class:"control-buttons"},{default:c((()=>[i(t,{onClick:se,class:"control-btn"},{default:c((()=>[f("开始")])),_:1}),i(t,{onClick:te,class:"control-btn secondary"},{default:c((()=>[f("暂停")])),_:1}),i(t,{onClick:oe,class:"control-btn"},{default:c((()=>[f("重置")])),_:1})])),_:1})])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("🛠️ 应用场景")])),_:1}),i(v,{class:"scenario-card"},{default:c((()=>[i(a,{class:"card-title"},{default:c((()=>[f("商品秒杀")])),_:1}),i(v,{class:"product-seckill"},{default:c((()=>[i(v,{class:"product-info"},{default:c((()=>[i(p,{class:"product-image",src:S,mode:"aspectFill"}),i(v,{class:"product-details"},{default:c((()=>[i(a,{class:"product-name"},{default:c((()=>[f("限时秒杀商品")])),_:1}),i(v,{class:"price-info"},{default:c((()=>[i(a,{class:"seckill-price"},{default:c((()=>[f("¥99")])),_:1}),i(a,{class:"original-price"},{default:c((()=>[f("¥299")])),_:1})])),_:1})])),_:1})])),_:1}),i(v,{class:"seckill-countdown"},{default:c((()=>[i(a,{class:"countdown-label"},{default:c((()=>[f("距离结束还有:")])),_:1}),i(_(D),{"end-time":A.value,"show-days":!1,"background-color":"#ff3b30","value-color":"#ffffff","border-radius":6,"value-size":28,onFinish:ue},null,8,["end-time"])])),_:1})])),_:1})])),_:1}),i(v,{class:"scenario-card"},{default:c((()=>[i(a,{class:"card-title"},{default:c((()=>[f("活动预告")])),_:1}),i(v,{class:"activity-preview"},{default:c((()=>[i(v,{class:"activity-banner"},{default:c((()=>[i(a,{class:"activity-title"},{default:c((()=>[f("🎉 双11购物节")])),_:1}),i(a,{class:"activity-desc"},{default:c((()=>[f("全场5折起，错过再等一年！")])),_:1})])),_:1}),i(v,{class:"activity-countdown"},{default:c((()=>[i(a,{class:"countdown-title"},{default:c((()=>[f("活动开始倒计时")])),_:1}),i(_(D),{"end-time":J.value,"show-labels":!0,"background-color":"#ff6b35","value-color":"#ffffff","label-color":"#ffffff","border-radius":10,"value-size":32,"day-label":"天","hour-label":"时","minute-label":"分","second-label":"秒"},null,8,["end-time"])])),_:1})])),_:1})])),_:1}),i(v,{class:"scenario-card"},{default:c((()=>[i(a,{class:"card-title"},{default:c((()=>[f("在线考试")])),_:1}),i(v,{class:"exam-interface"},{default:c((()=>[i(v,{class:"exam-header"},{default:c((()=>[i(a,{class:"exam-title"},{default:c((()=>[f("JavaScript 基础测试")])),_:1}),i(v,{class:"exam-timer"},{default:c((()=>[i(a,{class:"timer-label"},{default:c((()=>[f("剩余时间:")])),_:1}),i(_(D),{time:P.value,"show-days":!1,"background-color":"#ff9500","value-color":"#ffffff","border-radius":6,"value-size":24,onFinish:de},null,8,["time"])])),_:1})])),_:1}),i(v,{class:"exam-content"},{default:c((()=>[i(a,{class:"question-title"},{default:c((()=>[f("第1题: 以下哪个是JavaScript的数据类型？")])),_:1}),i(v,{class:"options"},{default:c((()=>[i(v,{class:"option"},{default:c((()=>[f("A. string")])),_:1}),i(v,{class:"option"},{default:c((()=>[f("B. number")])),_:1}),i(v,{class:"option"},{default:c((()=>[f("C. boolean")])),_:1}),i(v,{class:"option"},{default:c((()=>[f("D. 以上都是")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),i(v,{class:"demo-section"},{default:c((()=>[i(a,{class:"section-title"},{default:c((()=>[f("🎮 控制演示")])),_:1}),i(v,{class:"demo-card"},{default:c((()=>[i(a,{class:"demo-label"},{default:c((()=>[f("手动控制:")])),_:1}),i(_(D),{ref_key:"manualCountdownRef",ref:E,time:W.value,"auto-start":!1,"background-color":"#6c757d","value-color":"#ffffff","border-radius":8,onChange:ce,onStart:re,onPause:ne,onResume:ie,onFinish:fe},null,8,["time"]),i(v,{class:"control-buttons"},{default:c((()=>[i(t,{onClick:me,class:"control-btn"},{default:c((()=>[f("开始")])),_:1}),i(t,{onClick:ve,class:"control-btn secondary"},{default:c((()=>[f("暂停")])),_:1}),i(t,{onClick:be,class:"control-btn"},{default:c((()=>[f("继续")])),_:1}),i(t,{onClick:_e,class:"control-btn danger"},{default:c((()=>[f("停止")])),_:1}),i(t,{onClick:ye,class:"control-btn"},{default:c((()=>[f("重置")])),_:1})])),_:1}),i(a,{class:"demo-info"},{default:c((()=>[f("状态: "+m(G.value),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}}}),[["__scopeId","data-v-d2350889"]]);export{M as default};
