import{y as t,z as e,D as a,H as l,A as s,M as c,o,c as n,w as u,a as d,l as r,j as i,k as f,F as b,r as _,b as m,t as p,x as v,J as g,E as h,N as y,O as C,e as k,i as x,S as w,B as $,s as S,f as W,C as U}from"./index-ChUEiI3E.js";import{n as z,C as B}from"./index.DI3nc_37.js";import"./index.4kChqqAX.js";import{_ as H}from"./_plugin-vue_export-helper.BCo6x5W8.js";const M=H(t({__name:"Tabs",props:{current:{default:0},tabs:{default:()=>[]},height:{default:"auto"},backgroundColor:{default:"#ffffff"},tabHeight:{default:88},tabBackgroundColor:{default:"#ffffff"},tabBorderColor:{default:"#e5e5e5"},tabWidth:{default:"auto"},tabMinWidth:{default:120},tabPadding:{default:"0 20rpx"},tabMargin:{default:"0"},fontSize:{default:28},color:{default:"#666666"},activeColor:{default:"#007aff"},disabledColor:{default:"#cccccc"},showIndicator:{type:Boolean,default:!0},indicatorColor:{default:"#007aff"},indicatorHeight:{default:4},indicatorWidth:{default:"auto"},indicatorRadius:{default:2},badgeColor:{default:"#ff3b30"},badgeTextColor:{default:"#ffffff"},badgeSize:{default:32},scrollable:{type:Boolean,default:!0},closable:{type:Boolean,default:!1},swipeable:{type:Boolean,default:!1},animated:{type:Boolean,default:!0},position:{default:"top"},align:{default:"left"},customClass:{default:""},customStyle:{default:()=>({})}},emits:["update:current","change","click","close","scroll"],setup(t,{emit:$}){const S=t,W=$,U=e(0);e([]);const z=e(0),B=e(0),H=a((()=>S.tabs)),M=a((()=>H.value[S.current]||null)),j=a((()=>["tabs-container-base",`tabs-position-${S.position}`,{"tabs-animated":S.animated,"tabs-swipeable":S.swipeable},S.customClass])),I=a((()=>({height:"number"==typeof S.height?`${S.height}rpx`:S.height,backgroundColor:S.backgroundColor,...S.customStyle}))),N=a((()=>["tabs-header-base",{"tabs-scrollable":S.scrollable}])),R=a((()=>({height:"number"==typeof S.tabHeight?`${S.tabHeight}rpx`:S.tabHeight,backgroundColor:S.tabBackgroundColor,borderBottomColor:S.tabBorderColor}))),T=a((()=>({justifyContent:"center"===S.align?"center":"right"===S.align?"flex-end":"flex-start"}))),A=a((()=>["tabs-content-base"])),P=a((()=>({height:"auto"===S.height?"auto":`calc(${"number"==typeof S.height?`${S.height}rpx`:S.height} - ${"number"==typeof S.tabHeight?`${S.tabHeight}rpx`:S.tabHeight})`}))),q=a((()=>["tab-indicator-base",{"indicator-animated":S.animated}])),D=a((()=>({backgroundColor:S.indicatorColor,height:"number"==typeof S.indicatorHeight?`${S.indicatorHeight}rpx`:S.indicatorHeight,width:`${B.value}rpx`,left:`${z.value}rpx`,borderRadius:"number"==typeof S.indicatorRadius?`${S.indicatorRadius}rpx`:S.indicatorRadius}))),E=(t,e)=>["tab-item-base",{"tab-active":e===S.current,"tab-disabled":t.disabled,"tab-closable":t.closable&&S.closable}],F=(t,e)=>{const a=e===S.current,l=t.disabled;return{width:"number"==typeof S.tabWidth?`${S.tabWidth}rpx`:S.tabWidth,minWidth:"number"==typeof S.tabMinWidth?`${S.tabMinWidth}rpx`:S.tabMinWidth,padding:S.tabPadding,margin:S.tabMargin,color:l?S.disabledColor:a?S.activeColor:S.color}},J=(t,e)=>{const a=e===S.current;return{color:t.disabled?S.disabledColor:a?S.activeColor:S.color}},O=(t,e)=>{const a=e===S.current,l=t.disabled;return{fontSize:"number"==typeof S.fontSize?`${S.fontSize}rpx`:S.fontSize,color:l?S.disabledColor:a?S.activeColor:S.color,fontWeight:a?"bold":"normal"}},V=t=>["tab-badge-base",{"badge-dot":t.dot,"badge-text":t.badge&&!t.dot}],G=t=>{const e={backgroundColor:S.badgeColor,color:S.badgeTextColor};if(t.dot){const t=Math.max(16,.5*Number(S.badgeSize));e.width=`${t}rpx`,e.height=`${t}rpx`}else e.fontSize=.6*Number(S.badgeSize)+"rpx",e.minWidth=`${S.badgeSize}rpx`,e.height=`${S.badgeSize}rpx`;return e},K=(t,e)=>{const a=e===S.current;return{color:t.disabled?S.disabledColor:a?S.activeColor:S.color}},L=(t,e)=>["tab-pane-base",{"pane-active":e===S.current}],Q=()=>{S.showIndicator&&c((()=>{const t=Number(S.tabMinWidth)||120;z.value=S.current*t+20,B.value="auto"===S.indicatorWidth?t-40:Number(S.indicatorWidth)}))},X=()=>{if(!S.scrollable)return;const t=Number(S.tabMinWidth)||120,e=Math.max(0,S.current*t-375+t/2);U.value=e,W("scroll",e)};return l((()=>S.current),(()=>{c((()=>{Q(),X()}))}),{immediate:!0}),s((()=>{c((()=>{Q()}))})),(t,e)=>{const a=k,l=x,s=w;return o(),n(l,{class:r(["tabs-container",j.value]),style:i(I.value)},{default:u((()=>[d(s,{"scroll-x":"",class:r(["tabs-header",N.value]),style:i(R.value),"scroll-left":U.value,"scroll-with-animation":""},{default:u((()=>[d(l,{class:"tabs-nav",style:i(T.value)},{default:u((()=>[(o(!0),f(b,null,_(H.value,((e,s)=>(o(),n(l,{key:e.key||s,class:r(["tab-item",E(e,s)]),style:i(F(e,s)),onClick:t=>((t,e)=>{t.disabled||(W("update:current",e),W("change",e,t),W("click",t,e),c((()=>{Q(),X()})))})(e,s)},{default:u((()=>[e.icon?(o(),n(l,{key:0,class:"tab-icon",style:i(J(e,s))},{default:u((()=>[d(a,{class:"icon-text"},{default:u((()=>[m(p(e.icon),1)])),_:2},1024)])),_:2},1032,["style"])):v("",!0),d(a,{class:"tab-title",style:i(O(e,s))},{default:u((()=>[m(p(e.title),1)])),_:2},1032,["style"]),e.badge||e.dot?(o(),n(l,{key:1,class:r(["tab-badge",V(e)]),style:i(G(e))},{default:u((()=>[e.badge&&!e.dot?(o(),n(a,{key:0,class:"badge-text"},{default:u((()=>{return[m(p((t=e.badge,"number"==typeof t&&t>99?"99+":String(t))),1)];var t})),_:2},1024)):v("",!0)])),_:2},1032,["class","style"])):v("",!0),e.closable&&t.closable?(o(),n(l,{key:2,class:"tab-close",style:i(K(e,s)),onClick:g((t=>((t,e)=>{W("close",t,e)})(e,s)),["stop"])},{default:u((()=>[d(a,{class:"close-icon"},{default:u((()=>[m("✕")])),_:1})])),_:2},1032,["style","onClick"])):v("",!0)])),_:2},1032,["class","style","onClick"])))),128)),t.showIndicator?(o(),n(l,{key:0,class:r(["tab-indicator",q.value]),style:i(D.value)},null,8,["class","style"])):v("",!0)])),_:1},8,["style"])])),_:1},8,["class","style","scroll-left"]),d(l,{class:r(["tabs-content",A.value]),style:i(P.value)},{default:u((()=>[h(t.$slots,"default",{current:t.current,tab:M.value},(()=>[(o(!0),f(b,null,_(H.value,((e,s)=>y((o(),n(l,{key:e.key||s,class:r(["tab-pane",L(0,s)]),style:i({})},{default:u((()=>[h(t.$slots,`pane-${s}`,{tab:e,index:s},(()=>[d(l,{class:"default-content"},{default:u((()=>[d(a,null,{default:u((()=>[m(p(e.content||`标签页 ${s+1} 的内容`),1)])),_:2},1024)])),_:2},1024)]),!0)])),_:2},1032,["class","style"])),[[C,s===t.current]]))),128))]),!0)])),_:3},8,["class","style"])])),_:3},8,["class","style"])}}}),[["__scopeId","data-v-179408b0"]]),j=H(t({__name:"tabs-demo",setup(t){const a=e(0),l=e(0),c=e([{title:"首页",content:"首页内容"},{title:"分类",content:"分类内容"},{title:"购物车",content:"购物车内容"},{title:"我的",content:"我的内容"}]),r=e(0),f=e([{title:"首页",icon:"🏠",content:"首页内容"},{title:"分类",icon:"📂",content:"分类内容"},{title:"购物车",icon:"🛒",content:"购物车内容"},{title:"我的",icon:"👤",content:"我的内容"}]),b=e(0),_=e([{title:"消息",badge:5,content:"消息内容"},{title:"通知",dot:!0,content:"通知内容"},{title:"任务",badge:99,content:"任务内容"},{title:"设置",content:"设置内容"}]),v=e(0),g=e([{title:"红色主题",content:"红色主题内容"},{title:"标签二",content:"标签二内容"},{title:"标签三",content:"标签三内容"}]),h=e(0),y=e([{title:"大标签一",content:"大标签一内容"},{title:"大标签二",content:"大标签二内容"},{title:"大标签三",content:"大标签三内容"}]),C=e(0),H=e([{title:"圆角一",content:"圆角一内容"},{title:"圆角二",content:"圆角二内容"},{title:"圆角三",content:"圆角三内容"}]),j=e(0),I=e([{title:"标签页一",content:"内容一"},{title:"标签页二",content:"内容二"},{title:"标签页三",content:"内容三"},{title:"标签页四",content:"内容四"},{title:"标签页五",content:"内容五"},{title:"标签页六",content:"内容六"},{title:"标签页七",content:"内容七"},{title:"标签页八",content:"内容八"}]),N=e(0),R=e([{title:"标签一",content:"内容一"},{title:"标签二",content:"内容二"},{title:"标签三",content:"内容三"}]),T=e(0),A=e([{title:"标签1",content:"内容1",closable:!0,key:"tab1"},{title:"标签2",content:"内容2",closable:!0,key:"tab2"},{title:"标签3",content:"内容3",closable:!0,key:"tab3"},{title:"标签4",content:"内容4",closable:!0,key:"tab4"}]),P=e(0),q=e([{title:"可用标签",content:"可用内容"},{title:"禁用标签",content:"禁用内容",disabled:!0},{title:"另一个可用",content:"另一个内容"},{title:"也是禁用",content:"禁用内容",disabled:!0}]),D=e(0),E=e([{title:"首页",content:"首页内容区域"},{title:"发现",content:"发现内容区域"},{title:"消息",content:"消息内容区域"},{title:"我的",content:"我的内容区域"}]),F=e(0),J=e([{title:"基本信息",content:"基本信息内容"},{title:"安全设置",content:"安全设置内容"},{title:"隐私设置",content:"隐私设置内容"},{title:"通知设置",content:"通知设置内容"}]),O=e(0),V=e([{title:"首页",icon:"🏠"},{title:"数据",icon:"📊"},{title:"设置",icon:"⚙️"}]),G=e(0),K=e([{title:"商品详情"},{title:"规格参数"},{title:"用户评价",badge:128}]),L=e(0),Q=e([{title:"个人信息"},{title:"我的订单",badge:20},{title:"账户设置"}]);s((()=>{a.value=z.getTotalHeight()}));const X=(t,e)=>{console.log("基础标签切换:",t,e)},Y=(t,e)=>{console.log("图标标签切换:",t,e)},Z=(t,e)=>{console.log("徽章标签切换:",t,e)},tt=(t,e)=>{console.log("标签点击:",t,e)},et=t=>{console.log("标签滚动:",t)},at=(t,e)=>{console.log("关闭标签:",t,e),A.value.splice(e,1),T.value>=A.value.length&&(T.value=Math.max(0,A.value.length-1)),S({title:`已关闭 ${t.title}`,icon:"none"})},lt=()=>{const t=A.value.length+1,e={title:`新标签${t}`,content:`新标签${t}的内容`,closable:!0,key:`new-tab-${t}`};A.value.push(e),T.value=A.value.length-1,S({title:"已添加新标签",icon:"success"})},st=()=>{A.value=[{title:"标签1",content:"内容1",closable:!0,key:"tab1"},{title:"标签2",content:"内容2",closable:!0,key:"tab2"},{title:"标签3",content:"内容3",closable:!0,key:"tab3"},{title:"标签4",content:"内容4",closable:!0,key:"tab4"}],T.value=0,S({title:"已重置标签",icon:"success"})};return(t,e)=>{const s=k,S=x,z=W,ct=U,ot=w;return o(),n(S,{class:"page"},{default:u((()=>[d($(B),{title:"标签页组件演示","show-back":!0}),d(ot,{"scroll-y":"",class:"content",style:i({paddingTop:a.value+"px"})},{default:u((()=>[d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("📝 基础用法")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("默认标签页:")])),_:1}),d($(M),{current:l.value,"onUpdate:current":e[0]||(e[0]=t=>l.value=t),tabs:c.value,onChange:X,onClick:tt},null,8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("🎨 图标和徽章")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("图标标签页:")])),_:1}),d($(M),{current:r.value,"onUpdate:current":e[1]||(e[1]=t=>r.value=t),tabs:f.value,onChange:Y},null,8,["current","tabs"])])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("徽章标签页:")])),_:1}),d($(M),{current:b.value,"onUpdate:current":e[2]||(e[2]=t=>b.value=t),tabs:_.value,onChange:Z},null,8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("🎨 样式定制")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("自定义颜色:")])),_:1}),d($(M),{current:v.value,"onUpdate:current":e[3]||(e[3]=t=>v.value=t),tabs:g.value,"active-color":"#ff3b30","indicator-color":"#ff3b30","tab-background-color":"#fff5f5"},null,8,["current","tabs"])])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("大尺寸标签:")])),_:1}),d($(M),{current:h.value,"onUpdate:current":e[4]||(e[4]=t=>h.value=t),tabs:y.value,"tab-height":120,"font-size":32,"indicator-height":6},null,8,["current","tabs"])])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("圆角指示器:")])),_:1}),d($(M),{current:C.value,"onUpdate:current":e[5]||(e[5]=t=>C.value=t),tabs:H.value,"indicator-color":"#4cd964","indicator-height":8,"indicator-radius":4},null,8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("📜 滚动标签")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("可滚动标签页:")])),_:1}),d($(M),{current:j.value,"onUpdate:current":e[6]||(e[6]=t=>j.value=t),tabs:I.value,scrollable:!0,"tab-min-width":160,onScroll:et},null,8,["current","tabs"])])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("固定宽度标签:")])),_:1}),d($(M),{current:N.value,"onUpdate:current":e[7]||(e[7]=t=>N.value=t),tabs:R.value,scrollable:!1,align:"center"},null,8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("❌ 可关闭标签")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("可关闭标签页:")])),_:1}),d($(M),{current:T.value,"onUpdate:current":e[8]||(e[8]=t=>T.value=t),tabs:A.value,closable:!0,onClose:at},null,8,["current","tabs"]),d(S,{class:"control-buttons"},{default:u((()=>[d(z,{onClick:lt,class:"control-btn"},{default:u((()=>[m("添加标签")])),_:1}),d(z,{onClick:st,class:"control-btn secondary"},{default:u((()=>[m("重置标签")])),_:1})])),_:1})])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("🚫 禁用状态")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("部分禁用:")])),_:1}),d($(M),{current:P.value,"onUpdate:current":e[9]||(e[9]=t=>P.value=t),tabs:q.value,"disabled-color":"#cccccc"},null,8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("📍 位置控制")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("底部标签:")])),_:1}),d(S,{class:"position-demo"},{default:u((()=>[d($(M),{current:D.value,"onUpdate:current":e[10]||(e[10]=t=>D.value=t),tabs:E.value,position:"bottom",height:300,"background-color":"#f8f9fa"},{default:u((()=>[d(S,{class:"tab-content"},{default:u((()=>[d(s,null,{default:u((()=>{var t;return[m(p((null==(t=E.value[D.value])?void 0:t.content)||"内容区域"),1)]})),_:1})])),_:1})])),_:1},8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("左侧标签:")])),_:1}),d(S,{class:"position-demo"},{default:u((()=>[d($(M),{current:F.value,"onUpdate:current":e[11]||(e[11]=t=>F.value=t),tabs:J.value,position:"left",height:300,"tab-width":150,"background-color":"#f8f9fa"},{default:u((()=>[d(S,{class:"tab-content"},{default:u((()=>[d(s,null,{default:u((()=>{var t;return[m(p((null==(t=J.value[F.value])?void 0:t.content)||"内容区域"),1)]})),_:1})])),_:1})])),_:1},8,["current","tabs"])])),_:1})])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("🎯 自定义内容")])),_:1}),d(S,{class:"demo-card"},{default:u((()=>[d(s,{class:"demo-label"},{default:u((()=>[m("插槽内容:")])),_:1}),d($(M),{current:O.value,"onUpdate:current":e[12]||(e[12]=t=>O.value=t),tabs:V.value,height:400},{"pane-0":u((()=>[d(S,{class:"custom-pane"},{default:u((()=>[d(s,{class:"pane-title"},{default:u((()=>[m("🏠 首页内容")])),_:1}),d(s,{class:"pane-desc"},{default:u((()=>[m("这是首页的自定义内容")])),_:1}),d(S,{class:"feature-list"},{default:u((()=>[d(S,{class:"feature-item"},{default:u((()=>[m("✅ 功能一")])),_:1}),d(S,{class:"feature-item"},{default:u((()=>[m("✅ 功能二")])),_:1}),d(S,{class:"feature-item"},{default:u((()=>[m("✅ 功能三")])),_:1})])),_:1})])),_:1})])),"pane-1":u((()=>[d(S,{class:"custom-pane"},{default:u((()=>[d(s,{class:"pane-title"},{default:u((()=>[m("📊 数据统计")])),_:1}),d(S,{class:"stats-grid"},{default:u((()=>[d(S,{class:"stat-item"},{default:u((()=>[d(s,{class:"stat-number"},{default:u((()=>[m("1,234")])),_:1}),d(s,{class:"stat-label"},{default:u((()=>[m("用户数")])),_:1})])),_:1}),d(S,{class:"stat-item"},{default:u((()=>[d(s,{class:"stat-number"},{default:u((()=>[m("5,678")])),_:1}),d(s,{class:"stat-label"},{default:u((()=>[m("访问量")])),_:1})])),_:1}),d(S,{class:"stat-item"},{default:u((()=>[d(s,{class:"stat-number"},{default:u((()=>[m("90%")])),_:1}),d(s,{class:"stat-label"},{default:u((()=>[m("满意度")])),_:1})])),_:1})])),_:1})])),_:1})])),"pane-2":u((()=>[d(S,{class:"custom-pane"},{default:u((()=>[d(s,{class:"pane-title"},{default:u((()=>[m("⚙️ 设置选项")])),_:1}),d(S,{class:"settings-list"},{default:u((()=>[d(S,{class:"setting-item"},{default:u((()=>[d(s,{class:"setting-label"},{default:u((()=>[m("推送通知")])),_:1}),d(ct,{checked:"",color:"#007aff"})])),_:1}),d(S,{class:"setting-item"},{default:u((()=>[d(s,{class:"setting-label"},{default:u((()=>[m("自动更新")])),_:1}),d(ct,{color:"#007aff"})])),_:1}),d(S,{class:"setting-item"},{default:u((()=>[d(s,{class:"setting-label"},{default:u((()=>[m("数据同步")])),_:1}),d(ct,{checked:"",color:"#007aff"})])),_:1})])),_:1})])),_:1})])),_:1},8,["current","tabs"])])),_:1})])),_:1}),d(S,{class:"demo-section"},{default:u((()=>[d(s,{class:"section-title"},{default:u((()=>[m("🛠️ 应用场景")])),_:1}),d(S,{class:"scenario-card"},{default:u((()=>[d(s,{class:"card-title"},{default:u((()=>[m("商品详情页")])),_:1}),d($(M),{current:G.value,"onUpdate:current":e[13]||(e[13]=t=>G.value=t),tabs:K.value,height:300,"active-color":"#ff6b35","indicator-color":"#ff6b35"},{"pane-0":u((()=>[d(S,{class:"product-detail"},{default:u((()=>[d(s,{class:"detail-title"},{default:u((()=>[m("商品详情")])),_:1}),d(s,{class:"detail-text"},{default:u((()=>[m("这是一款优质的产品，具有以下特点：")])),_:1}),d(S,{class:"detail-list"},{default:u((()=>[d(s,null,{default:u((()=>[m("• 高品质材料制作")])),_:1}),d(s,null,{default:u((()=>[m("• 精工细作工艺")])),_:1}),d(s,null,{default:u((()=>[m("• 人性化设计")])),_:1}),d(s,null,{default:u((()=>[m("• 性价比超高")])),_:1})])),_:1})])),_:1})])),"pane-1":u((()=>[d(S,{class:"product-params"},{default:u((()=>[d(s,{class:"params-title"},{default:u((()=>[m("规格参数")])),_:1}),d(S,{class:"params-table"},{default:u((()=>[d(S,{class:"param-row"},{default:u((()=>[d(s,{class:"param-label"},{default:u((()=>[m("品牌:")])),_:1}),d(s,{class:"param-value"},{default:u((()=>[m("优质品牌")])),_:1})])),_:1}),d(S,{class:"param-row"},{default:u((()=>[d(s,{class:"param-label"},{default:u((()=>[m("型号:")])),_:1}),d(s,{class:"param-value"},{default:u((()=>[m("ABC-123")])),_:1})])),_:1}),d(S,{class:"param-row"},{default:u((()=>[d(s,{class:"param-label"},{default:u((()=>[m("尺寸:")])),_:1}),d(s,{class:"param-value"},{default:u((()=>[m("20×15×10cm")])),_:1})])),_:1}),d(S,{class:"param-row"},{default:u((()=>[d(s,{class:"param-label"},{default:u((()=>[m("重量:")])),_:1}),d(s,{class:"param-value"},{default:u((()=>[m("500g")])),_:1})])),_:1})])),_:1})])),_:1})])),"pane-2":u((()=>[d(S,{class:"product-reviews"},{default:u((()=>[d(s,{class:"reviews-title"},{default:u((()=>[m("用户评价")])),_:1}),d(S,{class:"review-item"},{default:u((()=>[d(S,{class:"review-header"},{default:u((()=>[d(s,{class:"reviewer-name"},{default:u((()=>[m("用户A")])),_:1}),d(s,{class:"review-rating"},{default:u((()=>[m("⭐⭐⭐⭐⭐")])),_:1})])),_:1}),d(s,{class:"review-content"},{default:u((()=>[m("质量很好，值得购买！")])),_:1})])),_:1}),d(S,{class:"review-item"},{default:u((()=>[d(S,{class:"review-header"},{default:u((()=>[d(s,{class:"reviewer-name"},{default:u((()=>[m("用户B")])),_:1}),d(s,{class:"review-rating"},{default:u((()=>[m("⭐⭐⭐⭐")])),_:1})])),_:1}),d(s,{class:"review-content"},{default:u((()=>[m("性价比不错，推荐。")])),_:1})])),_:1})])),_:1})])),_:1},8,["current","tabs"])])),_:1}),d(S,{class:"scenario-card"},{default:u((()=>[d(s,{class:"card-title"},{default:u((()=>[m("用户中心")])),_:1}),d($(M),{current:L.value,"onUpdate:current":e[14]||(e[14]=t=>L.value=t),tabs:Q.value,height:250,"active-color":"#4cd964","indicator-color":"#4cd964"},{"pane-0":u((()=>[d(S,{class:"user-info"},{default:u((()=>[d(S,{class:"user-avatar"},{default:u((()=>[m("👤")])),_:1}),d(s,{class:"user-name"},{default:u((()=>[m("张三")])),_:1}),d(s,{class:"user-level"},{default:u((()=>[m("VIP会员")])),_:1})])),_:1})])),"pane-1":u((()=>[d(S,{class:"user-orders"},{default:u((()=>[d(S,{class:"order-stats"},{default:u((()=>[d(S,{class:"stat-item"},{default:u((()=>[d(s,{class:"stat-number"},{default:u((()=>[m("12")])),_:1}),d(s,{class:"stat-label"},{default:u((()=>[m("待付款")])),_:1})])),_:1}),d(S,{class:"stat-item"},{default:u((()=>[d(s,{class:"stat-number"},{default:u((()=>[m("5")])),_:1}),d(s,{class:"stat-label"},{default:u((()=>[m("待发货")])),_:1})])),_:1}),d(S,{class:"stat-item"},{default:u((()=>[d(s,{class:"stat-number"},{default:u((()=>[m("3")])),_:1}),d(s,{class:"stat-label"},{default:u((()=>[m("待收货")])),_:1})])),_:1})])),_:1})])),_:1})])),"pane-2":u((()=>[d(S,{class:"user-settings"},{default:u((()=>[d(S,{class:"setting-item"},{default:u((()=>[d(s,null,{default:u((()=>[m("账户安全")])),_:1}),d(s,{class:"setting-arrow"},{default:u((()=>[m(">")])),_:1})])),_:1}),d(S,{class:"setting-item"},{default:u((()=>[d(s,null,{default:u((()=>[m("隐私设置")])),_:1}),d(s,{class:"setting-arrow"},{default:u((()=>[m(">")])),_:1})])),_:1}),d(S,{class:"setting-item"},{default:u((()=>[d(s,null,{default:u((()=>[m("消息通知")])),_:1}),d(s,{class:"setting-arrow"},{default:u((()=>[m(">")])),_:1})])),_:1})])),_:1})])),_:1},8,["current","tabs"])])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})}}}),[["__scopeId","data-v-68beeb4d"]]);export{j as default};
