# VerificationCodeButton 验证码按钮组件

一个功能完整、高度可定制的验证码按钮组件，支持倒计时、加载状态、样式定制等功能。

## ✨ 功能特性

- 📱 **获取验证码** - 点击按钮触发验证码获取逻辑
- ⏰ **倒计时功能** - 可配置的倒计时，防止频繁点击
- 🔄 **加载状态** - 支持异步操作的加载状态显示
- 🎨 **样式定制** - 丰富的样式配置选项和预设主题
- 📱 **响应式设计** - 完美适配移动端和桌面端
- 🛡️ **TypeScript 支持** - 完整的类型定义和智能提示
- 🎮 **程序化控制** - 支持手动控制倒计时和加载状态
- 📡 **丰富事件** - 完整的事件回调支持

## 📦 安装使用

```typescript
// 1. 导入组件
import { VerificationCodeButton } from '@/components/VerificationCodeButton'

// 2. 使用组件
<VerificationCodeButton
  @click="handleGetCode"
/>
```

## 🎯 基础用法

### 简单示例

```vue
<template>
  <view class="form-row">
    <input v-model="phone" placeholder="请输入手机号" />
    <VerificationCodeButton
      ref="codeButtonRef"
      @click="handleGetCode"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VerificationCodeButton } from '@/components/VerificationCodeButton'

const phone = ref('')
const codeButtonRef = ref()

const handleGetCode = async () => {
  if (!phone.value) {
    uni.showToast({ title: '请输入手机号', icon: 'none' })
    return
  }
  
  try {
    // 设置加载状态
    codeButtonRef.value?.setLoading(true)
    
    // 调用API发送验证码
    await sendVerificationCode(phone.value)
    
    // 开始倒计时
    codeButtonRef.value?.startCountdown()
    
    uni.showToast({ title: '验证码已发送', icon: 'success' })
  } catch (error) {
    uni.showToast({ title: '发送失败', icon: 'error' })
  } finally {
    codeButtonRef.value?.setLoading(false)
  }
}
</script>
```

## 📋 API 文档

### Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| disabled | `boolean` | `false` | 是否禁用按钮 |
| loading | `boolean` | `false` | 是否显示加载状态 |
| countdown | `number` | `60` | 倒计时时长（秒） |
| autoStart | `boolean` | `false` | 点击后是否自动开始倒计时 |
| text | `string` | `'获取验证码'` | 按钮文本 |
| loadingText | `string` | `'发送中...'` | 加载状态文本 |
| countdownFormat | `string` | `'{time}s后重新获取'` | 倒计时文本格式 |
| width | `number \| string` | `'auto'` | 按钮宽度 |
| height | `number \| string` | `36` | 按钮高度 |
| backgroundColor | `string` | `'#007aff'` | 背景颜色 |
| disabledBackgroundColor | `string` | `'#c8c9cc'` | 禁用状态背景颜色 |
| textColor | `string` | `'#ffffff'` | 文字颜色 |
| disabledTextColor | `string` | `'#969799'` | 禁用状态文字颜色 |
| fontSize | `number` | `14` | 字体大小 |
| borderRadius | `number` | `4` | 圆角大小 |
| borderWidth | `number` | `0` | 边框宽度 |
| borderColor | `string` | `'transparent'` | 边框颜色 |
| borderStyle | `string` | `'solid'` | 边框样式 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | `()` | 点击按钮时触发 |
| countdownStart | `(countdown: number)` | 倒计时开始时触发 |
| countdownTick | `(remaining: number)` | 倒计时每秒触发 |
| countdownEnd | `()` | 倒计时结束时触发 |

### 组件方法

通过 ref 可以调用以下方法：

```typescript
// 开始倒计时
codeButtonRef.value?.startCountdown()

// 停止倒计时
codeButtonRef.value?.stopCountdown()

// 设置加载状态
codeButtonRef.value?.setLoading(true)

// 获取组件状态
const state = codeButtonRef.value?.getState()
```

## 🎨 样式定制

### 使用预设主题

```vue
<template>
  <!-- 成功主题 -->
  <VerificationCodeButton v-bind="successTheme" />
  
  <!-- 警告主题 -->
  <VerificationCodeButton v-bind="warningTheme" />
  
  <!-- 朴素主题 -->
  <VerificationCodeButton v-bind="plainTheme" />
</template>

<script setup lang="ts">
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const successTheme = verificationCodeButtonUtils.createPreset('success')
const warningTheme = verificationCodeButtonUtils.createPreset('warning')
const plainTheme = verificationCodeButtonUtils.createPreset('plain')
</script>
```

### 自定义样式

```vue
<VerificationCodeButton
  width="120px"
  height="40px"
  background-color="#ff6b6b"
  text-color="#ffffff"
  border-radius="20px"
  font-size="16"
/>
```

## 🔧 高级用法

### 自动开始倒计时

```vue
<VerificationCodeButton
  :auto-start="true"
  @click="handleGetCode"
/>
```

### 自定义倒计时

```vue
<VerificationCodeButton
  :countdown="120"
  countdown-format="重新发送({time}s)"
  @click="handleGetCode"
/>
```

### 异步操作处理

```vue
<template>
  <VerificationCodeButton
    ref="codeButtonRef"
    :loading="isLoading"
    loading-text="发送中..."
    @click="handleAsyncGetCode"
  />
</template>

<script setup lang="ts">
const isLoading = ref(false)
const codeButtonRef = ref()

const handleAsyncGetCode = async () => {
  isLoading.value = true
  
  try {
    await sendCode()
    codeButtonRef.value?.startCountdown()
  } catch (error) {
    // 处理错误
  } finally {
    isLoading.value = false
  }
}
</script>
```

### 事件监听

```vue
<VerificationCodeButton
  @click="handleClick"
  @countdown-start="handleCountdownStart"
  @countdown-tick="handleCountdownTick"
  @countdown-end="handleCountdownEnd"
/>

<script setup lang="ts">
const handleClick = () => {
  console.log('按钮被点击')
}

const handleCountdownStart = (countdown: number) => {
  console.log(`倒计时开始: ${countdown}秒`)
}

const handleCountdownTick = (remaining: number) => {
  console.log(`剩余时间: ${remaining}秒`)
}

const handleCountdownEnd = () => {
  console.log('倒计时结束')
}
</script>
```

## 🛠️ 工具函数

### 手机号验证

```typescript
import { verificationCodeButtonUtils } from '@/components/VerificationCodeButton'

const isValidPhone = verificationCodeButtonUtils.validatePhone('13800138000')
// 返回: true
```

### 邮箱验证

```typescript
const isValidEmail = verificationCodeButtonUtils.validateEmail('<EMAIL>')
// 返回: true
```

### 创建异步处理函数

```typescript
const asyncHandler = verificationCodeButtonUtils.createAsyncHandler(
  () => sendVerificationCode(phone.value),
  {
    onStart: () => console.log('开始发送'),
    onSuccess: (result) => console.log('发送成功', result),
    onError: (error) => console.log('发送失败', error),
    onFinally: () => console.log('发送完成')
  }
)

// 使用
const handleGetCode = () => asyncHandler(codeButtonRef.value)
```

## 📱 使用场景

### 登录场景

```vue
<VerificationCodeButton
  v-bind="loginPreset"
  @click="handleLoginCode"
/>

<script setup lang="ts">
const loginPreset = verificationCodeButtonUtils.createPreset('default', 'default', 'login')
</script>
```

### 注册场景

```vue
<VerificationCodeButton
  v-bind="registerPreset"
  @click="handleRegisterCode"
/>

<script setup lang="ts">
const registerPreset = verificationCodeButtonUtils.createPreset('success', 'default', 'register')
</script>
```

### 找回密码场景

```vue
<VerificationCodeButton
  v-bind="resetPasswordPreset"
  @click="handleResetPasswordCode"
/>

<script setup lang="ts">
const resetPasswordPreset = verificationCodeButtonUtils.createPreset('warning', 'default', 'resetPassword')
</script>
```

## 📊 预设配置

### 主题预设

- `default` - 默认蓝色主题
- `success` - 成功绿色主题
- `warning` - 警告橙色主题
- `danger` - 危险红色主题
- `plain` - 朴素边框主题
- `dark` - 暗色主题

### 尺寸预设

- `small` - 小尺寸 (28px)
- `default` - 默认尺寸 (36px)
- `large` - 大尺寸 (44px)
- `xlarge` - 超大尺寸 (52px)

### 场景预设

- `login` - 登录场景
- `register` - 注册场景
- `resetPassword` - 找回密码场景
- `bindPhone` - 绑定手机场景
- `changePassword` - 修改密码场景

## 🔄 状态管理

组件内部维护以下状态：

- `isCounting` - 是否正在倒计时
- `isLoading` - 是否正在加载
- `remainingTime` - 剩余倒计时时间
- `isDisabled` - 是否禁用

可以通过 `getState()` 方法获取当前状态。

## 📄 许可证

MIT License
